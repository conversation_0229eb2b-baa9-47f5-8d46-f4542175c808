---
description: 'Comprehensive guide for AI coding assistants working with MediTrack API'
applyTo: '**/*.cs, **/*.csproj, **/*.json, **/specs/*.md'
---

# MediTrack API - AI Coding Assistant Instructions

## Architecture Overview

MediTrack is a **multi-tenant healthcare kiosk API** built with Clean Architecture and CQRS patterns. The system serves hospital kiosks and mobile apps with dynamic HIS (Hospital Information System) integration.

### Solution Structure
```
GoTRUST.MediTrack.API.sln
├── src/
│   ├── core/                           # Business logic layer
│   │   ├── MediTrack.Domain/          # Entities, value objects, interfaces
│   │   └── MediTrack.Application/     # CQRS handlers, DTOs, services
│   ├── infras/                        # Infrastructure layer
│   │   ├── MediTrack.Persistence/     # EF Core, repositories
│   │   ├── MediTrack.HisIntegration/  # HIS service implementations
│   │   └── MediTrack.External/        # Third-party integrations
│   ├── pre/                           # Presentation layer
│   │   └── MediTrack.Apis/           # Minimal API endpoints
│   └── lib/                          # HIS client libraries
│       ├── MediTrack.His.Client.V1/  # Hospital system v1 client
│       ├── MediTrack.His.Client.V2/  # Hospital system v2 client
│       └── ... (v3-v24)              # Supporting 24+ HIS versions
```

## Critical Code Patterns

### 1. CQRS Handler Pattern
Every operation MUST follow this exact structure:
```csharp
public record Query(string ExameTypeId, string? ClinicId) : IRequest<BaseCommandResultWithData<List<HealthService>>>;

public class Handler : IRequestHandler<Query, BaseCommandResultWithData<List<HealthService>>>
{
    private readonly ICurrentHospitalService current;
    private readonly ICachedService cachedService;
    
    public async Task<BaseCommandResultWithData<List<HealthService>>> Handle(Query request, CancellationToken cancellationToken)
    {
        var result = new BaseCommandResultWithData<List<HealthService>>();
        
        // 1. Always check cache first with hospital-specific key
        var cacheKey = $"HealthServices{current.CurrentHospital.HospitalId}{request.ExameTypeId}{request.ClinicId}";
        var cached = await cachedService.GetAsync<List<HealthService>>(cacheKey);
        if (cached != null) return result.Set(true, "Success", cached);
        
        // 2. Use dynamic HIS service
        var (success, message, error, data) = await current.HisService.GetHealthServices(...);
        
        // 3. Handle errors with ErrorTypeEnum
        if (!success) return result.Set(false, message, errorType: error);
        
        // 4. Cache successful results
        await cachedService.SetAsync(cacheKey, data, TimeSpan.FromMinutes(30));
        
        return result.Set(true, "Success", data);
    }
}
```

### 2. Minimal API Endpoint Pattern
All endpoints inherit from `BaseEndpoint`:
```csharp
public class HealthServiceEndpoint : BaseEndpoint
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this.GetBaseUrl())  // Uses /api/{controller-name}
           .RequireRateLimiting("fixed")
           .WithTags("Health Services")
           .MapGet(GetHealthServices, "")
           .MapGet(GetDefaultHealthServices, "default/{exameTypeId}");
    }
    
    private static async Task<Results<Ok<BaseCommandResultWithData<List<HealthService>>>, BadRequest<BaseCommandResult>>> 
        GetHealthServices([AsParameters] GetHealthServices.Query query, ISender sender)
    {
        var result = await sender.Send(query);
        return result.Result ? TypedResults.Ok(result) : TypedResults.BadRequest(result as BaseCommandResult);
    }
}
```

### 3. Multi-Tenant Resolution
The system resolves tenants via headers:
- **Kiosk**: `x-kiosk-id` header → resolves hospital from kiosk registration
- **Mobile**: `x-hospital-id` + `x-device-id: mobile` headers
- **Tracing**: `x-trace-id` for request correlation

Access current context via `ICurrentHospitalService`:
```csharp
// Available in every handler/service
current.CurrentHospital     // Hospital configuration
current.HisService         // Dynamic HIS client (v1-v15)
current.KioskId           // Current kiosk ID
current.LogPrefix         // Structured log prefix [TraceId-HospitalId-KioskId]
```

### 4. Dynamic HIS Integration
Each hospital uses a specific HIS version (v1-v15). The correct client is resolved automatically:
```csharp
// DON'T: Manual HIS client creation
// DO: Use injected HisService
var (success, message, error, data) = await current.HisService.GetPatientInfo(patientCode);

// HIS methods always return tuple: (bool success, string message, ErrorTypeEnum error, TData data)
```

### 5. Metadata Configuration Pattern
Hospital/kiosk-specific configurations stored in metadata tables:
```csharp
// Hospital metadata
var config = await metadataService.GetHospitalMetadata<HealthServiceConfig>(
    hospitalId, 
    "HEALTH_SERVICE_CONFIG"
);

// Kiosk metadata with hierarchical keys
var kioskConfig = await metadataService.GetKioskMetadata<ExameTypeConfig>(
    kioskId,
    $"kiosk_configs:exame_types:{exameTypeId}"
);
```

## Development Workflows

### Local Development
```bash
# Run with hot reload (recommended)
dotnet watch run --project src/pre/MediTrack.Apis/MediTrack.Apis.csproj

# Build solution
dotnet build

# Run tests
dotnet test

# VS Code tasks available: "watch", "build", "test"
```

### Database Operations
```bash
# Add migration
dotnet ef migrations add MigrationName -p src/infras/MediTrack.Persistence -s src/pre/MediTrack.Apis

# Update database
dotnet ef database update -p src/infras/MediTrack.Persistence -s src/pre/MediTrack.Apis

# VS Code task: "Add EF Migration"
```

### Docker Development
```bash
./build_dev.sh                    # Build development image
docker-compose -f docker-compose.dev.yml up  # Run with dependencies
# API: http://localhost:8080
# PostgreSQL: localhost:5432
# Redis: localhost:6379
```

## Key Integration Points

### External Services
- **Agent Gateway**: Patient registration, appointment booking
- **Payment**: MediBank, HD Bank integration
- **Insurance**: Social insurance verification
- **Identity**: VNEID verification (MongoDB storage)

### Storage Systems
- **PostgreSQL**: Primary database (multi-tenant)
- **MongoDB**: VNEID data, Hangfire jobs
- **Redis**: Distributed cache (fallback to in-memory)
- **S3/MinIO**: File storage (medical images, reports)

### Background Jobs
```csharp
// Hangfire with MongoDB storage
BackgroundJob.Enqueue<ISyncService>(x => x.SyncPatientData(hospitalId));
```

## Common Implementation Patterns

### Age/Gender Filtering
Health services often have restrictions:
```csharp
if (!string.IsNullOrEmpty(service.LimitConfig))
{
    var limit = JsonSerializer.Deserialize<LimitConfig>(service.LimitConfig);
    if (limit.MinAge > 0 && patientAge < limit.MinAge) continue;
    if (limit.Gender != null && patientGender != limit.Gender) continue;
}
```

### Cache Invalidation
```csharp
// Remove all cached items for a hospital
await cachedService.RemoveByPrefixAsync($"HealthServices{hospitalId}");
```

### Error Response Pattern
```csharp
return result.Result 
    ? TypedResults.Ok(result) 
    : TypedResults.BadRequest(result as BaseCommandResult);
```

## Anti-Patterns to Avoid

1. **Direct HIS Client Usage**: Never instantiate HIS clients directly - use `current.HisService`
2. **Missing Hospital Context**: All database queries must include hospital context
3. **Hardcoded Cache Keys**: Always include `hospitalId` in cache keys
4. **Synchronous I/O**: Use async/await throughout, pass `CancellationToken`
5. **Missing Logging Context**: Always include `current.LogPrefix` in logs

## Testing Patterns

```csharp
// Unit test handlers with mocked services
var current = new Mock<ICurrentHospitalService>();
current.Setup(x => x.CurrentHospital).Returns(new Hospital { HospitalId = "TEST01" });
current.Setup(x => x.HisService.GetHealthServices(...)).ReturnsAsync((true, "Success", ErrorTypeEnum.None, data));
```

## Debugging Tips

1. **Request Tracing**: Follow `x-trace-id` through logs
2. **Cache Debugging**: Check Redis with `redis-cli KEYS "HealthServices*"`
3. **HIS Version Issues**: Verify `CurrentHospital.HisVersion` matches expected
4. **Multi-tenant Issues**: Check `x-kiosk-id` or `x-hospital-id` headers
5. **Performance**: Enable SQL logging in `appsettings.Development.json`
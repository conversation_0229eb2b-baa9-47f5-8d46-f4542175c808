<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="MediTrack.Persistence">src/infras/MediTrack.Persistence/MediTrack.Persistence.csproj</projectFile>
    <projectFile kind="Docker">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
    <projectFile profileName="http">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
    <projectFile profileName="https">src/pre/MediTrack.Apis/MediTrack.Apis.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ede5b86b-e0f6-4859-9e53-5d469a365aa1" name="Changes" comment="feat: add AdvertisingKioskCampaign to database service">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.MediTrack/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.MediTrack/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pre/MediTrack.Apis/ApiFilter/HospitalClientFilter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/src/pre/MediTrack.Apis/ApiFilter/HospitalClientFilter.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pre/MediTrack.Apis/Program.cs" beforeDir="false" afterPath="$PROJECT_DIR$/src/pre/MediTrack.Apis/Program.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/add-sync-DN-data-api" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;GoTRUST-TrungNQ&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;**************:gotrust-vn/GoTRUST.MediTrack.API.git&quot;,
    &quot;accountId&quot;: &quot;c4b7926a-62d5-4bd0-8c98-c602173cf564&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5334b8902b514154b33dda33685f81a4f9600/31/f25529a8/Ensure.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/28b236b45b2069a1eda3696c06a51f4c6d17c924ff4ce494859ec9d23c707/HangfireServiceCollectionExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/621526284d3e4868b98948aff942206525763e57e194a27623b66c384466/ServiceProviderServiceExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bf9021a960b74107a7e141aa06bc9d8a0a53c929178c2fb95b1597be8af8dc/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/c047fc6ebc23320511c2a9f9f9288a136ed91576d425e55bb725219d305959/IdentityOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/e0b3c333fc82e8939de81c158f7d2c2e3fd8462ec28df46c578ffffbf3e13/ConcurrentDictionary.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fea93ee2ea48db69df97b9196fe77e86f2428c362835f6eda284e2df33bc5db/Ensure.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/pre/MediTrack.Apis/Endpoints/KioskEndpoint.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2qeMHKJVaQrK9myr8TropGaOvz9" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET Launch Settings Profile.MediTrack.Apis: http.executor&quot;: &quot;Debug&quot;,
    &quot;.NET Launch Settings Profile.MediTrack.Apis: https.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;XThreadsFramesViewSplitterKey&quot;: &quot;0.27675277&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET Launch Settings Profile.MediTrack.Apis: http">
    <configuration name="MediTrack.Apis: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/pre/MediTrack.Apis/MediTrack.Apis.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Apis: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/pre/MediTrack.Apis/MediTrack.Apis.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Persistence" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/src/infras/MediTrack.Persistence/MediTrack.Persistence.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="MediTrack.Persistence" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="MediTrack.Apis/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="meditrack.apis" />
          <option name="contextFolderPath" value="$PROJECT_DIR$" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="src/pre/MediTrack.Apis/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="true" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ede5b86b-e0f6-4859-9e53-5d469a365aa1" name="Changes" comment="" />
      <created>1735018498821</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735018498821</updated>
      <workItem from="1735018499865" duration="872000" />
      <workItem from="1745515107289" duration="52000" />
      <workItem from="1745515169697" duration="3184000" />
      <workItem from="1745554355089" duration="17916000" />
      <workItem from="1745669295405" duration="3089000" />
      <workItem from="1745720950140" duration="3728000" />
      <workItem from="1745779824918" duration="617000" />
      <workItem from="1745805668237" duration="239000" />
      <workItem from="1745812559433" duration="434000" />
      <workItem from="1745813675833" duration="545000" />
      <workItem from="1745822315694" duration="2211000" />
      <workItem from="1746432907128" duration="105000" />
      <workItem from="1746433032094" duration="2818000" />
      <workItem from="1746437589972" duration="931000" />
      <workItem from="1746496258432" duration="1005000" />
      <workItem from="1746497267384" duration="1305000" />
      <workItem from="1746518585751" duration="370000" />
      <workItem from="1746580062223" duration="1918000" />
      <workItem from="1746584401670" duration="370000" />
      <workItem from="1746590600392" duration="360000" />
      <workItem from="1747015116260" duration="1700000" />
      <workItem from="1747290587705" duration="2664000" />
      <workItem from="1747539559178" duration="915000" />
      <workItem from="1749277098356" duration="457000" />
      <workItem from="1749651856390" duration="11904000" />
      <workItem from="1749707366092" duration="70000" />
      <workItem from="1750093697147" duration="1134000" />
      <workItem from="1750129549504" duration="22000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
</project>
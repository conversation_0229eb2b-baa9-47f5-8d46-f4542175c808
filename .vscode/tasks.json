{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/pre/MediTrack.Apis/MediTrack.Apis.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/src/pre/MediTrack.Apis/MediTrack.Apis.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/pre/MediTrack.Apis/MediTrack.Apis.csproj"], "problemMatcher": "$msCompile"}, {"label": "Add EF Migration", "type": "shell", "command": "dotnet", "args": ["ef", "migrations", "add", "${input:migrationName}", "-p", "src/infras/MediTrack.Persistence"], "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always"}, "problemMatcher": []}], "inputs": [{"id": "migrationName", "type": "promptString", "description": "Enter the migration name"}]}
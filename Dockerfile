#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
RUN apt-get update -y && apt-get install -y fontconfig openssl  # Cài đặt openssl để tạo chứng chỉ SSL
WORKDIR /app

# Tạo chứng chỉ tự ký SSL
RUN mkdir /https-certs
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /https-certs/aspnetcore.key -out /https-certs/aspnetcore.crt -subj "/CN=localhost"

# Tạo nhóm Diffie-<PERSON>man (DH) mạnh hơn
RUN openssl dhparam -out /https-certs/dhparam.pem 2048

# Cấu hình Kestrel để sử dụng chứng chỉ SSL và nhóm DH
ENV ASPNETCORE_Kestrel__Certificates__Default__Path=/https-certs/aspnetcore.crt
ENV ASPNETCORE_Kestrel__Certificates__Default__KeyPath=/https-certs/aspnetcore.key

# Thêm tham chiếu đến nhóm DH trong cấu hình SSL
RUN echo "ssl_dhparam /https-certs/dhparam.pem;" >> /etc/ssl/openssl.cnf

RUN chmod -R 644 /https-certs/*
USER root
WORKDIR /app
EXPOSE 80


FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/pre/MediTrack.Apis/MediTrack.Apis.csproj", "src/pre/MediTrack.Apis/"]
COPY ["src/core/MediTrack.Application/MediTrack.Application.csproj", "src/core/MediTrack.Application/"]
COPY ["src/core/MediTrack.Domain/MediTrack.Domain.csproj", "src/core/MediTrack.Domain/"]
COPY ["src/core/MediTrack.Ultils/MediTrack.Ultils.csproj", "src/core/MediTrack.Ultils/"]
COPY ["src/infras/MediTrack.Integrated/MediTrack.Integrated.csproj", "src/infras/MediTrack.Integrated/"]
COPY ["src/lib/HisClient.Lib/HisClient.Lib.csproj", "src/lib/HisClient.Lib/"]
COPY ["src/infras/MediTrack.Persistence/MediTrack.Persistence.csproj", "src/infras/MediTrack.Persistence/"]
RUN dotnet restore "./src/pre/MediTrack.Apis/./MediTrack.Apis.csproj"
COPY . .
WORKDIR "/src/src/pre/MediTrack.Apis"
RUN dotnet build "./MediTrack.Apis.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./MediTrack.Apis.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MediTrack.Apis.dll"]

using System;

namespace CAService.Lib.Config
{
    public class CAServiceConfigModel : IDisposable
    {
        public string Url { get; set; } = string.Empty;
        public string EMRUrl { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public int RetryCount { get; set; } = 3;
        public int DelayOnRetry { get; set; } = 1000;
        public int TimeoutInSeconds { get; set; } = 30;

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }
    }
}

using System;

namespace CAService.Lib.Requests
{
    public class LoginRequest
    {
        public string Account { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class CreateCustomerRequest
    {
        public string IdentityNo { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string FaceImgBase64 { get; set; } = string.Empty;
    }

    public class FaceRecognitionRequest
    {
        public string FaceImgBase64 { get; set; } = string.Empty;
    }

    public class CustomerPinRequest
    {
        public string FaceImgBase64 { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
    }

    public class SignPdfRequestRequest
    {
        public string FileName { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
    }

    public class SignPdfRequest
    {
        public Guid SignedPDFId { get; set; }
        public Guid MedicalRecordFileId { get; set; }
        public string KeyBase<PERSON> { get; set; } = string.Empty;
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int PageNumber { get; set; }
    }
}

using System;

namespace CAService.Lib.Models
{
    public class BaseCAResponse<T>
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Errors { get; set; } = string.Empty;
        public string TraceId { get; set; } = string.Empty;
        public T? Data { get; set; }
    }

    public class LoginData
    {
        public bool IsSuccess { get; set; }
        public string Email { get; set; } = string.Empty;
        public Guid? SessionId { get; set; }
        public UserInfo UserInfo { get; set; } = new();
        public bool IsTwoFactorRequired { get; set; }
    }

    public class UserInfo
    {
        public string Username { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public string RoleName { get; set; } = string.Empty;
        public DateTime TokenExpiresAt { get; set; }
    }

    public class CreateCustomerData
    {
        public bool IsSuccess { get; set; }
        public Guid CustomerId { get; set; }
    }

    public class FaceRecognitionData
    {
        public List<CustomerFace> Faces { get; set; } = new();
    }

    public class CustomerFace
    {
        public string CustomerId { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string IdentityNo { get; set; } = string.Empty;
    }

    public class CustomerPinData
    {
        public bool IsSuccess { get; set; }
        public Guid CustomerId { get; set; }
        public string Pin { get; set; } = string.Empty;
    }

    public class SignedPdfData
    {
        public Guid Id { get; set; }
        public Guid CustomerId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string EncryptionKey { get; set; } = string.Empty;
    }

    public class SignPdfRequestData
    {
        public Guid SignedPDFId { get; set; }
    }

    public class SignPdfData
    {
        public bool IsSuccess { get; set; }
        public Guid SignedPDFId { get; set; }
    }
}

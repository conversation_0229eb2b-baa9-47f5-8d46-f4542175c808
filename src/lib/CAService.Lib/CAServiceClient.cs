using CAService.Lib.Models;
using CAService.Lib.Requests;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;

namespace CAService.Lib
{
    public static class CAServiceClient
    {
        public static async Task<(bool, string, LoginData)> Login(this HttpClient client, LoginRequest request, string url)
        {
            bool result = false;
            string message = string.Empty;
            LoginData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();

                string endpoint = $"{url}/authenticates/login";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<LoginData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new LoginData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, CreateCustomerData)> CreateCustomer(this HttpClient client, CreateCustomerRequest request, string url, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            CreateCustomerData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{url}/customers";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<CreateCustomerData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new CreateCustomerData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, FaceRecognitionData)> GetCustomersByFace(this HttpClient client, FaceRecognitionRequest request, string url, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            FaceRecognitionData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{url}/customers/ids-by-face";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<FaceRecognitionData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new FaceRecognitionData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, CustomerPinData)> GetCustomerPin(this HttpClient client, CustomerPinRequest request, string url, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            CustomerPinData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{url}/customers/pin";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<CustomerPinData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new CustomerPinData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, SignedPdfData)> GetSignedPdf(this HttpClient client, Guid id, string url, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            SignedPdfData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{url}/signatures/signed-pdf/{id}";

                HttpResponseMessage response = await client.GetAsync(endpoint);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<SignedPdfData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new SignedPdfData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, SignPdfRequestData)> CreateSignPdfRequest(this HttpClient client, SignPdfRequestRequest request, string url, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            SignPdfRequestData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{url}/signatures/sign-pdf-request";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<SignPdfRequestData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new SignPdfRequestData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, SignPdfData)> SignPdf(this HttpClient client, SignPdfRequest request, string emrUrl, string accessToken)
        {
            bool result = false;
            string message = string.Empty;
            SignPdfData data = new();

            try
            {
                client.DefaultRequestHeaders.Clear();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                string endpoint = $"{emrUrl}/ca-service/sign-pdf";
                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                HttpResponseMessage response = await client.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var responseModel = JsonConvert.DeserializeObject<BaseCAResponse<SignPdfData>>(responseContent);

                    if (responseModel != null)
                    {
                        result = responseModel.Code == "000";
                        message = responseModel.Message;
                        data = responseModel.Data ?? new SignPdfData();
                    }
                    else
                    {
                        message = "Response parse error";
                    }
                }
                else
                {
                    message = await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }

            return (result, message, data);
        }
    }
}

﻿using HisClientV2.Lib.Model;
using HisClientV2.Lib.Request;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HisClientV2.Lib
{
    /// <summary>
    /// H<PERSON> H<PERSON>
    /// </summary>
    public static class HisIntegrateV2Client
    {
        public static async Task<(bool, string, string)> GetAccessToken(
            this HttpClient client, GetTokenRequest request, string url)
        {
            bool result = false;
            string message = string.Empty;
            string token = string.Empty;

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });

            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };

            // G<PERSON><PERSON> yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // X<PERSON> lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<object>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                    token = responseModel.Message.ToString();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }


            return (result, message, token);
        }

        public static async Task<(bool, string, List<ClinicModel>)> GetClinic(
            this HttpClient client, GetClinicRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            List<ClinicModel> responseData = new();
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string getUrl = url;

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Get, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<ClinicModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                    responseData = responseModel.Data ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }


            return (result, message, responseData);
        }

        public static async Task<(bool, string, PatientModel)> CheckPatient(
            this HttpClient client, CreatePatientRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            PatientModel responseData = new();

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<PatientModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                    responseData = responseModel.Data?.FirstOrDefault() ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message, responseData);
        }

        public static async Task<(bool, string, RegisterFormModel)> CreateRegisterForm(
            this HttpClient client, CreateRegisterFormRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            RegisterFormModel responseData = new();

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<RegisterFormModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0 && responseModel.Data != null
                        ;
                    message = responseModel.Message;
                    responseData = responseModel.Data?.FirstOrDefault() ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message, responseData);
        }

        public static async Task<(bool, string, PaymentResultModel)> CheckPaymentStatus(
            this HttpClient client, CheckPaymentRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            PaymentResultModel responseData = new();

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<PaymentResultModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                    responseData = responseModel.Data?.FirstOrDefault() ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message, responseData);
        }

        public static async Task<(bool, string, List<CategoryModel>)> GetCategories(
            this HttpClient client, GetCategoriesRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            List<CategoryModel> responseData = [];

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string getUrl = url;

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Get, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<CategoryModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                    responseData = responseModel.Data ?? [];
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message, responseData);
        }

        public static async Task<(bool, string)> GetQueueNumbers(
            this HttpClient client, CreateQueueRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string getUrl = url;

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<object>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.IsError == 0;
                    message = responseModel.Message;
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message);
        }

        public static async Task<(bool, string, GetHealthInsuranceResponse)> GetHealthInsurance(
            this HttpClient client, GetHealthInsuranceRequest request, string url, string token)
        {
            bool result = false;
            string message = string.Empty;
            GetHealthInsuranceResponse responseData = new();

            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            string getUrl = url;

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
            });
            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };
            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<GetHealthInsuranceResponse>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.MaKetQua == "000"  || responseModel.MaKetQua == "004";
                    message = responseModel.GhiChu;
                    responseData = responseModel ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                token = string.Empty;
            }

            return (result, message, responseData);
        }
    }
}

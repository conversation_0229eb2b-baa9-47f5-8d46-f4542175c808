using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HisClientV2.Lib.Request
{
    public class CreateQueueRequest
    {
        /// <summary>
        /// Nhóm phát số để phát số riêng biệt cho từng khu vực. Mặc định 0.
        /// </summary>
        public int NhomPhatSo { get; set; } = 0;

        /// <summary>
        /// Thiết lập nhóm quầy phát số để phát số riêng biệt cho quầy cùng đầu số. Mặc định 0.
        /// </summary>
        public int NhomQuay { get; set; } = 0;

        /// <summary>
        /// Tên quầy cấp số. VD: “Lấy số thường”
        /// </summary>
        public string TenQuay { get; set; } = string.Empty;

        /// <summary>
        /// Đầu số phát sinh. Mặc định 0
        /// </summary>
        public string DauSo { get; set; } = "0";

        /// <summary>
        /// Định dạng số được phát (số chữ số thứ tự, sẽ bổ sung số '0' để đủ định dạng). Mặc định 4.
        /// </summary>
        public int DinhDangSo { get; set; } = 4;

        /// <summary>
        /// Định dạng số trừ đầu số 0? Mặc định false.
        /// </summary>
        public bool TruDauSo0 { get; set; } = false;

        /// <summary>
        /// Số bắt đầu cấp (số bé nhất cấp ra). Mặc định 0.
        /// </summary>
        public int SoBatDau { get; set; } = 0;

        /// <summary>
        /// Mức độ ưu tiên gọi. Mặc định 0.
        /// </summary>
        public int UuTien { get; set; } = 0;
    }
}
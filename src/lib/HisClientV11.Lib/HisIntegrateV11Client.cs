﻿using HisClientV11.Lib.Helper;
using HisClientV11.Lib.Model;
using HisClientV11.Lib.Request;
using HisClientV11.Lib.Response;
using Newtonsoft.Json;
using Serilog;
using System.Text;

namespace HisClientV11.Lib
{
    /// <summary>
    /// HIS V11 Client - Extension methods for HttpClient following V7 pattern
    /// Luồng chung: VietSen
    /// </summary>
    public static class HisIntegrateV11Client
    {
        public static async Task<(bool, string, List<ExameTypeModel>)> GetExameTypes(this HttpClient client, string url,
            string merchantId, string secretKey)
        {
            bool result = false;
            string message = string.Empty;
            List<ExameTypeModel> data = [];

            string timeStamp = RequestTimeHelper.TimeStamp;
            string plainText = $"{merchantId}|{timeStamp}";

            client.DefaultRequestHeaders.Add("x-sign", SignHelper.SignData(plainText, secretKey));
            client.DefaultRequestHeaders.Add("x-merchant-id", merchantId);
            client.DefaultRequestHeaders.Add("x-timestamp", timeStamp);

            string getUrl = $"{url}";

            // G<PERSON>i yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.GetAsync(getUrl);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<List<ExameTypeModel>>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.Code == "000";
                    message = responseModel.Message;

                    // Xử lý decrypt
                    data = responseModel.Data ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, PatientModel)> GetPatient(this HttpClient client, GetPatientRequest request,
            string url, string merchantId, string secretKey)
        {
            bool result = false;
            string message = string.Empty;
            PatientModel data = new();

            string timeStamp = RequestTimeHelper.TimeStamp;
            string plainText = $"{merchantId}|{timeStamp}|{request.so_gttt}|{request.loai_gttt}";

            client.DefaultRequestHeaders.Add("x-sign", SignHelper.SignData(plainText, secretKey));
            client.DefaultRequestHeaders.Add("x-merchant-id", merchantId);
            client.DefaultRequestHeaders.Add("x-timestamp", timeStamp);

            string getUrl = $"{url}?SO_GTTT={request.so_gttt}&LOAI_GTTT={request.loai_gttt}&DIEN_THOAI={request.dien_thoai}";

            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.GetAsync(getUrl);

            // Xử lý phản hồi
            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<PatientModel>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.Code == "000";
                    message = responseModel.Message;

                    // Xử lý decrypt
                    data = responseModel.Data ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
            }

            return (result, message, data);
        }

        public static async Task<(bool, string, RegisterFormModel)> CreateRegisterForm(this HttpClient client, CreateRegisterFormRequest request,
            string url, string merchantId, string secretKey)
        {
            bool result = false;
            string message = string.Empty;
            RegisterFormModel data = new();

            string timeStamp = RequestTimeHelper.TimeStamp;
            string plainText = $"{merchantId}|{timeStamp}|{request.thong_tin_benh_nhan.ma_bn}";

            client.DefaultRequestHeaders.Add("x-sign", SignHelper.SignData(plainText, secretKey));
            client.DefaultRequestHeaders.Add("x-merchant-id", merchantId);
            client.DefaultRequestHeaders.Add("x-timestamp", timeStamp);

            string jsonData = JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
                ContractResolver = new UppercaseContractResolver()
            });

            // Tạo HttpRequestMessage với phương thức POST và dữ liệu JSON
            HttpRequestMessage requestMessage = new(HttpMethod.Post, url)
            {
                Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            };

            // Gửi yêu cầu và nhận phản hồi
            HttpResponseMessage response = await client.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                var responseModel = JsonConvert.DeserializeObject<CommonResponse<RegisterFormModel>>(responseContent);

                if (responseModel != null)
                {
                    result = responseModel.Code == "000";
                    message = responseModel.Message;
                    data = responseModel.Data ?? new();
                }
                else
                {
                    message = ErrorConstant.ReponseParseError;
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
            }

            return (result, message, data);
        }
    }
}

﻿using HisClientV11.Lib.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HisClientV11.Lib.Request
{
    public class CreateRegisterFormRequest
    {
        public PatientModel thong_tin_benh_nhan { get; set; } = new PatientModel();
        public HealthServiceModel thong_tin_dich_vu { get; set; } = new HealthServiceModel();
        public string id_loai_kham { get; set; } = string.Empty;
        public int bn_uu_tien { get; set; } = 1;
        public string du_phong { get; set; } = string.Empty;
        public string? so_giay_chuyen_tuyen { get; set; }
        public string? ma_benh_chuyen_tuyen { get; set; }
        public string? don_vi_chuyen_tuyen { get; set; }
    }
}

﻿namespace HisClientV11.Lib.Model
{
    public class RegisterFormModel
    {
        public PatientModel thong_tin_benh_nhan { get; set; } = new();
        public AdmitData thong_tin_tiep_nhan { get; set; } = new AdmitData();
        public RegisterData thong_tin_dang_ky { get; set; } = new RegisterData();
        public PaymentData thong_tin_thanh_toan { get; set; } = new PaymentData();
    }
    public class PaymentData
    {
        public string so_phieu { get; set; } = string.Empty;
        public string qr_code { get; set; } = string.Empty;
    }

    public class AdmitData
    {
        public string id_lk { get; set; } = string.Empty;
        public string ma_lk { get; set; } = string.Empty;
        public string ma_vv { get; set; } = string.Empty;
        public string ngay_vao { get; set; } = string.Empty;
        public string ngay_vao_noi_tru { get; set; } = string.Empty;
        public string ngay_ra { get; set; } = string.Empty;
        public int? trang_thai_lk { get; set; }
        public int stt_lk { get; set; }
    }

    public class RegisterData
    {
        public string id_dang_ky { get; set; } = string.Empty;
        public string ma_dang_ky { get; set; } = string.Empty;
        public string ten_dang_ky { get; set; } = string.Empty;
        public string ngay_dang_ky { get; set; } = string.Empty;
        public int? trang_thai_dang_ky { get; set; }
        public decimal don_gia_dang_ky { get; set; }
        public string mo_ta_dang_ky { get; set; } = string.Empty;
        public List<RegisterDetailData>? chi_tiet_dang_ky { get; set; }
    }

    public class RegisterDetailData
    {
        public string id_dang_ky { get; set; } = string.Empty;
        public string id_chi_tiet_dk { get; set; } = string.Empty;
        public string id_phong_kham { get; set; } = string.Empty;
        public string ten_phong_kham { get; set; } = string.Empty;
        public string loai_dich_vu { get; set; } = string.Empty;
        public string don_vi { get; set; } = string.Empty;
        public int so_luong { get; set; }
        public string dvt { get; set; } = string.Empty;
        public decimal don_gia { get; set; }
        public int trang_thai { get; set; }
    }
}

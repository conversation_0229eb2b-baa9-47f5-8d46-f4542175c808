﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HisClientV11.Lib.Model
{
    public class PatientModel
    {
        public string? id_bn { get; set; }
        public string? ma_bn { get; set; }
        public string ho_ten { get; set; } = string.Empty;
        public string ho_bn { get; set; } = string.Empty;
        public string ten_bn { get; set; } = string.Empty;
        public string matinh_cutru { get; set; } = string.Empty;
        public string mahuyen_cu_tru { get; set; } = string.Empty;
        public string maxa_cu_tru { get; set; } = string.Empty;
        public string ma_dantoc { get; set; } = string.Empty;
        public string ma_quoctich { get; set; } = string.Empty;
        public string ma_nghe_nghiep { get; set; } = string.Empty;
        public string ma_nghe_nghiep_his { get; set; } = string.Empty;
        public string dia_chi { get; set; } = string.Empty;
        public string ngay_sinh { get; set; } = string.Empty;
        public int gioi_tinh { get; set; }
        public string nhom_mau { get; set; } = string.Empty;
        public string email { get; set; } = string.Empty;
        public string dien_thoai { get; set; } = string.Empty;
        public string ma_dinh_danh { get; set; } = string.Empty;
        public string ma_the_bhyt { get; set; } = string.Empty;
        public string so_gttt { get; set; } = string.Empty;
        public string ma_dkbd { get; set; } = string.Empty;
        public string gt_the_tu { get; set; } = string.Empty;
        public string gt_the_den { get; set; } = string.Empty;
        public string ngay_cap_gttt { get; set; } = string.Empty;
        public string noi_cap_gttt { get; set; } = string.Empty;
        public string ngay_vao { get; set; } = string.Empty;
        public string ngay_vao_noi_tru { get; set; } = string.Empty;
        public string ly_do_vnt { get; set; } = string.Empty;
        public string ma_doituong_kcb { get; set; } = string.Empty;
        /// <summary>
        /// HIS Tag có mã đối tượng khám chữa bệnh riêng
        /// </summary>
        public string ma_doituong_kcb_his { get; set; } = string.Empty;
        public string ma_loai_kcb { get; set; } = string.Empty;
        public string noi_lam_viec { get; set; } = string.Empty;
        public string dia_chi_lam_viec { get; set; } = string.Empty;
        
        // Fields mới cho API mới
        public string ma_ly_do_vnt { get; set; } = string.Empty;
        public string ma_cskcb { get; set; } = string.Empty;
        public string anh_bn_cccd { get; set; } = string.Empty;
    }
}

﻿using AgentGateway.Lib.Models;
using AgentGateway.Lib.Requests;
using AgentGateway.Lib.Responses;
using Newtonsoft.Json;
using System.Text;

namespace AgentGateway.Lib;

public static class AgentGatewayIntegrateClient
{
    public static async Task<(bool, string, InitTransactionResponse)> InitTransaction(
        this HttpClient client,
        InitTransactionRequest request,
        string url)
    {
        bool result = false;
        string message = string.Empty;
        InitTransactionResponse data = new();

        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        HttpResponseMessage response = await client.PostAsync(url, content);

        if (response.IsSuccessStatusCode)
        {
            string responseContent = await response.Content.ReadAsStringAsync();
            var responseModel = JsonConvert.DeserializeObject<VneidBaseResponse<InitTransactionResponse>>(responseContent);

            if (responseModel != null)
            {
                result = responseModel.responseCode == "0";
                message = responseModel.description ?? string.Empty;
                data = responseModel.data ?? new();
            }
        }
        else
        {
            message = await response.Content.ReadAsStringAsync();
        }

        return (result, message, data);
    }

    public static async Task<(bool, string, GetTransactionResponse)> GetTransaction(
        this HttpClient client,
        GetTransactionRequest request,
        string url)
    {
        bool result = false;
        string message = string.Empty;
        GetTransactionResponse data = new();

        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        HttpResponseMessage response = await client.PostAsync(url, content);

        if (response.IsSuccessStatusCode)
        {
            string responseContent = await response.Content.ReadAsStringAsync();
            var responseModel = JsonConvert.DeserializeObject<VneidBaseResponse<GetTransactionStatusResponse>>(responseContent);

            if (responseModel != null)
            {
                result = responseModel.responseCode == "0";
                message = responseModel.description ?? string.Empty;
                data = new GetTransactionResponse
                {
                    Success = result,
                    Message = message,
                    BiometricResult = responseModel.data?.biometricResult,
                    UserInfo = responseModel.data?.userInfo
                };
            }
        }
        else
        {
            message = await response.Content.ReadAsStringAsync();
        }

        return (result, message, data);
    }

    public static async Task<(bool, string, UserSharedRequestResponse)> UserShared(
        this HttpClient client,
        UserSharedRequest request,
        string url)
    {
        bool result = false;
        string message = string.Empty;
        UserSharedRequestResponse data = new();

        var content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
        HttpResponseMessage response = await client.PostAsync(url, content);

        if (response.IsSuccessStatusCode)
        {
            string responseContent = await response.Content.ReadAsStringAsync();
            var responseModel = JsonConvert.DeserializeObject<VneidBaseResponse<UserSharedResponse>>(responseContent);

            if (responseModel != null)
            {
                result = responseModel.responseCode == "0";
                message = responseModel.description ?? string.Empty;
                data = new UserSharedRequestResponse
                {
                    Success = result,
                    Message = message,
                    ResultCode = responseModel.responseCode,
                    ResultDesc = responseModel.description
                };
            }
        }
        else
        {
            message = await response.Content.ReadAsStringAsync();
        }

        return (result, message, data);
    }
}

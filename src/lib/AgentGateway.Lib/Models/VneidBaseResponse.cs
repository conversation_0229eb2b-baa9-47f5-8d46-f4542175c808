﻿namespace AgentGateway.Lib.Models
{
    public class VneidBaseResponse<T>
    {
        public string? responseCode { get; set; }
        public string? description { get; set; }
        public string? responseTime { get; set; }
        public T? data { get; set; }
        public string? userInfo { get; set; }
    }
    public class InitTransactionRequestResponse
    {
        public string? txnId { get; set; }
        public string? requestId { get; set; }
    }

    public class GetTransactionStatusResponse
    {
        public string? biometricResult { get; set; }
        public string? userInfo { get; set; }
    }

    public class UserSharedResponse
    {
        public string? responseCode { get; set; }
        public string? description { get; set; }
        public string? responseTime { get; set; }
    }
}

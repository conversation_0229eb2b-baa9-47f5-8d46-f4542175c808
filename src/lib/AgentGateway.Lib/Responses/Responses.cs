﻿namespace AgentGateway.Lib.Responses
{
    public class InitTransactionResponse
    {
        //public bool Success { get; set; }
        //public string? Message { get; set; }
        public string? TxnId { get; set; }
        public string? RequestId { get; set; }
    }

    public class GetTransactionResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? BiometricResult { get; set; }
        public string? UserInfo { get; set; }
    }

    public class UserSharedRequestResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? ResultCode { get; set; }
        public string? ResultDesc { get; set; }
    }
}

﻿namespace HisClientV7.Lib.Request
{
    public class CreateRegisterFormWithMultiServiceRequest
    {
        public PatientInfoModel thong_tin_benh_nhan { get; set; } = new PatientInfoModel();
        public List<ServiceInfoModel> thong_tin_dich_vu { get; set; } = new List<ServiceInfoModel>();
        public string du_phong { get; set; } = string.Empty;
        public string so_giay_chuyen_tuyen { get; set; } = string.Empty;
        public string ngay_chuyen_tuyen { get; set; } = string.Empty;
        public string ma_benh_chuyen_tuyen { get; set; } = string.Empty;
        public string don_vi_chuyen_tuyen { get; set; } = string.Empty;
        public string id_thiet_bi { get; set; } = string.Empty;
        public string? ma_ca { get; set; }
        public string? ma_quay { get; set; }
    }

    public class ServiceInfoModel
    {
        public string id_khoa { get; set; } = string.Empty;
        public string id_phong_kham { get; set; } = string.Empty;
        public string id_nhom_phong_kham { get; set; } = string.Empty;
        public string ten_phong_kham { get; set; } = string.Empty;
        public decimal don_gia_phong_kham { get; set; }
        public string id_khung_thoi_gian { get; set; } = string.Empty;
        public string id_loai_kham { get; set; } = string.Empty;
    }

    public class PatientInfoModel
    {
        /// <summary>
        /// ID bệnh nhân
        /// </summary>
        public string id_bn { get; set; } = string.Empty;
        /// <summary>
        /// Code bệnh nhân
        /// </summary>
        public string ma_bn { get; set; } = string.Empty;
        public string ho_ten { get; set; } = string.Empty;
        public string ho_bn { get; set; } = string.Empty;
        public string ten_bn { get; set; } = string.Empty;
        public string matinh_cutru { get; set; } = string.Empty;
        public string mahuyen_cu_tru { get; set; } = string.Empty;
        public string maxa_cu_tru { get; set; } = string.Empty;
        public string ma_dantoc { get; set; } = string.Empty;
        public string ma_quoctich { get; set; } = string.Empty;
        public string ma_nghe_nghiep { get; set; } = string.Empty;
        public string ma_nghe_nghiep_his { get; set; } = string.Empty;
        public string dia_chi { get; set; } = string.Empty;
        public string? dia_chi_day_du { get; set; }
        public string? dia_chi_bhyt { get; set; }
        public string ngay_sinh { get; set; } = string.Empty;
        public string ma_dinh_danh { get; set; } = string.Empty;
        public int gioi_tinh { get; set; } = 0;
        public string nhom_mau { get; set; } = string.Empty;
        public string so_gttt { get; set; } = string.Empty;
        public string ngay_cap_gttt { get; set; } = string.Empty;
        public string noi_cap_gttt { get; set; } = string.Empty;
        public string ma_the_bhyt { get; set; } = string.Empty;
        public string ma_dkbd { get; set; } = string.Empty;
        public string gt_the_tu { get; set; } = string.Empty;
        public string gt_the_den { get; set; } = string.Empty;
        public string ma_doituong_kcb { get; set; } = string.Empty;
        public string ma_kv { get; set; } = string.Empty;
        public string ngay_du_5_nam { get; set; } = string.Empty;
        public string ngay_vao { get; set; } = string.Empty;
        public string ngay_vao_noi_tru { get; set; } = string.Empty;
        public string ly_do_vnt { get; set; } = string.Empty;
        public string ma_ly_do_vnt { get; set; } = string.Empty;
        public string ma_loai_kcb { get; set; } = string.Empty;
        public string ma_doituong_kcb_his { get; set; } = string.Empty;
        public string ma_cskcb { get; set; } = string.Empty;
        public string anh_bn_cccd { get; set; } = string.Empty;
        public string noi_lam_viec { get; set; } = string.Empty;
        public string dia_chi_lam_viec { get; set; } = string.Empty;
        public string dien_thoai { get; set; } = string.Empty;
        public string quan_he_nt { get; set; } = string.Empty;
        public string ho_ten_nt { get; set; } = string.Empty;
        public string ngay_sinh_nt { get; set; } = string.Empty;
        public string dia_chi_nt { get; set; } = string.Empty;
        public int? phan_tuyen { get; set; }
        public string dien_thoai_nt { get; set; } = string.Empty;
        public string ma_dinh_danh_nt { get; set; } = string.Empty;
        public string chan_doan_tuyen_duoi { get; set; } = string.Empty;
        public string? cs_chieu_cao { get; set; }
        public string? cs_can_nang { get; set; }
        public string? cs_huyet_ap { get; set; }
        public string? cs_nhip_tim { get; set; }
        public string? cs_nhip_tho { get; set; }
        public string? cs_oxi_mau { get; set; }
        public string? cs_mach { get; set; }
        public string? cs_nhiet_do { get; set; }
        public int? uu_tien { get; set; }
    }
}

﻿using HisClientV7.Lib.Model;

namespace HisClientV7.Lib.Request
{
    public class CreateRegisterForForPackageRequest
    {
        public PatientRequestModel thong_tin_benh_nhan { get; set; } = new();
        public HealthPackageModel thong_tin_dich_vu { get; set; } = new();
        public string? id_loai_kham { get; set; }
    }

    public class PatientRequestModel
    {
        public string? id_bn { get; set; }
        public string? ma_bn { get; set; }
        public string ho_ten { get; set; } = string.Empty;
        public string ho_bn { get; set; } = string.Empty;
        public string ten_bn { get; set; } = string.Empty;
        public string matinh_cutru { get; set; } = string.Empty;
        public string mahuyen_cu_tru { get; set; } = string.Empty;
        public string maxa_cu_tru { get; set; } = string.Empty;
        public string ma_dantoc { get; set; } = string.Empty;
        public string ma_quoctich { get; set; } = string.Empty;
        public string ma_nghe_nghiep { get; set; } = string.Empty;
        public string dia_chi { get; set; } = string.Empty;
        public string dia_chi_day_du { get; set; } = string.Empty;
        public string ngay_sinh { get; set; } = string.Empty;
        public int gioi_tinh { get; set; }
        public string dien_thoai { get; set; } = string.Empty;
        public string so_gttt { get; set; } = string.Empty;
        public string ngay_cap_gttt { get; set; } = string.Empty;
        public string noi_cap_gttt { get; set; } = string.Empty;
        public string noi_lam_viec { get; set; } = string.Empty;
        public string dia_chi_lam_viec { get; set; } = string.Empty;
        public string anh_bn_cccd { get; set; } = string.Empty;
    }

    public class HealthPackageModel
    {
        public string id_goi_kham { get; set; } = string.Empty;
        public string? ma_goi_kham { get; set; }
        public string ten_goi_kham { get; set; } = string.Empty;
        public decimal don_gia { get; set; }
        public List<HealthPackageServiceModel> chi_tiet_dich_vu { get; set; } = new();
        public List<TechnicalServiceModel> dich_vu_ky_thuat { get; set; } = new();
    }
}

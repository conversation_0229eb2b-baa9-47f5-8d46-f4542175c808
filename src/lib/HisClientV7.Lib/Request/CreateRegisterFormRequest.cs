﻿using HisClientV7.Lib.Model;

namespace HisClientV7.Lib.Request
{
    public class CreateRegisterFormRequest
    {
        public PatientModel thong_tin_benh_nhan { get; set; } = new PatientModel();
        public HealthServiceModel thong_tin_dich_vu { get; set; } = new HealthServiceModel();
        public string id_thiet_bi { get; set; } = string.Empty;
        public string id_loai_kham { get; set; } = string.Empty;
        public string du_phong { get; set; } = string.Empty;
        public string so_giay_chuyen_tuyen { get; set; } = string.Empty;
        public string ma_benh_chuyen_tuyen { get; set; } = string.Empty;
        public string don_vi_chuyen_tuyen { get; set; } = string.Empty;
        /// <summary>
        /// Có đăng ký dịch vụ không
        /// </summary>
        public bool bn_dichvu { get; set; } = false;
        /// <summary>
        /// Mã tai nạn (nếu có)
        /// </summary>
        public string ma_tai_nan { get; set; } = string.Empty;
    }
}

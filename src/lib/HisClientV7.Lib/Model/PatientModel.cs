﻿using Newtonsoft.Json;
namespace HisClientV7.Lib.Model
{
    public class PatientModel
    {
        public string? id_bn { get; set; }
        public string? ma_bn { get; set; }
        public string ho_ten { get; set; } = string.Empty;
        public string ho_bn { get; set; } = string.Empty;
        public string ten_bn { get; set; } = string.Empty;
        public string matinh_cutru { get; set; } = string.Empty;
        public string macutru_cap1 { get; set; } = string.Empty;
        public string mahuyen_cu_tru { get; set; } = string.Empty;
        public string maxa_cu_tru { get; set; } = string.Empty;
        public string macutru_cap2 { get; set; } = string.Empty;
        public string ma_dantoc { get; set; } = string.Empty;
        public string ma_quoctich { get; set; } = string.Empty;
        public string ma_nghe_nghiep { get; set; } = string.Empty;
        public string ma_nghe_nghiep_his { get; set; } = string.Empty;
        public string dia_chi { get; set; } = string.Empty;
        public string dia_chi_day_du { get; set; } = string.Empty;
        public string dia_chi_bhyt { get; set; } = string.Empty;
        public string ngay_sinh { get; set; } = string.Empty;
        public int gioi_tinh { get; set; }
        public string nhom_mau { get; set; } = string.Empty;
        public string email { get; set; } = string.Empty;
        public string dien_thoai { get; set; } = string.Empty;
        public string ma_dinh_danh { get; set; } = string.Empty;
        public string ma_the_bhyt { get; set; } = string.Empty;
        public string so_gttt { get; set; } = string.Empty;
        public string ma_dkbd { get; set; } = string.Empty;
        public string ngay_du_5_nam { get; set; } = string.Empty;
        public string gt_the_tu { get; set; } = string.Empty;
        public string gt_the_den { get; set; } = string.Empty;
        public string ngay_cap_gttt { get; set; } = string.Empty;
        public string noi_cap_gttt { get; set; } = string.Empty;
        public string ngay_vao { get; set; } = string.Empty;
        public string ngay_vao_noi_tru { get; set; } = string.Empty;
        public string ly_do_vnt { get; set; } = string.Empty;
        public string ly_do_vv { get; set; } = string.Empty;
        public string ma_doituong_kcb { get; set; } = string.Empty;
        /// <summary>
        /// HIS Tag có mã đối tượng khám chữa bệnh riêng
        /// </summary>
        public string ma_doituong_kcb_his { get; set; } = string.Empty;
        public string ma_loai_kcb { get; set; } = string.Empty;
        public string ma_kv { get; set; } = string.Empty;
        public int? phan_tuyen { get; set; }
        public string tuyen { get; set; } = string.Empty;
        public string noi_lam_viec { get; set; } = string.Empty;
        public string dia_chi_lam_viec { get; set; } = string.Empty;
        public string quan_he_nt { get; set; } = string.Empty;
        public string ho_ten_nt { get; set; } = string.Empty;
        public string ngay_sinh_nt { get; set; } = string.Empty;
        public string dia_chi_nt { get; set; } = string.Empty;
        public string dien_thoai_nt { get; set; } = string.Empty;
        public string ma_dinh_danh_nt { get; set; } = string.Empty;
        public List<HistoryModel> lich_su_kham_benh { get; set; } = new();
        public string chan_doan_tuyen_duoi { get; set; } = string.Empty;
        public string cs_huyet_ap { get; set; } = string.Empty;
        public string cs_nhip_tim { get; set; } = string.Empty;
        public string cs_nhip_tho { get; set; } = string.Empty;
        public string cs_oxy_mau { get; set; } = string.Empty;
        public string cs_chieu_cao { get; set; } = string.Empty;
        public string cs_can_nang { get; set; } = string.Empty;
        public string cs_mach { get; set; } = string.Empty;
        public string cs_nhiet_do { get; set; } = string.Empty;
        [JsonConverter(typeof(IntBoolConverter))]
        public int? uu_tien { get; set; }
        public int tiep_nhan_kham { get; set; } = 1;
        public string? ly_do_tu_choi { get; set; }
    }

    public class IntBoolConverter : JsonConverter<int?>
    {
        public override int? ReadJson(JsonReader reader, Type objectType, int? existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            return reader.TokenType switch
            {
                JsonToken.Null => null,
                JsonToken.Boolean => reader.Value != null && (bool)reader.Value ? 1 : 0,
                JsonToken.Integer => reader.Value != null ? Convert.ToInt32(reader.Value) : 0,
                _ => null
            };
        }

        public override void WriteJson(JsonWriter writer, int? value, JsonSerializer serializer)
        {
            writer.WriteValue(value);
        }
    }
}

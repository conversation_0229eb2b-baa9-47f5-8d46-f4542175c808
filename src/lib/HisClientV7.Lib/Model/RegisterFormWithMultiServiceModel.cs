using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HisClientV7.Lib.Model
{
    public class RegisterFormWithMultiServiceModel
    {
        public PatientInfoModel thong_tin_benh_nhan { get; set; } = new PatientInfoModel();
        public ReceptionInformationModel thong_tin_tiep_nhan { get; set; } = new ReceptionInformationModel();
        public List<RegisterInfoModel> thong_tin_dang_ky { get; set; } = [];
        public PaymentInfoModel thong_tin_thanh_toan { get; set; } = new PaymentInfoModel();
    }

    public class PaymentInfoModel
    {
        public string qr_code { get; set; } = string.Empty;
        public string so_phieu { get; set; } = string.Empty;
    }

    public class RegisterDetailModel
    {
        public string id_dang_ky { get; set; } = string.Empty;
        public string id_chi_tiet_dk { get; set; } = string.Empty;
        public string id_phong_kham { get; set; } = string.Empty;
        public string ten_phong_kham { get; set; } = string.Empty;
        public string loai_dich_vu { get; set; } = string.Empty;
        public string don_vi { get; set; } = string.Empty;
        public int? so_luong { get; set; }
        public string dvt { get; set; } = string.Empty;
        public decimal don_gia { get; set; }
        public int? trang_thai { get; set; }
    }

    public class RegisterInfoModel
    {
        public string id_dang_ky { get; set; } = string.Empty;
        public string ma_dang_ky { get; set; } = string.Empty;
        public string ten_dang_ky { get; set; } = string.Empty;
        public string ngay_dang_ky { get; set; } = string.Empty;
        public int? trang_thai_dang_ky { get; set; }
        public decimal don_gia_dang_ky { get; set; }
        public string mo_ta_dang_ky { get; set; } = string.Empty;
        public string? ten_khu_vuc { get; set; }
        public int? stt_lk { get; set; }
        public RegisterDetailModel chi_tiet_dang_ky { get; set; } = new();
    }

    public class ReceptionInformationModel
    {
        public string id_lk { get; set; } = string.Empty;
        public string ma_lk { get; set; } = string.Empty;
        public string ma_vv { get; set; } = string.Empty;
        public string ngay_vao { get; set; } = string.Empty;
        public string ngay_vao_noi_tru { get; set; } = string.Empty;
        public string ngay_ra { get; set; } = string.Empty;
        public int? trang_thai_lk { get; set; }
    }

    public class PatientInfoModel
    {
        public string id_bn { get; set; } = string.Empty;
        public string ma_bn { get; set; } = string.Empty;
        public string ho_ten { get; set; } = string.Empty;
        public string ho_bn { get; set; } = string.Empty;
        public string ten_bn { get; set; } = string.Empty;
        public string matinh_cutru { get; set; } = string.Empty;
        public string mahuyen_cu_tru { get; set; } = string.Empty;
        public string maxa_cu_tru { get; set; } = string.Empty;
        public string ma_dantoc { get; set; } = string.Empty;
        public string ma_quoctich { get; set; } = string.Empty;
        public string ma_nghe_nghiep { get; set; } = string.Empty;
        public string dia_chi { get; set; } = string.Empty;
        public string ngay_sinh { get; set; } = string.Empty;
        public int gioi_tinh { get; set; } = 0;
        public string nhom_mau { get; set; } = string.Empty;
        public string email { get; set; } = string.Empty;
        public string so_gttt { get; set; } = string.Empty;
        public string ma_the_bhyt { get; set; } = string.Empty;
    }
}
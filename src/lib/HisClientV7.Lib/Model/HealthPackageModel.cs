﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HisClientV7.Lib.Model
{
    public class HealthPackageModel
    {
        public string id_goi_kham { get; set; } = string.Empty;
        public string? ma_goi_kham { get; set; }
        public string ten_goi_kham { get; set; } = string.Empty;
        public string? id_loai_kham { get; set; }
        public decimal don_gia { get; set; }
        public string? mo_ta { get; set; }
        public int? thu_tu_sap_xep { get; set; }
        public string? do_tuoi { get; set; }
        public string? doi_tuong_sd { get; set; }
        public string? han_dung { get; set; }
        public string? sl_su_dung { get; set; }
        public List<HealthPackageServiceModel> dich_vu_kham { get; set; } = new List<HealthPackageServiceModel>();
    }
}

﻿using System.Globalization;
using System.Text.RegularExpressions;
using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using HisClientV7.Lib.Response;
using Mapster;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Luồng chung: Q5, Bệnh viện GTVT, Tỉnh Điện Biên, VNPT L2
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV7Service : HisServiceParameters, IHisService
    {
        private readonly HisV7Config hisConfig;

        public HisV7Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV7Config is null");
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            //get healthcare service tier from hospital metadata
            var tiers = await hospitalMetadataRepository!.GetHospitalMetadataByKeyAsync(current.HospitalId, "healthcare_service_tiers");

            if (tiers is null || string.IsNullOrEmpty(tiers.Value))
            {
                return (false, "Không tìm thấy cấu hình thông tin tuyến", ErrorTypeEnum.MediPayError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, JsonConvert.DeserializeObject<List<GetHealthcareServiceTierDto>>(tiers.Value) ?? []);
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            var config = await databaseService.HospitalMetaDatas
                    .FirstOrDefaultAsync(x => x.HospitalId == current.HospitalId && x.GroupType == "danh_sach_doi_tuong_kcb");

            if (config is null || string.IsNullOrEmpty(config.Value))
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            //parse string to list of GetMedicalTreatmentCategoryDto
            var list = JsonConvert.DeserializeObject<List<GetMedicalTreatmentCategoryDto>>(config.Value);
            if (list is null)
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            if (current.IsGenQueueNumberByHis)
            {
                var url = $"{hisConfig.Host}/lay-stt";
                (bool result, string message, QueueNumberResponse resData)
                    = await httpClientFactory.CreateClient().GetQueueNumber(new QueueNumberRequest
                    {
                        MA_PHONG = string.Empty,
                        MA_DICH_VU = string.Empty,
                        UU_TIEN = request.UuTien.GetValueOrDefault()
                    }, url, hisConfig.MerchantId, hisConfig.SecretKey);

                if (!result)
                {
                    return (false, message, ErrorTypeEnum.HisError, new AutoDialerQueueAddNewResponse());
                }

                return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
                {
                    SoThuTu = int.Parse(resData.STT ?? "0"),
                    UuTien = request.UuTien == 1,
                    NgayDangKy = DateTimeHelper.GetCurrentLocalDateTime(),
                    MaPhong = request.MaPhong ?? string.Empty,
                });
            }
            var today = DateTimeHelper.GetCurrentLocalDateTime().Date;
            var identityNo = request.SoCCCD ?? string.Empty;
            if (current.IsAllowOnlyOneQueuePerDay)
            {
                var existedHistory = await databaseService.DialerQueueHistories
                    .FirstOrDefaultAsync(h =>
                        h.IdentityNo == identityNo &&
                        h.QueueDate.Date == today &&
                        h.HospitalId == current.HospitalId);
                if (!string.IsNullOrEmpty(identityNo) && existedHistory != null)
                {
                    return (true, "QUEUE_EXISTS", ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
                    {
                        Id = existedHistory.DialerQueueId,
                        SoThuTu = existedHistory.QueueNumber,
                        UuTien = existedHistory.UuTien,
                        NgayDangKy = existedHistory.QueueDate.AddHours(7),
                        MaPhong = existedHistory.HealthServiceId,
                    });
                }
            }

            // Số ưu tiên nối tiếp số thường, bỏ qua Priority
            var currentQueue = await databaseService.DialerQueues.FirstOrDefaultAsync(a =>
                a.QueueDate.Date == today &&
                a.HospitalId == current.HospitalId &&
                (string.IsNullOrEmpty(request.MaPhong) || a.HealthServiceId == request.MaPhong)
            );

            if (currentQueue is not null)
            {
                currentQueue.QueueNumber += 1;
                databaseService.DialerQueues.Update(currentQueue);
            }
            else
            {
                currentQueue = new DialerQueue
                {
                    Id = Guid.NewGuid().ToString(),
                    HealthServiceId = request.MaPhong,
                    QueueDate = DateTimeHelper.GetCurrentLocalDateTime(),
                    QueueNumber = 1,
                    HospitalId = current.HospitalId,
                };
                databaseService.DialerQueues.Add(currentQueue);
            }
            if (current.IsAllowOnlyOneQueuePerDay)
            {
                var history = new DialerQueueHistory
                {
                    Id = Guid.NewGuid().ToString(),
                    IdentityNo = identityNo,
                    QueueDate = today,
                    QueueNumber = currentQueue.QueueNumber,
                    DialerQueueId = currentQueue.Id,
                    HospitalId = current.HospitalId,
                };
                databaseService.DialerQueueHistories.Add(history);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
            {
                Id = currentQueue.Id,
                SoThuTu = currentQueue.QueueNumber,
                UuTien = request.UuTien == 1,
                NgayDangKy = currentQueue.QueueDate.AddHours(7),
                MaPhong = currentQueue.HealthServiceId,
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            var callQueueNumberHisRequest = new CallQueueNumberHisRequest
            {
                TEN_DOI_TUONG = request.TenDoiTuong ?? string.Empty,
                ID_DOI_TUONG = request.IdDoiTuong ?? string.Empty,
                NGAY_LAYSO = request.NgayLaySo ?? string.Empty,
                STT = request.STT ?? string.Empty,
                SO_GTTT = request.SoGTTT ?? string.Empty,
                HO_TEN = request.HoTenBN ?? string.Empty,
                NGAY_SINH = request.NgaySinh ?? string.Empty,
                GIOI_TINH = request.GioiTinh,
                DIA_CHI_DAY_DU = request.DiaChiDayDu ?? string.Empty,
                UU_TIEN = request.UuTien ?? false,
            };

            string url = $"{hisConfig.Host}/goi-stt";
            bool result;
            string message;
            (result, message) = await httpClientFactory.CreateClient().CallQueueNumberHis(
                url,
                hisConfig.MerchantId,
                hisConfig.SecretKey,
                callQueueNumberHisRequest
            );

            Log.Information("{LogPrefix} CallQueueNumberHis --- Result: {Result} - Message: {Message} - {@Request}", logPrefix, result, message, callQueueNumberHisRequest);
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new object());
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            PaymentStatusDto status = new();

            if (!current.IsGenQR)
            {
                var payment = await databaseService.Payments
                .FirstOrDefaultAsync(x => x.RefNo == refNo && x.HospitalId == current.HospitalId);

                if (payment is null)
                {
                    return (false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError, new PaymentStatusDto());
                }

                return (true, string.Empty, ErrorTypeEnum.NoError, new PaymentStatusDto()
                {
                    Number = payment.Id ?? string.Empty,
                    PaymentStatus = payment.Status ?? PaymentConstant.WaitForPayment,
                    InvoiceInfoRef = payment.InvoiceInfoRef ?? string.Empty,
                    RefNo = payment.RefNo ?? string.Empty
                });
            }

            string url = $"{hisConfig.Host}/trang-thai-phieu";

            bool result;
            string? message;
            Log.Information("{LogPrefix} CheckPaymentStatus Req --- RefNo: {RefNo}", logPrefix, refNo);
            (result, message, PaymentModel resData) =
                await httpClientFactory.CreateClient().CheckPayment(new CheckPaymentRequest()
                {
                    so_phieu = refNo
                }, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                status.Number = resData.so_phieu;
                status.RefNo = resData.so_phieu;
                status.PaymentStatus = resData.da_thanh_toan ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, status);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            bool result = false;
            string message = string.Empty;
            string refNo = string.Empty;
            RegisterFormsResponseDto data = new();

            try
            {
                if (request.Service.Count == 0)
                {
                    return (false, "No services provided", ErrorTypeEnum.MediPayError, string.Empty, new RegisterFormsResponseDto());
                }

                var doiTuongKcb = request.MedicalTreatmentCategoryId;

                if (string.IsNullOrEmpty(doiTuongKcb))
                {
                    var defaultDoiTuongKcb = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                    : null;

                    doiTuongKcb = defaultDoiTuongKcb?.Value;
                }

                if (string.IsNullOrEmpty(doiTuongKcb))
                {
                    var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                        : null;

                    if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                    {
                        var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                        if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                        {
                            doiTuongKcb = doiTuongKCBMap.ContainsKey(request.ExameTypeId ?? string.Empty)
                                ? doiTuongKCBMap[request.ExameTypeId ?? string.Empty] : "1";
                        }
                    }
                }

                var nationalityId = request.Customer.NationalityId ?? string.Empty;

                var mapNationalities = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                        : null;

                if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
                {
                    var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                    if (nationalities is not null && nationalities.Count > 0)
                    {
                        nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                    }
                }

                var careerId = request.Customer.CareerId ?? string.Empty;
                var mapCareers = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                    : null;

                if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
                {
                    var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                    if (careers is not null && careers.Count > 0)
                    {
                        careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                    }
                }

                var defaultNation = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                    : null;

                // Create a list of ServiceInfoModel objects from the request
                var serviceInfoModels = request.Service.Select(service => new ServiceInfoModel
                {
                    id_khoa = service.ClinicId ?? string.Empty,
                    id_phong_kham = service.Id ?? string.Empty,
                    id_nhom_phong_kham = service.ClinicGroupId ?? string.Empty,
                    ten_phong_kham = service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (service.InsurancePrice ?? 0) : (service.UnitPrice ?? 0),
                    id_khung_thoi_gian = string.Empty,
                    id_loai_kham = service.ExameTypeId ?? string.Empty,
                }).ToList();

                string url = $"{hisConfig.Host}/dangky-n-kcb";
                var req = new CreateRegisterFormWithMultiServiceRequest()
                {
                    thong_tin_benh_nhan = new HisClientV7.Lib.Request.PatientInfoModel()
                    {
                        id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                        ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                        ten_bn = request.Customer.LastName ?? string.Empty,
                        ho_bn = request.Customer.FirstName ?? string.Empty,
                        ho_ten = request.Customer.GetFullName().ToUpper(),
                        dia_chi = request.Customer.Street ?? string.Empty,
                        dia_chi_day_du = request.Customer.Address ?? string.Empty,
                        dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                        ma_dantoc = nationalityId,
                        ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                        matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                        mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                        maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                        ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                        gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                        dien_thoai = request.Customer.Phone ?? string.Empty,
                        ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                        ma_nghe_nghiep = careerId,
                        ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                        nhom_mau = string.Empty,
                        so_gttt = request.Customer.IdentityNo ?? string.Empty,
                        ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                        ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                        ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                        gt_the_tu = request.IsInsurance ?
                            DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                            : string.Empty,
                        gt_the_den = request.IsInsurance ?
                            DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                            : string.Empty,
                        ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                        noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                        ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                        ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                        ly_do_vnt = "Khám bệnh",
                        ma_doituong_kcb = doiTuongKcb ?? "1",
                        ma_loai_kcb = "01",
                        ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                        ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                        noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                        dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                        quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                        ho_ten_nt = request.CustomerRelationship.GetFullName(),
                        ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                            ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                            : string.Empty,
                        dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                        phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                        dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                        ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                        chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                        cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                        cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                        cs_nhiet_do = request.Temperature?.ToString() ?? string.Empty,
                        cs_mach = request.PulseRate?.ToString() ?? string.Empty,
                        uu_tien = request.Priority,
                    },
                    thong_tin_dich_vu = serviceInfoModels,
                    du_phong = string.Empty,
                    id_thiet_bi = request.DeviceId ?? string.Empty,
                    so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                    ngay_chuyen_tuyen = string.Empty,
                    ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                    don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                };

                Log.Information("{LogPrefix} CreateRegisterFormWithMultiService Req --- {@Request}", logPrefix, req);

                var client = httpClientFactory.CreateClient();
                (result, message, RegisterFormWithMultiServiceModel registerDto) = await client.CreateRegisterFormWithMultiService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

                Log.Information("{LogPrefix} CreateRegisterFormWithMultiService Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

                if (result)
                {
                    refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                    data = new RegisterFormsResponseDto
                    {
                        QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                        QueueNumberPriority = request.Priority.ToString(),
                        RegisterNumber = refNo,
                        ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        ExaminationLocation = string.Empty,
                        MedicalTreatmentCategoryName = string.Empty,
                        RateOfInsurance = "0",
                        PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                        PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                        RegisterDetails = [.. registerDto.thong_tin_dang_ky.Select(detail => new RegisterDetailDto
                        {
                            RegisterDetailId = detail.id_dang_ky,
                            RegisterId = refNo,
                            HealthServiceId = detail.chi_tiet_dang_ky.id_phong_kham,
                            HealthServiceName = detail.chi_tiet_dang_ky.ten_phong_kham,
                            ServiceType = detail.chi_tiet_dang_ky.loai_dich_vu,
                            Unit = detail.chi_tiet_dang_ky.don_vi,
                            Quantity = detail.chi_tiet_dang_ky.so_luong,
                            UnitOfMeasure = detail.chi_tiet_dang_ky.dvt,
                            UnitPrice = detail.don_gia_dang_ky,
                            Status = detail.chi_tiet_dang_ky.trang_thai,
                            QueueNumber = detail.stt_lk
                        })],
                    };
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"Error creating register form with multiple services: {ex.Message}";
                Log.Error(ex, "{LogPrefix} Error creating register form with multiple services", logPrefix);
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }
        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;
            var doiTuongKcb = request.MedicalTreatmentCategoryId;

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcb = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                : null;

                doiTuongKcb = defaultDoiTuongKcb?.Value;
            }

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                    : null;

                if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                {
                    var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                    if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                    {
                        doiTuongKcb = doiTuongKCBMap.ContainsKey(request.Service.ExameTypeId ?? string.Empty)
                            ? doiTuongKCBMap[request.Service.ExameTypeId ?? string.Empty] : "1";
                    }
                }
            }

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName().ToUpper(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = careerId,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = request.BloodType ?? string.Empty,
                    so_gttt = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    ly_do_vv = request.ReasonForVisit ?? string.Empty,
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = doiTuongKcb ?? "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                    phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                    cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                    cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                    cs_nhiet_do = request.Temperature.ToString() ?? string.Empty,
                    cs_mach = request.PulseRate.ToString() ?? string.Empty,
                    uu_tien = request.Priority,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    ma_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_nhom_phong_kham = request.Service.ClinicGroupId ?? string.Empty,
                    ma_nhom_phong_kham = request.Service.ClinicGroupCode ?? string.Empty,
                    ten_nhom_phong_kham = request.Service.ClinicGroup ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                bn_dichvu = !request.IsInsurance,
                ma_tai_nan = request.AccidentCode ?? string.Empty,
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = registerDto.thong_tin_benh_nhan.uu_tien?.ToString() ?? request.Priority.ToString(),
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = registerDto.thong_tin_dang_ky.ten_khu_vuc ?? string.Empty,
                    MedicalTreatmentCategoryName = registerDto.thong_tin_dang_ky.ten_doi_tuong ?? string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            string url = $"{hisConfig.Host}/khoa";
            var req = new GetClinicRequest()
            {
                id_loai_kham = exameTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetClinics Req --- {@Request}", logPrefix, req);

            (result, message, List<ClinicModel> resData) =
                await httpClientFactory.CreateClient().GetClinic(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new Clinic()
                {
                    Id = x.id_khoa ?? string.Empty,
                    Code = !string.IsNullOrEmpty(x.ma_khoa) ? x.ma_khoa : (x.id_khoa ?? string.Empty),
                    Name = x.ten_khoa ?? string.Empty,
                    ExameTypeID = exameTypeId ?? string.Empty,
                    WaitingPatientCount = (x.sl_hien_tai.HasValue && x.sl_da_kham.HasValue)
                                                ? Math.Max(x.sl_hien_tai.Value - x.sl_da_kham.Value, 0)
                                                : null,
                    RemainingPatientCount = null,
                    TotalPatientCount = x.sl_toi_da,
                    ProcessingNumber = x.sl_hien_tai,
                }).OrderBy(x => x.Name)];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            string url = $"{hisConfig.Host}/bhyt";
            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var request = new GetHealthInsuranceRequest()
            {
                so_gttt = checkCode ?? string.Empty,
                loai_gttt = "CCCD",
                ho_ten = customer.GetFullName(),
                ngay_sinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                gioi_tinh = customer.Sex == "Nam" ? 2 : 1
            };

            bool result;
            string message;
            Log.Information("{LogPrefix} GetCustomerHealthInsurance Req --- {@Request}", logPrefix, request);
            (result, message, HealthInsuranceModel card) = await httpClientFactory.CreateClient().GetHealthInsurance(
                request, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Res --- Result: {Result} - Message: {Message} - {@Card}", logPrefix, result, message, card);

            if (result)
            {
                data.HealthInsuranceId = card.ma_the_bhyt;
                data.CustomerId = customer.Id;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.ma_the_bhyt ?? string.Empty;
                data.InsuranceGroupID = string.Empty;
                data.FullFiveYearDate = DateTimeHelper.FormatDate(card.ngay_du_5_nam, "yyyy-MM-dd", "dd/MM/yyyy");
                data.FromDate = DateTimeHelper.FormatDate(card.gt_the_tu, "yyyy-MM-dd", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.gt_the_den, "yyyy-MM-dd", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ma_dkbd;
                data.RoutingType = "-1";
                data.ReferralLevel = card.phan_tuyen > 0 ? card.phan_tuyen.ToString()
                    : (card.tiep_nhan_bhyt == 1 ? "1" : "2");
                data.IsCorrectRouting = card.tiep_nhan_bhyt == 1;
                data.AreaCode = card.ma_kv;
                data.Description = card.ten_kq;
                data.RegisterAddress = card.dia_chi;
                data.HisMessage = card.message ?? string.Empty;
                data.MedicalHistories = card.lich_su_kcb?.Select(x => new MedicalHistoryModel()
                {
                    RecordId = x.ma_ho_so ?? string.Empty,
                    HealthcareFacilityId = x.ma_cs_kcb ?? string.Empty,
                    AdmissionDate = x.ngay_vao ?? string.Empty,
                    DischargeDate = x.ngay_ra ?? string.Empty,
                    DiseaseName = x.ten_benh ?? string.Empty,
                    Condition = x.tinh_trang ?? string.Empty,
                    TreatmentResult = x.kq_dieu_tri ?? string.Empty,
                    AdmissionReason = x.ly_do_vv ?? string.Empty
                }).ToList() ?? [];

                //check nếu bệnh viện không cho phép khám bệnh bảo hiểm nhiều lần trong ngày
                if (data.IsCorrectRouting && current.IsBlockForDuplicatedVisitInsurance && data.MedicalHistories?.Count > 0)
                {
                    string localDay = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMdd");
                    if (data.MedicalHistories.Any(x => x.AdmissionDate.StartsWith(localDay)))
                    {
                        data.HisMessage = "Không thể đăng ký khám BHYT do đã có đăng ký trong ngày";
                        data.IsCorrectRouting = false;
                    }
                }

                result = true;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/loai-kham";
            (result, message, List<ExameTypeModel> resData)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                //lấy danh sách id không tự động check bảo hiểm
                var ignoreAutoInsuranceExameTypeIds = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "exame_type_ids_ignore_auto_check_insurance")
                    : null;

                string[] ignoreAutoInsuranceExameTypeIdsArray = ignoreAutoInsuranceExameTypeIds?.Value?.Split(',') ?? [];

                list = [.. resData.Select(x => new ExameType()
                {
                    Id = x.id_loai_kham.ToString(),
                    Name = x.ten_loai_kham ?? string.Empty,
                    IsInsurance = (x.ten_loai_kham ?? string.Empty).Contains("BẢO HIỂM", StringComparison.CurrentCultureIgnoreCase)
                        || (x.ten_loai_kham ?? string.Empty).Contains("BHYT", StringComparison.CurrentCultureIgnoreCase),
                    IsIgnoreAutoCheckInsurance = ignoreAutoInsuranceExameTypeIdsArray.Contains(x.id_loai_kham),
                })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            string url = $"{hisConfig.Host}/phong-kham";
            var req = new GetHealthServiceRequest()
            {
                id_khoa = request.ClinicId ?? string.Empty,
                ma_khoa = request.ClinicCode ?? string.Empty,
                id_loai_kham = request.ExameTypeId ?? string.Empty,
                ma_the_bhyt = request.HealthInsuranceNo ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthServices Req --- {@Request}", logPrefix, req);

            (result, message, List<HealthServiceModel> resData)
                = await httpClientFactory.CreateClient().GetHealthService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                var hospitalMetaDatas = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(current.HospitalId)
                : null;

                var servicePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "SERVICE");
                var insurancePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "INSURANCE");

                list = [.. resData!.OrderBy(x => x.thu_tu_sap_xep)
                .Select(x => new HealthService()
                {
                    Id = x.id_phong_kham ?? string.Empty,
                    Code = x.ma_phong_kham ?? string.Empty,
                    Name = x.ten_phong_kham ?? string.Empty,
                    UnitPrice = current.IsAdvancePayment ? null : x.don_gia_phong_kham,
                    UnitPriceDisplay = servicePriceDisplay?.Value
                        ?? (current.IsAdvancePayment ? "Tạm ứng" : (x.don_gia_phong_kham.ToString("N0") + " đ")),
                    InsurancePrice = current.IsInsuranceAdvancePayment ? null : (current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt),
                    InsurancePriceDisplay = insurancePriceDisplay?.Value
                        ?? (current.IsInsuranceAdvancePayment
                            ? "Tạm ứng" : ((current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt).ToString("N0") + " đ")),
                    ExtraPrice = x.don_gia_thu_them,
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                    ExameTypeId = x.id_loai_kham,
                    ClinicGroupId = x.id_nhom_phong_kham ?? string.Empty,
                    ClinicGroupCode = x.ma_nhom_phong_kham ?? string.Empty,
                    ClinicGroup = x.ten_nhom_phong_kham ?? string.Empty,
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    ExaminationHour = string.Empty,
                    WaitingPatientCount = (x.sl_hien_tai.HasValue && x.sl_da_kham.HasValue)
                                                ? Math.Max(x.sl_hien_tai.Value - x.sl_da_kham.Value, 0)
                                                : null,
                    RemainingPatientCount = (x.sl_toi_da.HasValue && x.sl_hien_tai.HasValue)
                                                ? Math.Max(x.sl_toi_da.Value - x.sl_hien_tai.Value, 0)
                                                : null,
                    TotalPatientCount = x.sl_toi_da,
                    ProcessingNumber = x.sl_hien_tai,
                })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;
            string rejectReason = string.Empty;
            bool isReject = false;
            List<MedicalHistory> medicalHistory = [];

            string url = $"{hisConfig.Host}/benhnhan";

            string message;
            bool result;
            var req = new GetPatientRequest()
            {
                so_gttt = !string.IsNullOrEmpty(patientCode) ? patientCode : customer.IdentityNo ?? string.Empty,
                loai_gttt = requestType,
                dien_thoai = customer.Phone ?? string.Empty,
                ma_the_bhyt = customer.HealthInsuranceNo ?? string.Empty,
                ngay_sinh = customer.DateOfBirth?.ToString("yyyy-MM-dd") ?? string.Empty,
                ho_ten = customer.GetFullName(),
                gioi_tinh = customer.Sex == "Nam" ? 1 : 2,
            };
            Log.Information("{LogPrefix} GetCustomerHis Req --- {@Request}", logPrefix, req);
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(
                req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient?.id_bn ?? string.Empty;
                patientCodeResponse = patient?.ma_bn ?? string.Empty;
                careerId = patient?.ma_nghe_nghiep ?? string.Empty;
                (_, _, _, var exameTypes) = await GetExameTypes();
                medicalHistory = patient?.lich_su_kham_benh?.Select(x => new MedicalHistory()
                {
                    Id = x.id_lich_su_kham ?? string.Empty,
                    HealthServiceId = x.id_phong_kham ?? string.Empty,
                    HealthServiceCode = x.ma_phong_kham ?? string.Empty,
                    HealthServiceName = x.ten_phong_kham ?? string.Empty,
                    ExameTypeId = x.id_loai_kham,
                    ExameType = x.ten_loai_kham ?? string.Empty,
                    ClinicGroupId = x.id_nhom_phong_kham ?? string.Empty,
                    ClinicGroup = x.ten_nhom_phong_kham ?? string.Empty,
                    ClinicId = x.id_khoa ?? string.Empty,
                    ClinicCode = x.ma_khoa ?? string.Empty,
                    Clinic = x.ten_khoa ?? string.Empty,
                    ExaminationHour = x.ngay_dang_ky,
                    IsInsurance = exameTypes.Find(y => y.Id == x.id_loai_kham)?.IsInsurance ?? false,
                    Priority = x.uu_tien,
                })
                .OrderBy(mh => DateTime.TryParseExact(mh.ExaminationHour, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime examTime) ? examTime : DateTime.MaxValue)
                .ToList()
                ?? [];
                rejectReason = patient?.ly_do_tu_choi ?? string.Empty;
                isReject = patient?.tiep_nhan_kham == 0;
            }

            var currentTime = DateTimeHelper.GetCurrentLocalDateTime().TimeOfDay;

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new HisCustomerDto
            {
                PatientId = patientId,
                PatientCode = patientCodeResponse,
                CareerId = careerId,
                RejectReason = rejectReason,
                IsReject = isReject,
                MedicalHistory = medicalHistory
                    .Where(mh => DateTime.TryParseExact(mh.ExaminationHour, "yyyy-MM-dd HH:mm:ss",
                            CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime examTime)
                        && examTime.TimeOfDay > currentTime)
                    .DefaultIfEmpty(medicalHistory.FirstOrDefault()) // Fallback to the earliest time
                    .Where(mh => mh != null)
                    .Take(1)
                    .ToList()!
            });
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            List<GetSocialCareerDto> list = [];

            string url = $"{hisConfig.Host}/nghe-nghiep";

            bool result;
            string message;
            (result, message, List<SocialCareerModel> resData)
                = await httpClientFactory.CreateClient().GetSocialCareers(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetSocialCareers Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Where(x => !string.IsNullOrEmpty(x.MA_NGHE_NGHIEP))
                    .Select(x => new GetSocialCareerDto
                    {
                        Id = x.MA_NGHE_NGHIEP,
                        Name = x.TEN_NGHE_NGHIEP,
                        IsDefault = x.MAC_DINH.GetValueOrDefault()
                    })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            if (current.IsInsuranceAdvancePayment || current.IsAdvancePayment)
            {
                //get advance money from db
                var advancedMoneyMeta = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "advance_money")
                    : null;

                if (advancedMoneyMeta != null && !string.IsNullOrEmpty(advancedMoneyMeta.Value))
                {
                    return (true, string.Empty, ErrorTypeEnum.NoError, advancedMoneyMeta.Value.Split(";") ?? []);
                }
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, null);
        }

        public async Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            if (current.IsGenQR)
            {
                return (true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse());
            }

            var hospital = await databaseService.Hospitals.FindAsync(current.HospitalId);
            if (hospital is null)
            {
                return (false, PaymentConstant.NotFoundMerchant, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
            }

            //4. Send notification
            var config = new PaymentConfigModel()
            {
                SecretKey = hospital!.SecretKey,
                Url = hospital.IpnUrl
            };

            var ipnRequest = new CreateIpnRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = request.InvoiceId,
                TransactionId = request.TransactionId,
                Status = request.Status,
                TransactionAmount = request.PaidAmount,
                TransactionDescription = request.TransactionDescription,
                PaidAmount = request.PaidAmount,
                PaidDescription = request.PaidDescription,
                PaidTime = request.PaidTime.GetValueOrDefault().ToString("yyyyMMddHHmmss")
            };

            Log.Information("{HospitalId} Call Ipn Req {@Request}", current.HospitalId, ipnRequest);

            (bool ipnResult, string ipnMessage, string ipnRes, _) = await PaymentClient.Lib.PaymentClient.CreateIpn(httpClientFactory.CreateClient(), ipnRequest, config);

            //5. Cập nhật trạng thái sau khi IPN
            Log.Information("{HospitalId} Call Ipn Res {Result} - Message {Message} - Response {Response}", current.HospitalId, ipnResult, ipnMessage, ipnRes);

            return (true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()
            {
                IpnResult = ipnResult,
                IpnMessage = ipnMessage,
                IpnResponse = ipnRes
            });
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public async Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            var req = new UpdatePaymentStatusRequest
            {
                MerchantId = request.MerchantId,
                InvoiceId = request.InvoiceId,
                TransactionId = request.TransactionId,
                KioskId = request.KioskId,
                Status = request.Status,
                TransactionAmount = request.TransactionAmount,
                PaidAmount = request.PaidAmount,
                TransactionDescription = request.TransactionDescription,
                PaidDescription = request.PaidDescription,
                PaidTime = request.PaidTime,
            };
            (bool result, string message, ErrorTypeEnum errorType, UpdatePaymentStatusResponse res) = await UpdatePaymentStatus(req);
            if (!result)
            {
                return (result, message, errorType, new UpdateParaclinicalPaymentStatusResponse());
            }
            var response = res.Adapt<UpdateParaclinicalPaymentStatusResponse>();
            response.Url = DataStorageObject.GetHospital(current.HospitalId)?.IpnUrl ?? string.Empty;
            return (true, string.Empty, ErrorTypeEnum.NoError, response);
        }

        public async Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            string type = request.Option switch
            {
                1 => "MA_CCCD",
                2 => "MA_BN",
                3 => "MA_BA",
                _ => string.Empty
            };

            Customer customer = request.Option == 2
                ? await databaseService.Customers.FirstOrDefaultAsync(x => x.IdentityNo == request.Code) ?? new()
                : new();

            string url = $"{hisConfig.Host}/tracuu-cls?type={type}&code={request.Code}";

            Log.Information("{LogPrefix} GetParaclinicalIndications Req --- {url}", logPrefix, url);
            (bool result, string message, ParaclinicalModel paraclinicalModel) = await httpClientFactory.CreateClient().GetIndications(url, hisConfig.MerchantId, hisConfig.SecretKey, request.Code);
            Log.Information("{LogPrefix} GetParaclinicalIndications Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, paraclinicalModel);
            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, new IndicationSearchResponse());
            }
            var data = new IndicationSearchResponse
            {
                HealthInsuranceNo = paraclinicalModel.ma_the_bhyt ?? customer.HealthInsuranceNo ?? string.Empty,
                PatientCode = paraclinicalModel.ma_bn ?? string.Empty,
                FullName = paraclinicalModel.ten_bn ?? customer.GetFullName() ?? string.Empty,
                Sex = paraclinicalModel.gioi_tinh == 1 ? "Nam" : "Nữ",
                Phone = paraclinicalModel.dien_thoai ?? customer.Phone ?? string.Empty,
                DateOfBirth = DateTimeHelper.ConvertStringToDateTime(paraclinicalModel.ngay_sinh, "yyyy-MM-dd"),
                Address = paraclinicalModel.dia_chi_day_du ?? customer.Address ?? string.Empty,
                RefNo = paraclinicalModel.ma_ho_so ?? string.Empty,
                Indications = paraclinicalModel.chi_dinh?.Select(indication => new Indication()
                {
                    IndicationId = indication.so_phieu ?? string.Empty,
                    IndicationName = indication.loai_chi_dinh ?? string.Empty,
                    IndicationType = indication.loai_chi_dinh ?? string.Empty,
                    ClinicName = indication.chuyen_khoa ?? string.Empty,
                    TotalAmount = indication.tong_tien,
                    PaymentTotalAmount = indication.tong_tien_bn_can_tra,
                    PaymentStatus = indication.trang_thai_thanh_toan ? "Success" : "Fail",
                    Services = indication.chi_tiet?.Select(service => new Service()
                    {
                        ServiceId = service.ma_dich_vu ?? string.Empty,
                        ServiceCode = service.ma_dich_vu ?? string.Empty,
                        ServiceName = service.ten_dich_vu ?? string.Empty,
                        Quantity = service.so_luong,
                        UnitPrice = service.don_gia,
                        PaymentAmount = service.tong_tien_bn_can_tra,
                        Amount = service.tong_tien,
                    }).ToList()
                }).ToList(),
                TotalAmount = paraclinicalModel.tong_tien,
            };
            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            PushReceiptInfoResponseDto responseContent = new();

            var hospital = await databaseService.Hospitals.FindAsync(current.HospitalId);
            if (hospital is null)
            {
                return (false, PaymentConstant.NotFoundMerchant, ErrorTypeEnum.MediPayError, responseContent);
            }
            string url = $"{hisConfig.Host}/thong-tin-xuat-hoa-don";
            var client = httpClientFactory.CreateClient();

            var sendRequest = new SendReceiptInfoRequest
            {
                TEN_CONG_TY = request.CompanyName,
                DIA_CHI_CONG_TY = request.CompanyAddress,
                MA_SO_THUE = request.TaxNo,
                MA_BN = request.PatientCode,
                SO_PHIEU = request.RefNo,
                EMAIL = request.Email,
                SO_DIEN_THOAI = request.PhoneNumber,
                SO_TIEN = request.Amount,
            };

            bool result;
            string message;

            Log.Information("{LogPrefix} PushInvoiceInfo Req --- {@Request}", logPrefix, sendRequest);
            (result, message, PushReceiptInfoResponse response) = await client.PushInvoiceInfo(sendRequest, url, hospital.MerchantId, hospital.SecretKey);

            responseContent.Code = response.Code;
            responseContent.Message = response.Message;

            Log.Information("{LogPrefix} PushInvoiceInfo Res --- Result: {Result} - Message: {Message} - {@ResponseContent}", logPrefix, result, message, responseContent);

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, responseContent);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<HealthPackageType> list = [];

            string url = $"{hisConfig.Host}/loai-ksk";
            (result, message, List<HealthPackageTypeModel> resData)
                = await httpClientFactory.CreateClient().GetHealthPackageTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthPackageTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData.Select(x => new HealthPackageType()
                {
                    Id = x.id_loai_kham.ToString(),
                    Name = x.ten_loai_kham ?? string.Empty
                })];
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthPackage> list = [];

            string url = $"{hisConfig.Host}/goi-ksk";
            var req = new GetHealthPackageRequest()
            {
                id_loai_kham = packageTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthPackages Req --- {@Request}", logPrefix, req);

            (result, message, List<HisClientV7.Lib.Model.HealthPackageModel> resData) =
                await httpClientFactory.CreateClient().GetHealthPackage(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthPackages Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new HealthPackage()
                {
                    Id = x.id_goi_kham ?? string.Empty,
                    Code = !string.IsNullOrEmpty(x.ma_goi_kham) ? x.ma_goi_kham : (x.id_goi_kham ?? string.Empty),
                    Name = x.ten_goi_kham ?? string.Empty,
                    HealthPackageTypeId = packageTypeId ?? string.Empty,
                    UnitPrice = x.don_gia,
                    UnitPriceDisplay = x.don_gia.ToString("N0") + " đ",
                    Description = x.mo_ta ?? string.Empty,
                    SortIndex = x.thu_tu_sap_xep,
                    AgeRange = x.do_tuoi ?? string.Empty,
                    UserType = x.doi_tuong_sd ?? string.Empty,
                    ExpirationDate = x.han_dung ?? string.Empty,
                    UserCount = x.sl_su_dung ?? string.Empty,
                    HealthPackageServices = x.dich_vu_kham?.Select(y => new HealthPackageService()
                    {
                        Id = y.id_dich_vu ?? string.Empty,
                        Code = y.ma_dich_vu ?? string.Empty,
                        Name = y.ten_dich_vu ?? string.Empty,
                        UnitPrice = y.don_gia ?? 0,
                        Description = y.noi_dung ?? string.Empty,
                        SortIndex = y.thu_tu_sap_xep
                    }).ToList() ?? []
                }).OrderBy(x => x.SortIndex)
                .ThenBy(x => x.Name)];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            bool result = false;
            string message = string.Empty;
            List<TechnicalService> list = [];

            string url = $"{hisConfig.Host}/dichvu-kythuat";

            (result, message, List<TechnicalServiceModel> resData) =
                await httpClientFactory.CreateClient().GetHospitalTechnicalServices(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHospitalTechnicalServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new TechnicalService()
                {
                    Id = x.id_dich_vu ?? string.Empty,
                    Name = x.ten_dich_vu ?? string.Empty,
                    UnitPrice = x.don_gia,
                    UnitPriceDisplay = x.don_gia.ToString("N0") + " đ"
                }).OrderBy(x => x.Name)];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormPackageResponseDto data = new();

            bool result;
            string message;

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string url = $"{hisConfig.Host}/dangky-goi-ksk";
            var req = new CreateRegisterForForPackageRequest()
            {
                thong_tin_benh_nhan = new PatientRequestModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName().ToUpper(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_nghe_nghiep = careerId,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    anh_bn_cccd = request.Customer.Image ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                },
                thong_tin_dich_vu = new HisClientV7.Lib.Request.HealthPackageModel()
                {
                    id_goi_kham = request.HealthPackage.Id ?? string.Empty,
                    ma_goi_kham = request.HealthPackage.Code ?? string.Empty,
                    ten_goi_kham = request.HealthPackage.Name ?? string.Empty,
                    don_gia = request.HealthPackage.UnitPrice,
                    chi_tiet_dich_vu = request.HealthPackage.HealthPackageServices?.Select(x => new HealthPackageServiceModel()
                    {
                        id_dich_vu = x.Id ?? string.Empty,
                        ma_dich_vu = x.Code ?? string.Empty,
                        ten_dich_vu = x.Name ?? string.Empty,
                        don_gia = x.UnitPrice
                    }).ToList() ?? [],
                    dich_vu_ky_thuat = request.TechnicalServices?.Select(x => new TechnicalServiceModel()
                    {
                        id_dich_vu = x.Id ?? string.Empty,
                        ten_dich_vu = x.Name ?? string.Empty,
                        don_gia = x.UnitPrice,
                        so_luong = x.Quantity
                    }).ToList() ?? [],

                },
                id_loai_kham = request.HealthPackage.HealthPackageTypeId ?? string.Empty,

            };

            Log.Information("{LogPrefix} CreateRegisterFormforHealthPackage Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterFormForHealthPackage(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterFormforHealthPackage Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                data = new RegisterFormPackageResponseDto
                {
                    RegisterNumber = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan?.stt_lk.ToString() ?? string.Empty,
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}
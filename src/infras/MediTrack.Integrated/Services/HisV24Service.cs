using System.Text.RegularExpressions;
using HisClientV2.Lib;
using HisClientV2.Lib.Model;
using HisClientV2.Lib.Request;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Extensions;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Minh Lộ: Có tách thôn ra khỏi số nhà
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV24Service : HisV2Service
    {
        private readonly HisV2Config hisConfig;

        public HisV24Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV2Config>(configSerialize) ??
                        throw new ArgumentNullException("HisV24Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, string.Empty, new());
            }

            (bool _, string _, List<CategoryModel> categories) = await GetCategoryModels("dmcauhinhtamung", accessToken);

            (string? provinceId, string? districtId, string? wardId)
               = await GetAdministrativeUnitsAsync(accessToken, request.Customer.ProvinceId, request.Customer.DistrictId, request.Customer.WardId);

            var mapNationalities = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                : null;

            if (mapNationalities is null || string.IsNullOrEmpty(mapNationalities.Value))
            {
                return (false, "Không tìm thấy dữ liệu liên kết Dân tộc", ErrorTypeEnum.MediPayError, string.Empty, new());
            }

            var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
            if (nationalities is null || nationalities.Count == 0)
            {
                return (false, "Dữ liệu liên kết Dân tộc không hợp lệ", ErrorTypeEnum.MediPayError, string.Empty, new());
            }

            var nationalityId = nationalities.ContainsKey(request.Customer?.NationalityId ?? string.Empty)
                ? nationalities[request.Customer?.NationalityId ?? string.Empty] : string.Empty;

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string getClinicUrl = $"{hisConfig.Host}/api/KioskRegister";
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "malydotc")?.Name, out int maLyDoTC) == false)
                maLyDoTC = 3;
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "mahinhthuctt")?.Name, out int maHinhThucTT) == false)
                maHinhThucTT = 2;
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "maquyentc")?.Name, out int maQuyenTC) == false)
                maQuyenTC = 1;

            (bool _, string _, List<CategoryModel> dmGioiTinh) = await GetCategoryModels("dmgioitinh", accessToken);
            string? gioiTinh = dmGioiTinh.FirstOrDefault(a => a.Name == request.Customer?.Sex)?.Id;

            var relationShipId = request.CustomerRelationshipId ?? string.Empty;
            var mapRelationShips = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "id_quan_he_nt_map")
                : null;

            if (mapRelationShips is not null && !string.IsNullOrEmpty(mapRelationShips.Value))
            {
                var relationShips = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(mapRelationShips.Value);
                if (relationShips is not null && relationShips.Count > 0)
                {
                    relationShipId = relationShips.FirstOrDefault(x => x.OriginId == request.CustomerRelationshipId)?.Id ?? relationShipId;
                }
            }

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            //tách số nhà và thôn xóm
            string street = request.Customer?.Street ?? string.Empty;
            string? village = null;
            if (!string.IsNullOrEmpty(street))
            {
                //start with "thôn" 
                if (street.StartsWith("thôn", StringComparison.OrdinalIgnoreCase))
                {
                    village = street;
                    street = string.Empty;
                }
                //split by ", " and take the last part as village
                else if (street.Contains(", "))
                {
                    // Split the street by ", " and take the last part as village
                    // and join the rest as street
                    // Example: "123 Main St, Thôn Xóm" -> street = "123 Main St", village = "Thôn Xóm"
                    // Example: "Thôn Xóm, 123 Main St" -> street = "123 Main St", village = "Thôn Xóm"
                    var parts = street.Split([", "], StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length > 1)
                    {
                        village = parts[^1].Trim();
                        street = string.Join(", ", parts[..^1]).Trim();
                    }
                }
            }

            var req = new CreateRegisterFormRequest()
            {
                MaBenhNhan = request.CustomerHospital.PatientCode ?? string.Empty,
                NgayDangKy = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm"),
                HoVaTen = request.Customer!.GetFullName().ToUpper(),
                NgayThangNamSinh = request.Customer!.DateOfBirth.HasValue
                    ? request.Customer.DateOfBirth.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                GioiTinh = !string.IsNullOrEmpty(gioiTinh) ? gioiTinh : (request.Customer.Sex == "Nam" ? "1" : "2"),
                DanToc = nationalityId ?? string.Empty, // Dân tộc Việt Nam theo Id HIS
                QuocTich = defaultNation?.Value ?? string.Empty, // Quốc tịch Việt Nam theo Id HIS
                SoNha = street,
                ThonPho = village ?? string.Empty,
                DienThoai = request.Customer.Phone ?? string.Empty,
                SoCMND = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                NgayCapCMND = request.Customer.IdentityIssueDate.HasValue
                    ? request.Customer.IdentityIssueDate.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                NoiCapCMND = request.Customer.IdentityIssuePlace ?? string.Empty,
                NoiLamViec = request.Customer.WorkPlace ?? string.Empty,
                MaTinhThanh = provinceId ?? string.Empty,
                MaQuanHuyen = districtId ?? string.Empty,
                MaPhuongXa = wardId ?? string.Empty,
                MaDoiTuong = request.Service.ExameTypeId ?? string.Empty,
                MaPhong = request.Service.ClinicId ?? string.Empty,
                MaNoiCap = request.IsInsurance ? (request.Insurance?.InsuranceNo.SubStringExt(2, 3) ?? string.Empty) : string.Empty,
                MaThe = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                NgayBatDau = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                NgayKetThuc = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                Ngay5Nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                MaNoiDangKy = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                DiaChiThe = request.IsInsurance ? (request.Insurance?.RegisterAddress ?? string.Empty) : string.Empty,
                MaTuyenDangKy = !string.IsNullOrEmpty(request.HealthcareServiceTierId) ? request.HealthcareServiceTierId : "1",
                LyDoKham = !string.IsNullOrEmpty(request.ReasonForVisit) ? request.ReasonForVisit : "Khám bệnh",
                MaNgheNghiep = request.CustomerHospital.CareerId ?? string.Empty,
                DieuTriNgoaiTru = "1",
                MaLyDoTC = maLyDoTC,
                MaHinhThucTT = maHinhThucTT,
                MaDoiTuongKCB = request.MedicalTreatmentCategoryId ?? string.Empty,
                SoTienTU = (int)(request.Service.UnitPrice ?? 0),
                MaQuyenTC = maQuyenTC,
                MaSinhSong = request.IsInsurance ? (request.Insurance?.AreaCode?.SubStringEndExt(1) ?? string.Empty) : string.Empty,
                MaHinhThuc = request.HealthcareServiceTypeId ?? string.Empty,
                MaGoi = request.Service.Id,
                NguoiNha = (!string.IsNullOrEmpty(request.CustomerRelationshipName) ? (request.CustomerRelationshipName + ": ") : string.Empty)
                    + request.CustomerRelationship.GetFullName(),
                NgaySinhNguoiNha = request.CustomerRelationship.DateOfBirth.HasValue
                    ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                DienThoaiNguoiNha = request.CustomerRelationship.Phone ?? string.Empty,
                DiaChiBaoTin = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                QuanHe = relationShipId,
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel data) = await httpClientFactory.CreateClient().CreateRegisterForm(req, getClinicUrl, accessToken);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, data);

            string regNo = string.Empty;
            RegisterFormResponseDto res = new();

            if (result)
            {
                regNo = !string.IsNullOrEmpty(data.SoPhieu) ? data.SoPhieu : data.MaBenhNhan;
                res = new RegisterFormResponseDto
                {
                    PatientCode = !string.IsNullOrEmpty(data?.MaBenhNhan) ? data.MaBenhNhan : (request.CustomerHospital.PatientCode ?? string.Empty),
                    Clinic = data!.TenPhong,
                    QrCode = data.QRCode,
                    QueueNumber = data.ThuTuKham,
                    QueueNumberPriority = "0",
                    RegisterNumber = regNo,
                    ReceiptRefNo = regNo,
                    PaymentRefNo = regNo,
                    ExaminationLocation = data.TenGopHDKB,
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, regNo, res);
        }
    }
}

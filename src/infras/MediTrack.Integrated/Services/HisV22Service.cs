using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Tương tự v7, nhưng field giới tính truyền ngược lại nam : 1, nữ : 2
    /// </summary>
    public class HisV22Service : HisV7Service
    {
        private readonly HisV7Config hisConfig;

        public HisV22Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV22Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            string url = $"{hisConfig.Host}/bhyt";

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var request = new GetHealthInsuranceRequest()
            {
                so_gttt = checkCode ?? string.Empty,
                loai_gttt = "CCCD",
                ho_ten = customer.GetFullName(),
                ngay_sinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                gioi_tinh = customer.Sex == "Nam" ? 1 : 2
            };

            bool result;
            string message;
            Log.Information("{LogPrefix} GetCustomerHealthInsurance Req --- {@Request}", logPrefix, request);
            (result, message, HealthInsuranceModel card) = await httpClientFactory.CreateClient().GetHealthInsurance(
                request, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Res --- Result: {Result} - Message: {Message} - {@Card}", logPrefix, result, message, card);

            if (result)
            {
                data.HealthInsuranceId = card.ma_the_bhyt;
                data.CustomerId = customer.Id;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.ma_the_bhyt ?? string.Empty;
                data.InsuranceGroupID = string.Empty;
                data.FullFiveYearDate = DateTimeHelper.FormatDate(card.ngay_du_5_nam, "yyyy-MM-dd", "dd/MM/yyyy");
                data.FromDate = DateTimeHelper.FormatDate(card.gt_the_tu, "yyyy-MM-dd", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.gt_the_den, "yyyy-MM-dd", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ma_dkbd;
                data.RoutingType = "-1";
                data.ReferralLevel = card.phan_tuyen > 0 ? card.phan_tuyen.ToString()
                    : (card.tiep_nhan_bhyt == 1 ? "1" : "2");
                data.IsCorrectRouting = card.tiep_nhan_bhyt == 1;
                data.AreaCode = card.ma_kv;
                data.Description = card.ten_kq;
                data.RegisterAddress = card.dia_chi;
                data.HisMessage = card.message ?? string.Empty;
                data.MedicalHistories = card.lich_su_kcb?.Select(x => new MedicalHistoryModel()
                {
                    RecordId = x.ma_ho_so ?? string.Empty,
                    HealthcareFacilityId = x.ma_cs_kcb ?? string.Empty,
                    AdmissionDate = x.ngay_vao ?? string.Empty,
                    DischargeDate = x.ngay_ra ?? string.Empty,
                    DiseaseName = x.ten_benh ?? string.Empty,
                    Condition = x.tinh_trang ?? string.Empty,
                    TreatmentResult = x.kq_dieu_tri ?? string.Empty,
                    AdmissionReason = x.ly_do_vv ?? string.Empty
                }).ToList() ?? [];

                //check nếu bệnh viện không cho phép khám bệnh bảo hiểm nhiều lần trong ngày
                if (data.IsCorrectRouting && current.IsBlockForDuplicatedVisitInsurance && data.MedicalHistories?.Count > 0)
                {
                    string localDay = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMdd");
                    if (data.MedicalHistories.Any(x => x.AdmissionDate.StartsWith(localDay)))
                    {
                        data.HisMessage = "Không thể đăng ký khám BHYT do đã có đăng ký trong ngày";
                        data.IsCorrectRouting = false;
                    }
                }

                result = true;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }
    }

}
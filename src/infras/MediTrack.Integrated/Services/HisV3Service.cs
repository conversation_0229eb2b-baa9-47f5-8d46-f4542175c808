﻿using HisClientV3.Lib;
using HisClientV3.Lib.Model;
using HisClientV3.Lib.Request;
using HisClientV3.Lib.Response;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Extensions;
using MediTrack.Ultils.Helpers;
using MediTrack.Domain.Helpers;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using Serilog;
using Newtonsoft.Json;
using MediTrack.Application.Helpers;
using MediTrack.Application.Services;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Domain.Enums;
using MediTrack.Application.Features.SearchLogic.Dtos;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// HPT: Đà Nẵng
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV3Service : HisServiceParameters, IHisService
    {
        private readonly HisV3Config hisConfig;


        public HisV3Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV3Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV3Config is null");
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTierDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)>((true, string.Empty, ErrorTypeEnum.NoError, []));
        }

        public Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            bool result = false;
            string message = string.Empty;
            PaymentStatusDto status = new();

            string url = $"{hisConfig.Host}/api/medipay/orderinfo";

            (result, message, CheckPaymentResponse resData) = await httpClientFactory.CreateClient().CheckPayment(url, refNo);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                status.Number = refNo;
                status.RefNo = refNo;
                status.PaymentStatus = resData.Order.ARClosed ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
                status.InvoiceInfoRef = resData.Order.InvoiceInfoRefKey ?? string.Empty;
            }

            return (result, message, ErrorTypeEnum.NoError, status);
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            bool result = false;
            string message = string.Empty;
            string regNo = string.Empty;
            RegisterFormResponseDto register = new();

            KioskModel kioskInfo = new()
            {
                UserName = hisConfig.UserName,
                Password = hisConfig.Password,
                Key = hisConfig.Key,
                KioskName = string.Empty
            };

            //1. Check thông tin bệnh nhân
            string getPatientUrl = $"{hisConfig.Host}/api/medipay/patientinfo";
            DateTime fromDate = new DateTime(2000, 01, 01);
            DateTime toDate = DateTime.UtcNow;

            // Lấy thông tin múi giờ GMT+7
            TimeZoneInfo gmtPlus7Zone = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");

            // Chuyển đổi từ UTC sang GMT+7
            DateTime gmtPlus7Time = TimeZoneInfo.ConvertTimeFromUtc(toDate, gmtPlus7Zone);

            (result, message, PatientModel patientInfo) = await httpClientFactory.CreateClient().CreatePatient(getPatientUrl, new CreatePatientRequest()
            {
                KioskInfo = kioskInfo,
                SearchType = 3,
                FromTime = fromDate.ToString("yyyyMMddHHmmss"),
                ToTime = toDate.ToString("yyyyMMddHHmmss"),
                SearchValue = request.Customer.IdentityNo ?? string.Empty,
                CheckSum = SignatureHelper.ComputeMD5Hash(request.Customer.IdentityNo + kioskInfo.Password + kioskInfo.Key)
            });

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patientInfo);

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, string.Empty, register);
            }

            string patientId = string.IsNullOrEmpty(patientInfo.PatientID) == true ? Guid.Empty.ToString() : patientInfo.PatientID;

            //2. Thực hiện đăng ký
            var ethnicId = string.IsNullOrEmpty(patientInfo.EthnicID) ? request.Customer.NationalityId! : patientInfo.EthnicID;
            string url = $"{hisConfig.Host}/api/medipay/registration";
            var req = new CreateRegisterFormRequest()
            {
                KioskInfo = kioskInfo,
                ServicePointInfo = new
                {
                    ServicePointID = request.Service.Id,
                },
                PatientInfo = new PatientModel()
                {
                    PatientID = patientId,
                    PatientCode = patientInfo.PatientCode,
                    PatientFirstName = string.IsNullOrEmpty(patientInfo.PatientFirstName) ? request.Customer.FirstName!.ToUpper() : patientInfo.PatientFirstName,
                    PatientLastName = string.IsNullOrEmpty(patientInfo.PatientLastName) ? request.Customer.LastName!.ToUpper() : patientInfo.PatientLastName,
                    ProvinceID = string.IsNullOrEmpty(patientInfo.ProvinceID) ? request.Customer.ProvinceId! : patientInfo.ProvinceID,
                    DistrictID = string.IsNullOrEmpty(patientInfo.DistrictID) ? request.Customer.DistrictId! : patientInfo.DistrictID,
                    WardID = string.IsNullOrEmpty(patientInfo.WardID) ? request.Customer.WardId! : patientInfo.WardID,
                    EthnicID = string.IsNullOrEmpty(ethnicId) ? "01" : ethnicId,
                    Address = string.IsNullOrEmpty(patientInfo.Address) ? request.Customer.Address! : patientInfo.Address,
                    Sex = request.Customer.Sex == "Nam",
                    Birthday = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyyMMddHHmmss"),
                    CCCD = request.Customer.IdentityNo ?? string.Empty,
                    CCCDQRCode = $"{request.Customer.IdentityNo}||{request.Customer.GetFullName()}|{request.Customer.DateOfBirth?.ToString("ddMMyyyy")}|{request.Customer.Sex}|{request.Customer.Address}|{request.Customer.IdentityIssueDate?.ToString("ddMMyyyy")}",
                    ExameDate = gmtPlus7Time.ToString("yyyyMMddHHmmss"),
                    Mobile = request.Customer.Phone ?? string.Empty,
                },
                VisitTypeID = request.IsInsurance ? "BHT" : "VP",
                RefDate = gmtPlus7Time.ToString("yyyyMMddHHmmss"),
                ExamType = request.IsInsurance ? 1 : 2,
                InsuaranceCard = request.IsInsurance
                    ? new InsuranceModel()
                    {
                        InsuaranceCardDetailID = request.Insurance?.HealthInsuranceId ?? string.Empty,
                        InsuaranceCardNo = request.Insurance?.InsuranceNo ?? string.Empty,
                        ResiterPlaceID = request.Insurance?.RegisterPlaceID ?? string.Empty,
                        FromDate = DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyyMMddHHmmss"),
                        ExpiredDate = DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyyMMddHHmmss"),
                        InsuaranceGroupID = request.Insurance?.InsuranceGroupID ?? string.Empty,
                        Full5YearDate = DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyyMMddHHmmss")
                    }
                    : null,
                CheckSum = SignatureHelper.ComputeMD5Hash(request.Customer.FirstName + request.Customer.LastName + kioskInfo.Password + kioskInfo.Key)
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(url, req);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                regNo = registerDto.OrderOfExame?.OrderDetails?.Count > 0
                    ? registerDto.OrderOfExame?.OrderDetails?.FirstOrDefault()?.RefID ?? string.Empty
                    : string.Empty;
                register = new RegisterFormResponseDto
                {
                    Clinic = registerDto.OrderOfExame?.OrderDetails?.FirstOrDefault()?.ItemName ?? string.Empty,
                    QrCode = registerDto.OrderOfExame?.QrCode?.Data?.QrCode ?? string.Empty,
                    QueueNumber = (registerDto.VisitInfo?.Odx ?? 0).ToString(),
                    QueueNumberPriority = "0",
                    RegisterNumber = regNo,
                    ReceiptRefNo = regNo,
                    PaymentRefNo = regNo,
                    RefDocNo = registerDto.VisitInfo?.VisitCode ?? string.Empty,
                    ExaminationLocation = registerDto.VisitInfo?.LastServicePointInfoExame?.Location ?? string.Empty,
                    RateOfInsurance = (registerDto.VisitInfo?.RateOfInsuarance ?? 0).ToString(),
                    PatientCode = registerDto.PatientInfo?.PatientCode ?? string.Empty,
                    LinkCode = registerDto.VisitInfo?.VisitCode ?? string.Empty,
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, regNo, register);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            KioskModel kioskInfo = new()
            {
                UserName = hisConfig.UserName,
                Password = hisConfig.Password,
                Key = hisConfig.Key,
                KioskName = string.Empty
            };
            string url = $"{hisConfig.Host}/api/medipay/spgrouplist";

            var req = new GetClinicRequest()
            {
                KioskInfo = kioskInfo,
                ExameTypeID = exameTypeId ?? string.Empty,
            };

            Log.Information("{LogPrefix} GetClinics Req --- {@Request}", logPrefix, req);

            (result, message, List<ClinicModel> resData) = await httpClientFactory.CreateClient().GetClinics(url, req);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new Clinic()
                {
                    Id = x.GroupCode ?? string.Empty,
                    Code = x.GroupCode ?? string.Empty,
                    Name = x.GroupName ?? string.Empty,
                    ExameTypeID = x.ExameTypeID ?? string.Empty,
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            string url = $"{hisConfig.Host}/api/medipay/patientinfo";

            KioskModel kioskInfo = new()
            {
                UserName = hisConfig.UserName,
                Password = hisConfig.Password,
                Key = hisConfig.Key,
                KioskName = string.Empty
            };
            DateTime fromDate = new(2000, 01, 01);
            DateTime toDate = DateTime.Now;

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var searchModel = new GetHealthInsuranceRequest()
            {
                IDCardNo = checkCode ?? string.Empty,
                IDCardNoOld = string.Empty,
                FullName = customer.GetFullName(),
                Birthday = customer.DateOfBirth.GetValueOrDefault().ToString("ddMMyyyy"),
                Sex = customer.Sex ?? string.Empty,
                Address = customer.Address ?? string.Empty,
                DateOfIssuance = customer.IdentityIssueDate.GetValueOrDefault().ToString("ddMMyyyy")
            };
            string searchValue = searchModel.ToBase64();

            bool result;
            string message;

            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().CreatePatient(url,
                new CreatePatientRequest()
                {
                    KioskInfo = kioskInfo,
                    SearchType = 5,
                    FromTime = fromDate.ToString("yyyyMMddHHmmss"),
                    ToTime = toDate.ToString("yyyyMMddHHmmss"),
                    SearchValue = searchValue,
                    CheckSum = SignatureHelper.ComputeMD5Hash(searchValue + hisConfig.Password + hisConfig.Key)
                });

            if (result &&
                patient.CurrentOfInsuaranceCard != null &&
                patient.CurrentOfInsuaranceCard.ResultNo == "000")
            {
                var card = patient.CurrentOfInsuaranceCard;

                data.HealthInsuranceId = card.InsuaranceCardDetailID;
                data.CustomerId = customer.Id;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.InsuaranceCardNo ?? string.Empty;
                data.InsuranceGroupID = card.InsuaranceGroupID ?? string.Empty;
                data.FromDate = DateTimeHelper.FormatDate(card.FromDate, "yyyyMMddHHmmss", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.ExpiredDate, "yyyyMMddHHmmss", "dd/MM/yyyy");
                data.FullFiveYearDate = DateTimeHelper.FormatDate(card.Full5YearDate, "yyyyMMddHHmmss", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ResiterPlaceID;
                data.RoutingType = "-1";
                data.ReferralLevel = card.ResiterPlaceID == "48001" ? "1" : "2";
                data.IsCorrectRouting = card.ResiterPlaceID == "48001";
                data.Description = card.Description;

                result = true;
            }
            else if (result && patient.CurrentOfInsuaranceCard != null &&
                patient.CurrentOfInsuaranceCard.ResultNo != "000")
            {
                message = patient.CurrentOfInsuaranceCard.Description ?? string.Empty;
                return (result, message, ErrorTypeEnum.HisError, data);
            }

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/api/medipay/exametypelist";

            (result, message, List<ExamTypeModel> resData) = await httpClientFactory.CreateClient().GetExameTypes(url);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData.Select(x => new ExameType()
                {
                    Id = x.ExameTypeID.ToString(),
                    Name = x.ExameTypeName ?? string.Empty,
                    IsInsurance = x.IsInsuarance,
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            KioskModel kioskInfo = new()
            {
                UserName = hisConfig.UserName,
                Password = hisConfig.Password,
                Key = hisConfig.Key,
                KioskName = string.Empty
            };

            string url = $"{hisConfig.Host}/api/medipay/splist";
            (result, message, List<HealthServiceModel> resData) = await httpClientFactory.CreateClient().GetHealthServices(url, new GetHealthServicesRequest()
            {
                KioskInfo = kioskInfo,
                ExameTypeID = request.ExameTypeId,
                GroupCode = request.ClinicId,
            });

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new HealthService()
                {
                    Id = x.ServicePointID ?? string.Empty,
                    Name = x.ServicePointName ?? string.Empty,
                    UnitPrice = x.ItemPrice,
                    UnitPriceDisplay = x.ItemPrice.ToString("N0") + " đ",
                    InsurancePrice = x.InsuaranceItemPrice,
                    ExameTypeId = request.ExameTypeId,
                    ClinicId = x.ServicePointGroup?.GroupCode ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    InsurancePriceDisplay = x.InsuaranceItemPrice.ToString("N0") + " đ",
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;

            string url = $"{hisConfig.Host}/api/medipay/patientinfo";

            KioskModel kioskInfo = new()
            {
                UserName = hisConfig.UserName,
                Password = hisConfig.Password,
                Key = hisConfig.Key,
                KioskName = string.Empty
            };

            DateTime fromDate = new(2000, 01, 01);
            DateTime toDate = DateTime.Now;

            bool result;
            string message;
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().CreatePatient(url,
                new CreatePatientRequest()
                {
                    KioskInfo = kioskInfo,
                    SearchType = 3,
                    FromTime = fromDate.ToString("yyyyMMddHHmmss"),
                    ToTime = toDate.ToString("yyyyMMddHHmmss"),
                    SearchValue = customer.IdentityNo ?? string.Empty,
                    CheckSum = SignatureHelper.ComputeMD5Hash(customer.IdentityNo + kioskInfo.Password + kioskInfo.Key)
                });

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient.PatientID;
                patientCodeResponse = patient.PatientID;
                careerId = patient.OccupationCode;
            }

            return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto
            {
                PatientCode = patientCodeResponse,
                PatientId = patientId,
                CareerId = careerId
            });
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)>((true, string.Empty, ErrorTypeEnum.NoError, []));
        }

        public Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, string[]?)>((true, string.Empty, ErrorTypeEnum.NoError, null));
        }

        public Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()));
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }
        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

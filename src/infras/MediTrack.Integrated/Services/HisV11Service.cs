﻿using HisClientV11.Lib.Model;
using HisClientV11.Lib.Request;
using HisClientV11.Lib;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Luồng chung: VietSen
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV11Service : HisV7Service
    {
        private readonly HisV11Config hisConfig;


        public HisV11Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV11Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV11Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Address ?? string.Empty,
                    ma_dantoc = request.Customer.NationalityId ?? string.Empty,
                    ma_quoctich = request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 2 : 1,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    email = string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = request.Customer.CareerId ?? string.Empty,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ma_ly_do_vnt = string.Empty,
                    ly_do_vnt = "Khám bệnh",
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = !string.IsNullOrEmpty(request.MedicalTreatmentCategoryId) ? request.MedicalTreatmentCategoryId : "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    anh_bn_cccd = request.Customer.Image ?? string.Empty,
                    ma_cskcb = request.IsInsurance ? request.Insurance?.RegisterPlaceID ?? string.Empty : string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    id_khung_thoi_gian = string.Empty,
                    ma_dich_vu = request.Service.Code ?? string.Empty
                },
                du_phong = string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                bn_uu_tien = request.Priority,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = "0",
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = string.Empty,
                    ExaminationLocation = string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = null, // Remove thoi_gian_kham_dk as it's not in new response
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = true;
            string message = string.Empty;
            List<Clinic> list = [];
            var exameTypes = await cachedService.GetAsync<List<ExameTypeModel>>("ExameTypes");
            if (exameTypes is null)
            {
                string url = $"{hisConfig.Host}/dich-vu-tree";
                (result, message, exameTypes)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);
                await cachedService.SetAsync("ExameTypes", exameTypes);

            }
            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, exameTypes);
            if (result)
            {
                list = [.. exameTypes!.Where(x => (string.IsNullOrEmpty(exameTypeId) || x.id_loai_kham == exameTypeId) && x.khoa != null)
                            .SelectMany(x => x.khoa)
                            .Select(x => new Clinic()
                            {
                                Id = x.id_khoa ?? string.Empty,
                                Code = !string.IsNullOrEmpty(x.ma_khoa) ? x.ma_khoa : (x.id_khoa ?? string.Empty),
                                Name = x.ten_khoa ?? string.Empty,
                                ExameTypeID = exameTypeId ?? string.Empty,
                                // Add sub-clinics (phong_kham) as children
                                Children = x.phong_kham?.Select(pk => new Clinic()
                                {
                                    Id = pk.id_phong_kham ?? string.Empty,
                                    Code = pk.ma_khoa ?? string.Empty,
                                    Name = pk.ten_phong_kham ?? string.Empty,
                                    ExameTypeID = x.id_khoa ?? string.Empty,
                                }).ToList() ?? []
                            })];
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/dich-vu-tree";
            (result, message, List<ExameTypeModel> resData)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData.Select(x => new ExameType()
                {
                    Id = x.id_loai_kham.ToString(),
                    Name = x.ten_loai_kham ?? string.Empty,
                    IsInsurance = (x.ten_loai_kham ?? string.Empty).Contains("BẢO HIỂM", StringComparison.CurrentCultureIgnoreCase)
                        || (x.ten_loai_kham ?? string.Empty).Contains("BHYT", StringComparison.CurrentCultureIgnoreCase),
                }).ToList();
            }
            await cachedService.SetAsync("ExameTypes", resData);
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = true;
            string message = string.Empty;
            List<HealthService> list = [];
            var exameTypes = await cachedService.GetAsync<List<ExameTypeModel>>("ExameTypes");
            if (exameTypes is null)
            {
                string url = $"{hisConfig.Host}/dich-vu-tree";
                (result, message, exameTypes)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);
                await cachedService.SetAsync("ExameTypes", exameTypes);
            }
            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, exameTypes);
            if (result)
            {
                var priceDisplay = await databaseService.HospitalMetaDatas
                    .Where(x => x.HospitalId == current.HospitalId
                    && x.GroupType == "price_display_default"
                    && (x.Code == "INSURANCE" || x.Code == "SERVICE")).ToListAsync();

                var servicePriceDisplay = priceDisplay.Find(x => x.Code == "SERVICE");
                var insurancePriceDisplay = priceDisplay.Find(x => x.Code == "INSURANCE");
                
                // Now DICH_VU (services) are nested inside PHONG_KHAM (sub-clinics)
                list = [.. exameTypes!.Where(x => (string.IsNullOrEmpty(request.ExameTypeId) || x.id_loai_kham == request.ExameTypeId) && x.khoa != null)
                            .SelectMany(x => x.khoa)
                            .Where(x => (string.IsNullOrEmpty(request.ClinicId) || x.id_khoa == request.ClinicId) && x.phong_kham != null)
                            .SelectMany(x => x.phong_kham)
                            .Where(x => (string.IsNullOrEmpty(request.SubClinicId) || x.id_phong_kham == request.SubClinicId) && x.dich_vu != null)
                            .SelectMany(pk => pk.dich_vu.Select(dv => new HealthService()
                            {
                                Id = dv.ma_dich_vu ?? string.Empty,
                                Code = dv.ma_dich_vu ?? string.Empty,
                                Name = dv.ten_dich_vu ?? string.Empty,
                                UnitPrice = dv.don_gia_phong_kham,
                                UnitPriceDisplay = servicePriceDisplay?.Value ?? (dv.don_gia_phong_kham.ToString("N0") + " đ"),
                                InsurancePrice = dv.don_gia_bhyt,
                                InsurancePriceDisplay = insurancePriceDisplay?.Value ?? ((dv.don_gia_bhyt + dv.don_gia_thu_them).ToString("N0") + " đ"),
                                ExtraPrice = dv.don_gia_thu_them,
                                IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                                ExameTypeId = pk.id_loai_kham ?? string.Empty,
                                ClinicId = request.ClinicId ?? string.Empty,
                                SubClinicId = pk.id_phong_kham ?? string.Empty,
                                ClinicCode = request.ClinicCode ?? string.Empty,
                            }))];
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }


        public override async Task<(bool, string, ErrorTypeEnum, HisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResult = string.Empty;
            string careerId = string.Empty;

            string url = $"{hisConfig.Host}/benhnhan";

            string message;
            bool result;
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(
                new GetPatientRequest()
                {
                    so_gttt = customer.IdentityNo ?? string.Empty,
                    loai_gttt = requestType,
                    dien_thoai = customer.Phone ?? string.Empty
                }, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient?.id_bn ?? string.Empty;
                patientCodeResult = patient?.ma_bn ?? string.Empty;
                careerId = patient?.ma_nghe_nghiep ?? string.Empty;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new HisCustomerDto
            {
                PatientId = patientId,
                PatientCode = patientCodeResult,
                CareerId = careerId
            });
        }
    }
}

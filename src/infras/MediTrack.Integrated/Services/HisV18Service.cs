﻿using HisClientV6.Lib;
using HisClientV6.Lib.Model;
using HisClientV6.Lib.Request;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Extensions;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// MQ: luồng gửi đăng ký trước khi thanh toán
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV18Service : HisV6Service
    {
        private readonly HisV6Config hisConfig;

        public HisV18Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV6Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV18Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(
            RegisterFormRequestDto req)
        {
            //1. Tự động sinh phiếu do hệ thống phải tích hợp thanh toán --> đăng ký
            bool result;
            string refNo = IdentityHelper.Guid(15) ?? string.Empty;
            string message;
            var customerHospital = req.CustomerHospital;

            (string hisProvinceId, string hisDistrictId, string hisWardId) =
                await GetAddressId(req.Customer.WardId ?? string.Empty,
                                   req.Customer!.DistrictId ?? string.Empty,
                                   req.Customer!.ProvinceId ?? string.Empty);

            RegisterFormModel response;
            string url = $"{hisConfig.Host}/Booking/KiosPostBooking_Unpaid";
            var request = new CreateRegisterFormRequest()
            {
                ID = 0,
                Transaction_code_gd = string.Empty,
                Transaction_code_tt = string.Empty,
                Booking_date = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                Doctor_id = null,
                Subject_id = req.Service.ClinicId.ToInt(),
                Room_id = req.Service.Id.ToInt(),
                Patient_name = req.Customer.LastName,
                Patient_surname = req.Customer.FirstName,
                Birthyear = req.Customer!.DateOfBirth!.Value.Year,
                Birthdate = req.Customer!.DateOfBirth!.Value.ToString("yyyyMMdd"),
                Social_id = req.Customer!.IdentityNo,
                Mobile = req.Customer!.Phone,
                Patient_code = customerHospital.PatientCode ?? string.Empty,
                Country_code = "VN",
                City_id = hisProvinceId.ToInt(),
                District_id = hisDistrictId.ToInt(),
                Ward_id = hisWardId.ToInt(),
                Address = req.Customer!.Address,
                Sex = req.Customer.Sex == "Nam" ? 0 : 1,
                Booking_number = 0,
                Bv_time = DateTimeHelper.GetCurrentLocalDateTime().ToString("HH:mm"),
                Amount = req.PaymentAmount ?? 0,
                amount_original = 0,
                amount_gate = 0,
                Status = 99,
                IsBHYT = int.TryParse(req.Service.ExameTypeId, out int exameTypeId) ? exameTypeId : 0,
                Date_create = req.RegisterTime.ToString("yyyyMMddHHmm"),
                Date_update = req.RegisterTime.ToString("yyyyMMddHHmm"),
                PAYMENTCODE = string.Empty,
                Note = string.Empty
            };
            Log.Information("{logPrefix} Call CreateRegisterForm Req {@Request}", logPrefix, request);
            (result, message, response) = await
                    httpClientFactory.CreateClient().CreateRegisterForm(url, request, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call CreateRegisterForm Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, response);

            var data = new RegisterFormResponseDto
            {
                Clinic = req.Service.ClinicId ?? string.Empty,
                QrCode = string.Empty,
                QueueNumber = response.booking_number != 0 ? response.booking_number.ToString() : string.Empty,
                QueueNumberPriority = "0",
                RegisterNumber = string.IsNullOrEmpty(response.BookingID) ? refNo : response.BookingID,
                ReceiptRefNo = string.IsNullOrEmpty(response.BookingID) ? refNo : response.BookingID,
                PaymentRefNo = string.Empty,
                RefDocNo = string.Empty,
                ExaminationLocation = req.Service.ExaminationLocation ?? string.Empty,
                RateOfInsurance = "0",
                PatientId = customerHospital.PatientId ?? string.Empty,
                PatientCode = customerHospital.PatientCode ?? string.Empty,
                ExpectedAppointmentAt = null,
            };
            refNo = data.RegisterNumber;
            Log.Information("{LogPrefix} CreateRegisterForm Res --- {@Response}", logPrefix, data);

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            bool result;
            string message;
            CustomerHealthInsurance data = new();

            (result, message, ErrorTypeEnum errorType, HisCustomerDto patient) = await GetCustomerHis(customer);

            if (!result)
            {
                return (result, message, errorType, data);
            }

            string checkInsuranceUrl = $"{hisConfig.Host}/Patients/PostPatients_checkbh";
            var checkInsuranceReq = new CheckHealthInsuranceRequest()
            {
                ID = patient.PatientId,
                Ngay = DateTime.Now.ToString("yyyyMMdd")
            };

            Log.Information("{logPrefix} Call CheckHealthInsurance Req {@Request}", logPrefix, checkInsuranceReq);

            (result, message, HealthInsuranceModel insurance) = await
                httpClientFactory.CreateClient().CheckHealthInsurance(checkInsuranceUrl, checkInsuranceReq, hisConfig.EncryptKey, hisConfig.DecryptKey);

            Log.Information("{logPrefix} Call CheckHealthInsurance Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, insurance);

            if (result)
            {
                var metadata = hospitalMetadataRepository is not null
                    ? await hospitalMetadataRepository.GetInsuranceErrorMessageConfig(current.HospitalId ?? string.Empty) 
                    : [];

                data.HealthInsuranceId = insurance.maThe ?? string.Empty;
                data.Description = metadata.TryGetValue(insurance.maKetQua ?? string.Empty, out string? errorMessage) ? insurance.maKetQua + ": " + errorMessage : insurance.maKetQua + ": " + insurance.note ?? string.Empty;
                data.IsCorrectRouting = insurance.tiep_nhan_bhyt == 1;
                data.ExpiredDate = insurance.gtTheDen ?? string.Empty;
                data.FromDate = insurance.gtTheTu ?? string.Empty;
                data.RegisterAddress = insurance.cqBHXH ?? string.Empty;
                data.InsuranceNo = insurance.maThe ?? string.Empty;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.RegisterPlaceID = !string.IsNullOrEmpty(insurance.maDKBDMoi) ? insurance.maDKBDMoi : insurance.maDKBD ?? string.Empty;
                data.RegisterPlaceName = !string.IsNullOrEmpty(insurance.tenDKBDMoi) ? insurance.tenDKBDMoi : string.Empty;
                data.FullFiveYearDate = insurance.ngayDu5Nam ?? string.Empty;
                data.RoutingType = "-1";
                data.ReferralLevel = data.IsCorrectRouting ? "1" : "2";
                data.CustomerId = customer.Id;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/danhmuc/getdoituong";
            (bool getResult, message, List<CategoryModel> categoryModels) = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{LogPrefix} GetExameTypes --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, message, categoryModels);

            var exameTypes = categoryModels
                .Where(x => !string.IsNullOrEmpty(x.madoituong))
                .Select(x => new ExameType
                {
                    Id = x.madoituong,
                    Name = x.doituong,
                    IsInsurance = x.fielD_GIA == "gia_bh",
                }).ToList() ?? [];
            return (getResult, message, getResult ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, exameTypes);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            string getHealthServiceUrl = $"{hisConfig.Host}/Rooms/GetRooms";

            (result, message, List<HealthServiceModel> data) = await
                    httpClientFactory.CreateClient().GetHealthService(getHealthServiceUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);

            if (result)
            {
                string url = $"{hisConfig.Host}/danhmuc/getdoituong";
                (bool getResult, message, List<CategoryModel> categoryModels) = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{LogPrefix} GetExameTypes --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, message, categoryModels);

                if (!getResult)
                {
                    return (getResult, message, ErrorTypeEnum.HisError, list);
                }
                var isThuPhi = categoryModels.Any(x => x.madoituong == request.ExameTypeId && x.fielD_GIA != "gia_dv");
                list = data
                .Where(x => x.subject_id == request.ClinicId)
                .Select(x => new HealthService()
                {
                    Id = x.id.ToString(),
                    Name = x.name,
                    ExameTypeId = request.ExameTypeId ?? string.Empty,
                    UnitPrice = isThuPhi ? x.price_tp : x.price_dv,
                    UnitPriceDisplay = (isThuPhi ? x.price_tp : x.price_dv).ToString("N0") + " đ",
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    ExaminationLocation = string.IsNullOrEmpty(x.room_number) ? null : x.room_number,
                    InsurancePrice = x.price_bh,
                    InsurancePriceDisplay = x.price_bh.ToString("N0") + " đ",
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                }).ToList();
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            bool result;
            string message;

            (string hisProvinceId, string hisDistrictId, string hisWardId) =
                await GetAddressId(customer.WardId ?? string.Empty,
                                   customer!.DistrictId ?? string.Empty,
                                   customer!.ProvinceId ?? string.Empty);

            //1. Lấy thông tin bệnh nhân
            string getPatientUrl = $"{hisConfig.Host}/Patients/GetPatientsByMobile";
            var req = new GetPatientRequest()
            {
                social_id = customer.IdentityNo ?? string.Empty,
                mobile = customer.Phone ?? string.Empty,
                patientName = customer.GetFullName().ToUpper() ?? string.Empty,
                patientbirthday = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy")
            };
            Log.Information("{logPrefix} Call GetCustomerHis Req {@Req} ", logPrefix, req);
            (result, message, PatientModel patient) = await
                httpClientFactory.CreateClient().GetPatient(req, getPatientUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call GetCustomerHis Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, patient);

            if (!result || patient.Hide == 1 || string.IsNullOrEmpty(patient.id))
            {
                //2. Tạo mới nếu không có
                string postPatientUrl = $"{hisConfig.Host}/Patients/PostPatients";
                var request = new PostPatientRequest()
                {
                    name = customer.LastName ?? string.Empty,
                    surname = customer.FirstName ?? string.Empty,
                    sex = customer.Sex == "Nam" ? 0 : 1,
                    birthyear = customer.DateOfBirth.GetValueOrDefault().Year,
                    birthdate = customer.DateOfBirth.GetValueOrDefault().ToString("yyyyMMdd"),
                    mobile = customer.Phone ?? string.Empty,
                    social_id = customer.IdentityNo,
                    job_id = string.Empty,
                    country_code = "VN",
                    city_id = hisProvinceId ?? string.Empty,
                    district_id = hisDistrictId ?? string.Empty,
                    ward_id = hisWardId ?? string.Empty,
                    address = customer.Address ?? string.Empty,
                    tiep_nhan_bhyt = 1
                };

                Log.Information("{logPrefix} Call PostCustomerHis Req {@Request}", logPrefix, request);
                (result, message, patient) = await
                    httpClientFactory.CreateClient().CreatePatient(request, postPatientUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{logPrefix} Call PostCustomerHis Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, patient);
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new HisCustomerDto()
            {
                PatientCode = patient.id,
                PatientId = patient.id
            });
        }

        public override async Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();
            try
            {
                var register = await databaseService.Registers
                                        .Include(x => x.Customer)
                                        .FirstOrDefaultAsync(x => x.Number == request.RegisterNumber);
                if (register == null)
                {
                    return (false, string.Empty, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
                }

                (string hisProvinceId, string hisDistrictId, string hisWardId) =
                await GetAddressId(register.Customer!.WardId ?? string.Empty,
                                   register.Customer!.DistrictId ?? string.Empty,
                                   register.Customer!.ProvinceId ?? string.Empty);

                string url = $"{hisConfig.Host}/Booking/KiosPostBooking_Unpaid";
                var req = new CreateRegisterFormRequest()
                {
                    ID = register.RefNo.ToLong(),
                    Transaction_code_gd = request.TransactionId,
                    Transaction_code_tt = request.PaidTime!.Value.AddHours(7).ToString("dd/MM/yyyy HH:mm:ss"),
                    Booking_date = request.PaidTime!.Value.AddHours(7).ToString("yyyyMMddHHmm"),
                    Doctor_id = null,
                    Subject_id = register!.ClinicId.ToInt(),
                    Room_id = register!.HealthServiceId.ToInt(),
                    Patient_name = register.Customer!.LastName,
                    Patient_surname = register.Customer!.FirstName,
                    Birthyear = register.Customer!.DateOfBirth!.Value.Year,
                    Birthdate = register.Customer!.DateOfBirth!.Value.ToString("yyyyMMdd"),
                    Social_id = register.Customer!.IdentityNo,
                    Mobile = register.Customer!.Phone,
                    Patient_code = request.PatientCode,
                    Country_code = "VN",
                    City_id = hisProvinceId.ToInt(),
                    District_id = hisDistrictId.ToInt(),
                    Ward_id = hisWardId.ToInt(),
                    Address = register.Customer!.Address,
                    Sex = register.Customer!.Sex == "Nam" ? 0 : 1,
                    Booking_number = register.QueueNumber.ToInt(),
                    Bv_time = register.RegisterAt.GetValueOrDefault().AddHours(7).ToString("HH:mm"),
                    Amount = request.PaidAmount,
                    amount_original = 0,
                    amount_gate = 0,
                    Status = 1,
                    IsBHYT = register.ExameTypeId.ToInt(),
                    Date_create = register.RegisterAt?.AddHours(7).ToString("yyyyMMddHHmm"),
                    Date_update = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                    PAYMENTCODE = request.InvoiceId,
                    Note = request.TransactionDescription
                };
                Log.Information("{logPrefix} Call UpdatePaymentStatus Req {@Request}", logPrefix, req);
                (bool boolResult, string Message, RegisterFormModel response) = await
                        httpClientFactory.CreateClient().CreateRegisterForm(url, req, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{logPrefix} Call UpdatePaymentStatus Res {Result} - Message {Message} - Response {@Response}", logPrefix, boolResult, Message, response);

                return (boolResult, string.Empty, boolResult ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new UpdatePaymentStatusResponse()
                {
                    IpnResult = boolResult,
                    IpnMessage = Message,
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{logPrefix} UpdatePaymentStatus Error", logPrefix);
                return (false, ex.Message, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
            }
        }
    }
}
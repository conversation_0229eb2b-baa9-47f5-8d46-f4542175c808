﻿using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Luồng chung: FPT (Đức Giang) - thêm mã mối quan hệ khi truyền qua HIS thay vì truyền tên mối quan hệ
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV16Service : HisV7Service
    {
        private readonly HisV7Config hisConfig;

        public HisV16Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV16Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;
            var doiTuongKcb = request.MedicalTreatmentCategoryId;

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcb = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                : null;

                doiTuongKcb = defaultDoiTuongKcb?.Value;
            }

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                    : null;

                if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                {
                    var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                    if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                    {
                        doiTuongKcb = doiTuongKCBMap.ContainsKey(request.Service.ExameTypeId ?? string.Empty)
                            ? doiTuongKCBMap[request.Service.ExameTypeId ?? string.Empty] : "1";
                    }
                }
            }

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var relationShipId = request.CustomerRelationshipId ?? string.Empty;
            var mapRelationShips = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "id_quan_he_nt_map")
                : null;

            if (mapRelationShips is not null && !string.IsNullOrEmpty(mapRelationShips.Value))
            {
                var relationShips = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(mapRelationShips.Value);
                if (relationShips is not null && relationShips.Count > 0)
                {
                    relationShipId = relationShips.FirstOrDefault(x => x.OriginId == request.CustomerRelationshipId)?.Id ?? relationShipId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    macutru_cap1 = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    macutru_cap2 = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = careerId,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    ly_do_vv = request.ReasonForVisit ?? string.Empty,
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = doiTuongKcb ?? "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = relationShipId ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                    phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                    cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                    cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                    cs_nhiet_do = request.Temperature.ToString() ?? string.Empty,
                    cs_mach = request.PulseRate.ToString() ?? string.Empty,
                    uu_tien = request.Priority,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    ma_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_nhom_phong_kham = request.Service.ClinicGroupId ?? string.Empty,
                    ma_nhom_phong_kham = request.Service.ClinicGroupCode ?? string.Empty,
                    ten_nhom_phong_kham = request.Service.ClinicGroup ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                bn_dichvu = !request.IsInsurance
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = registerDto.thong_tin_benh_nhan.uu_tien?.ToString() ?? request.Priority.ToString(),
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RateOfInsurance = "0",
                    ExaminationLocation = registerDto.thong_tin_dang_ky.ten_khu_vuc ?? string.Empty,
                    MedicalTreatmentCategoryName = registerDto.thong_tin_dang_ky.ten_doi_tuong ?? string.Empty,
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthPackage> list = [];
            var hospitalMetadata = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "health_packages")
                : null;

            if (hospitalMetadata != null)
            {
                var packages = JsonConvert.DeserializeObject<List<HealthPackage>>(hospitalMetadata.Value ?? string.Empty) ?? [];
                string url = $"{hisConfig.Host}/goi-ksk";

                var task = packages
                    .Where(x => string.IsNullOrEmpty(packageTypeId) || x.HealthPackageTypeId == packageTypeId)
                    .Select(async x =>
                    {
                        var req = new GetHealthPackageRequest()
                        {
                            id_loai_kham = x.Id ?? string.Empty
                        };
                        Log.Information("{LogPrefix} GetHealthPackages Req --- {@Request}", logPrefix, req);

                        (result, message, HisClientV7.Lib.Model.HealthPackageModel resData) =
                            await httpClientFactory.CreateClient().GetHealthPackageforV16(req, url, hisConfig.MerchantId, hisConfig.SecretKey);
                        Log.Information("{LogPrefix} GetHealthPackages Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);
                        if (!result)
                        {
                            Log.Error("{LogPrefix} GetHealthPackages Res --- Result: {Result} - Message: {Message}", logPrefix, result, message);
                            return null;
                        }
                        return new HealthPackage()
                        {
                            Id = resData.id_loai_kham ?? string.Empty,
                            Code = !string.IsNullOrEmpty(resData.ma_goi_kham) ? resData.ma_goi_kham : (resData.id_goi_kham ?? string.Empty),
                            Name = resData.ten_goi_kham ?? string.Empty,
                            HealthPackageTypeId = packageTypeId ?? string.Empty,
                            UnitPrice = resData.don_gia,
                            UnitPriceDisplay = resData.don_gia.ToString("N0") + " đ",
                            Description = resData.mo_ta ?? string.Empty,
                            AgeRange = resData.do_tuoi ?? string.Empty,
                            UserType = resData.doi_tuong_sd ?? string.Empty,
                            ExpirationDate = resData.han_dung ?? string.Empty,
                            UserCount = resData.sl_su_dung ?? string.Empty,
                            HealthPackageServices = resData.dich_vu_kham?.Select(x => new HealthPackageService()
                            {
                                Id = x.id_dich_vu ?? string.Empty,
                                Code = x.ma_dich_vu ?? string.Empty,
                                Name = x.ten_dich_vu ?? string.Empty,
                                UnitPrice = x.don_gia ?? 0,
                                UnitPriceDisplay = (x.don_gia ?? 0).ToString("N0") + " đ",
                                Description = x.noi_dung ?? string.Empty,
                                SortIndex = x.thu_tu_sap_xep
                            }).ToList() ?? []
                        };
                    });
                packages = [.. await Task.WhenAll(task)];
                list = [.. packages.OrderBy(x => x.Name)];
                result = true;
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormPackageResponseDto data = new();

            bool result;
            string message;

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string url = $"{hisConfig.Host}/dangky-goi-ksk";
            var req = new CreateRegisterForForPackageRequest()
            {
                thong_tin_benh_nhan = new PatientRequestModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_nghe_nghiep = careerId,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    anh_bn_cccd = request.Customer.Image ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                },
                thong_tin_dich_vu = new HisClientV7.Lib.Request.HealthPackageModel()
                {
                    id_goi_kham = request.HealthPackage.Id ?? string.Empty,
                    ma_goi_kham = request.HealthPackage.Code ?? string.Empty,
                    ten_goi_kham = request.HealthPackage.Name ?? string.Empty,
                    don_gia = request.HealthPackage.UnitPrice,
                    chi_tiet_dich_vu = request.HealthPackage.HealthPackageServices?.Select(x => new HealthPackageServiceModel()
                    {
                        id_dich_vu = x.Id ?? string.Empty,
                        ma_dich_vu = x.Code ?? string.Empty,
                        ten_dich_vu = x.Name ?? string.Empty,
                        don_gia = x.UnitPrice
                    }).ToList() ?? [],
                    dich_vu_ky_thuat = request.TechnicalServices?.Select(x => new TechnicalServiceModel()
                    {
                        id_dich_vu = x.Id ?? string.Empty,
                        ten_dich_vu = x.Name ?? string.Empty,
                        don_gia = x.UnitPrice,
                        so_luong = x.Quantity
                    }).ToList() ?? [],

                },
                id_loai_kham = string.Empty,

            };

            Log.Information("{LogPrefix} CreateRegisterFormforHealthPackage Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterFormForHealthPackage(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterFormforHealthPackage Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                data = new RegisterFormPackageResponseDto
                {
                    RegisterNumber = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan?.stt_lk.ToString() ?? string.Empty,
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }
    }
}
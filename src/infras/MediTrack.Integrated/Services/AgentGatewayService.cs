﻿using AgentGateway.Lib;
using AgentGateway.Lib.Config;
using AgentGateway.Lib.Requests;
using AgentGateway.Lib.Responses;
using MediTrack.Application.Services;
using MediTrack.Domain.Enums;
using Microsoft.Extensions.Options;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;

namespace MediTrack.Integrated.Services
{
    public class AgentGatewayService(IOptions<AgentGatewayConfigModel> configuration, IHttpClientFactory httpClientFactory) : IAgentGatewayService
    {
        private readonly AgentGatewayConfigModel agentGatewayConfig = configuration.Value ?? throw new NullReferenceException(nameof(AgentGatewayConfigModel));

        public async Task<(bool, string, ErrorTypeEnum, InitTransactionResponseDto)> InitTransaction(InitTransactionRequest request)
        {
            string url = $"{agentGatewayConfig.Host}/api/vneids/init?code=GOTRUST";
            var (success, message, data) = await httpClientFactory.CreateClient().InitTransaction(request, url);
            var responseDto = new InitTransactionResponseDto
            {
                TxnId = data?.TxnId,
                RequestId = data?.RequestId
            };
            return (success, message, success ? ErrorTypeEnum.NoError : ErrorTypeEnum.MediPayError, responseDto);
        }

        public async Task<(bool, string, ErrorTypeEnum, GetTransactionResponse)> GetTransaction(GetTransactionRequest request)
        {
            string url = $"{agentGatewayConfig.Host}/api/vneids/verify?code=GOTRUST";
            var (success, message, data) = await httpClientFactory.CreateClient().GetTransaction(request, url);
            var responseDto = new GetTransactionResponse
            {
                Message = data?.Message,
                BiometricResult = data?.BiometricResult,
                UserInfo = data?.UserInfo,
                Success = data?.Success ?? false,
            };
            return (success, message, success ? ErrorTypeEnum.NoError : ErrorTypeEnum.MediPayError, responseDto);
        }

        public async Task<(bool, string, ErrorTypeEnum, UserSharedResponseDto)> RequestUserShared(UserSharedRequest request)
        {
            string url = $"{agentGatewayConfig.Host}/share/user";
            var (success, message, data) = await httpClientFactory.CreateClient().UserShared(request, url);
            var responseDto = new UserSharedResponseDto
            {
                ResultCode = data?.ResultCode,
                ResultDesc = data?.ResultDesc
            };
            return (success, message, success ? ErrorTypeEnum.NoError : ErrorTypeEnum.MediPayError, responseDto);
        }
    }
}
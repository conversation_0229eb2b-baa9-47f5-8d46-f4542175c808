﻿using HisClientV6.Lib;
using HisClientV6.Lib.Model;
using HisClientV6.Lib.Request;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Extensions;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// MQ: Quận 7, Buôn Ma Thuộc
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV6Service : HisServiceParameters, IHisService
    {
        private readonly HisV6Config hisConfig;

        public HisV6Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV6Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV6Config is null");
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTierDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)>((true, string.Empty, ErrorTypeEnum.NoError, []));
        }

        public Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            var payment = await databaseService.Payments
                            .FirstOrDefaultAsync(x => x.RefNo == refNo && x.HospitalId == current.HospitalId);

            if (payment is null)
            {
                return (false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError, new PaymentStatusDto());
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, new PaymentStatusDto()
            {
                Number = payment.Id ?? string.Empty,
                PaymentStatus = payment.Status ?? PaymentConstant.WaitForPayment,
                InvoiceInfoRef = payment.InvoiceInfoRef ?? string.Empty,
                RefNo = payment.RefNo ?? string.Empty
            });
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(
            RegisterFormRequestDto req)
        {
            //1. Tự động sinh phiếu do hệ thống phải tích hợp thanh toán --> đăng ký
            bool result = true;
            string refNo = IdentityHelper.Guid(15) ?? string.Empty;
            string message = string.Empty;
            var customerHospital = req.CustomerHospital;

            (string hisProvinceId, string hisDistrictId, string hisWardId) =
                await GetAddressId(req.Customer.WardId ?? string.Empty,
                                   req.Customer!.DistrictId ?? string.Empty,
                                   req.Customer!.ProvinceId ?? string.Empty);

            if (string.IsNullOrEmpty(req.CustomerHospital.PatientId))
            {
                //2. Tạo mới nếu không có
                string postPatientUrl = $"{hisConfig.Host}/Patients/PostPatients";
                var request = new PostPatientRequest()
                {
                    name = req.Customer.LastName ?? string.Empty,
                    surname = req.Customer.FirstName ?? string.Empty,
                    sex = req.Customer.Sex == "Nam" ? 0 : 1,
                    birthyear = req.Customer.DateOfBirth.GetValueOrDefault().Year,
                    birthdate = req.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyyMMdd"),
                    mobile = req.Customer.Phone ?? string.Empty,
                    social_id = req.Customer.IdentityNo,
                    job_id = req.CustomerHospital.CareerId ?? string.Empty,
                    country_code = "VN",
                    city_id = hisProvinceId ?? string.Empty,
                    district_id = hisDistrictId ?? string.Empty,
                    ward_id = hisWardId ?? string.Empty,
                    address = req.Customer.Address ?? string.Empty,
                };

                Log.Information("{logPrefix} Call PostCustomerHis Req {@Request}", logPrefix, request);
                (result, message, PatientModel patientModel) = await
                    httpClientFactory.CreateClient().CreatePatient(request, postPatientUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{logPrefix} Call PostCustomerHis Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, patientModel);
                customerHospital.PatientId = patientModel.id;
                customerHospital.PatientCode = patientModel.id;
            }

            RegisterFormModel response = new();
            if (req.PaymentAmount == 0)
            {
                string url = $"{hisConfig.Host}/Booking/PostBooking";
                var request = new CreateRegisterFormRequest()
                {
                    ID = 0,
                    Transaction_code_gd = string.Empty,
                    Transaction_code_tt = string.Empty,
                    Booking_date = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMdd"),
                    Doctor_id = null,
                    Subject_id = req.Service.ClinicId.ToInt(),
                    Room_id = req.Service.Id.ToInt(),
                    Patient_name = req.Customer.LastName,
                    Patient_surname = req.Customer.FirstName,
                    Birthyear = req.Customer!.DateOfBirth!.Value.Year,
                    Birthdate = req.Customer!.DateOfBirth!.Value.ToString("yyyyMMdd"),
                    Social_id = req.Customer!.IdentityNo,
                    Mobile = req.Customer!.Phone,
                    Patient_code = customerHospital.PatientCode ?? string.Empty,
                    Country_code = "VN",
                    City_id = hisProvinceId.ToInt(),
                    District_id = hisDistrictId.ToInt(),
                    Ward_id = hisWardId.ToInt(),
                    Address = req.Customer!.Address,
                    Sex = req.Customer.Sex == "Nam" ? 0 : 1,
                    Booking_number = 0,
                    Bv_time = DateTimeHelper.GetCurrentLocalDateTime().ToString("HH:mm"),
                    Amount = 0,
                    amount_original = 0,
                    amount_gate = 0,
                    Status = 1,
                    IsBHYT = req.IsInsurance ? 1 : 0, // 1: BHYT, 0: Không BHYT
                    Date_create = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                    Date_update = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                    PAYMENTCODE = string.Empty,
                    Note = string.Empty
                };
                Log.Information("{logPrefix} Call CreateRegisterForm Req {@Request}", logPrefix, request);
                (result, message, response) = await
                        httpClientFactory.CreateClient().CreateRegisterForm(url, request, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{logPrefix} Call CreateRegisterForm Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, response);
            }
            var data = new RegisterFormResponseDto
            {
                Clinic = req.Service.ClinicId ?? string.Empty,
                QrCode = string.Empty,
                QueueNumber = response.booking_number != 0 ? response.booking_number.ToString() : string.Empty,
                QueueNumberPriority = "0",
                RegisterNumber = string.IsNullOrEmpty(response.BookingID) ? refNo : response.BookingID,
                ReceiptRefNo = IdentityHelper.Guid(15),
                PaymentRefNo = string.Empty,
                RefDocNo = string.Empty,
                ExaminationLocation = req.Service.ExaminationLocation ?? string.Empty,
                RateOfInsurance = "0",
                PatientId = customerHospital.PatientId ?? string.Empty,
                PatientCode = customerHospital.PatientCode ?? string.Empty,
                ExpectedAppointmentAt = null,
            };
            refNo = data.RegisterNumber;
            Log.Information("{LogPrefix} CreateRegisterForm Res --- {@Response}", logPrefix, data);

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            string getClinicUrl = $"{hisConfig.Host}/Subjects/GetSubjects";

            (result, message, List<ClinicModel> data) = await
                    httpClientFactory.CreateClient().GetClinic(getClinicUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);

            if (result)
            {
                list = data.Select(x => new Clinic()
                {
                    Id = x.id.ToString(),
                    Code = x.id.ToString(),
                    Name = x.name,
                    ExameTypeID = exameTypeId ?? string.Empty
                }).ToList();
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            bool result = false;
            string message = string.Empty;
            CustomerHealthInsurance data = new();

            //1. Lấy thông tin bệnh nhân
            string postPatientUrl = $"{hisConfig.Host}/Patients/GetPatientsByMobile";

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var patientReq = new GetPatientRequest()
            {
                social_id = checkCode ?? string.Empty,
                mobile = customer.Phone ?? string.Empty,
                patientName = customer.GetFullName().ToUpper() ?? string.Empty,
                patientbirthday = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy")
            };

            Log.Information("{logPrefix} Call GetCustomerHealthInsurance Req {@Request}", logPrefix, patientReq);

            (bool getPatientResult, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(patientReq, postPatientUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);

            Log.Information("{logPrefix} Call GetCustomerHealthInsurance Res {Result} - Message {Message} - Response {@Response}", logPrefix, getPatientResult, message, patient);

            //2. Kiểm tra bảo hiểm theo mã bệnh nhân

            if (!getPatientResult)
            {
                return (getPatientResult, message, ErrorTypeEnum.HisError, data);
            }
            else
            {
                string checkInsuranceUrl = $"{hisConfig.Host}/Patients/PostPatients_checkbh";
                var checkInsuranceReq = new CheckHealthInsuranceRequest()
                {
                    ID = patient.id,
                    Ngay = DateTime.Now.ToString("yyyyMMdd")
                };

                Log.Information("{logPrefix} Call CheckHealthInsurance Req {@Request}", logPrefix, checkInsuranceReq);

                (result, message, HealthInsuranceModel insurance) = await
                    httpClientFactory.CreateClient().CheckHealthInsurance(checkInsuranceUrl, checkInsuranceReq, hisConfig.EncryptKey, hisConfig.DecryptKey);

                Log.Information("{logPrefix} Call CheckHealthInsurance Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, insurance);

                if (result)
                {
                    data.HealthInsuranceId = insurance.maThe ?? string.Empty;
                    data.Description = insurance.note ?? string.Empty;
                    data.IsCorrectRouting = insurance.tiep_nhan_bhyt == 1;
                    data.ExpiredDate = insurance.gtTheDen ?? string.Empty;
                    data.FromDate = insurance.gtTheTu ?? string.Empty;
                    data.RegisterAddress = insurance.cqBHXH ?? string.Empty;
                    data.InsuranceNo = insurance.maThe ?? string.Empty;
                    data.IdentityNo = customer.IdentityNo ?? string.Empty;
                    data.RegisterPlaceID = insurance.maDKBD ?? string.Empty;
                    data.FullFiveYearDate = insurance.ngayDu5Nam ?? string.Empty;
                    data.RoutingType = "-1";
                    data.ReferralLevel = data.IsCorrectRouting ? "1" : "2";
                    data.CustomerId = customer.Id;
                }
            }

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public virtual Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, ExameTypeConstant.ExameTypes));
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            string getHealthServiceUrl = $"{hisConfig.Host}/Rooms/GetRooms";

            (result, message, List<HealthServiceModel> data) = await
                    httpClientFactory.CreateClient().GetHealthService(getHealthServiceUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);

            if (result)
            {
                list = data
                .Where(x => x.subject_id == request.ClinicId)
                .Select(x => new HealthService()
                {
                    Id = x.id.ToString(),
                    Name = x.name,
                    ExameTypeId = request.ExameTypeId ?? string.Empty,
                    UnitPrice = x.price_dv,
                    UnitPriceDisplay = x.price_dv.ToString("N0") + " đ",
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    ExaminationLocation = string.IsNullOrEmpty(x.room_number) ? null : x.room_number,
                    InsurancePrice = x.price_bh,
                    InsurancePriceDisplay = x.price_bh.ToString("N0") + " đ",
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                }).ToList();
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            bool result;
            string message;

            //1. Lấy thông tin bệnh nhân
            string getPatientUrl = $"{hisConfig.Host}/Patients/GetPatientsByMobile";
            var req = new GetPatientRequest()
            {
                social_id = customer.IdentityNo ?? string.Empty,
                mobile = customer.Phone ?? string.Empty,
                patientName = customer.GetFullName().ToUpper() ?? string.Empty,
                patientbirthday = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy")
            };
            Log.Information("{logPrefix} Call GetCustomerHis Req {@Req} ", logPrefix, req);
            (result, message, PatientModel patient) = await
                httpClientFactory.CreateClient().GetPatient(req, getPatientUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call GetCustomerHis Res {Result} - Message {Message} - Response {@Response}", logPrefix, result, message, patient);

            return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto()
            {
                PatientCode = patient.id,
                PatientId = patient.id
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            List<GetSocialCareerDto> list = [];

            string url = $"{hisConfig.Host}/Jobs/GetJobs";

            bool result;
            string message;
            (result, message, List<SocialCareerModel> resData)
                = await httpClientFactory.CreateClient().GetSocialCareers(url, hisConfig.EncryptKey, hisConfig.DecryptKey);

            Log.Information("{LogPrefix} GetSocialCareers Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData!.Where(x => !string.IsNullOrEmpty(x.id))
                    .Select(x => new GetSocialCareerDto
                    {
                        Id = x.id,
                        Name = x.name,
                    }).ToList();
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, string[]?)>((true, string.Empty, ErrorTypeEnum.NoError, null));
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();
            try
            {
                var register = await databaseService.Registers
                                        .Include(x => x.Customer)
                                        .FirstOrDefaultAsync(x => x.Number == request.RegisterNumber);
                if (register == null)
                {
                    return (false, string.Empty, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
                }

                (string hisProvinceId, string hisDistrictId, string hisWardId) =
                await GetAddressId(register.Customer!.WardId ?? string.Empty,
                                   register.Customer!.DistrictId ?? string.Empty,
                                   register.Customer!.ProvinceId ?? string.Empty);

                string url = $"{hisConfig.Host}/Booking/PostBooking";
                var req = new CreateRegisterFormRequest()
                {
                    ID = 0,
                    Transaction_code_gd = request.TransactionId,
                    Transaction_code_tt = request.PaidTime!.Value.AddHours(7).ToString("dd/MM/yyyy HH:mm:ss"),
                    Booking_date = request.PaidTime!.Value.AddHours(7).ToString("yyyyMMdd"),
                    Doctor_id = null,
                    Subject_id = register!.ClinicId.ToInt(),
                    Room_id = register!.HealthServiceId.ToInt(),
                    Patient_name = register.Customer!.LastName,
                    Patient_surname = register.Customer!.FirstName,
                    Birthyear = register.Customer!.DateOfBirth!.Value.Year,
                    Birthdate = register.Customer!.DateOfBirth!.Value.ToString("yyyyMMdd"),
                    Social_id = register.Customer!.IdentityNo,
                    Mobile = register.Customer!.Phone,
                    Patient_code = request.PatientCode,
                    Country_code = "VN",
                    City_id = hisProvinceId.ToInt(),
                    District_id = hisDistrictId.ToInt(),
                    Ward_id = hisWardId.ToInt(),
                    Address = register.Customer!.Address,
                    Sex = register.Customer!.Sex == "Nam" ? 0 : 1,
                    Booking_number = 0,
                    Bv_time = register.RegisterAt.GetValueOrDefault().AddHours(7).ToString("HH:mm"),
                    Amount = request.PaidAmount,
                    amount_original = 0,
                    amount_gate = 0,
                    Status = 1,
                    IsBHYT = register.HealthInsurance, // 1: BHYT, 0: Không BHYT
                    Date_create = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                    Date_update = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMddHHmm"),
                    PAYMENTCODE = request.InvoiceId,
                    Note = request.TransactionDescription
                };
                Log.Information("{logPrefix} Call UpdatePaymentStatus Req {@Request}", logPrefix, req);
                (bool boolResult, string Message, RegisterFormModel response) = await
                        httpClientFactory.CreateClient().CreateRegisterForm(url, req, hisConfig.EncryptKey, hisConfig.DecryptKey);
                Log.Information("{logPrefix} Call UpdatePaymentStatus Res {Result} - Message {Message} - Response {@Response}", logPrefix, boolResult, Message, response);

                register.QueueNumber = response.booking_number != 0 ? response.booking_number.ToString() : string.Empty;
                register.RefNo = response.BookingID ?? string.Empty;
                register.HisMessageWhenCreateForm = Message;
                register.IsHisCreateFormSuccess = boolResult;

                databaseService.Registers.Update(register);
                return (boolResult, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()
                {
                    IpnResult = boolResult,
                    IpnMessage = Message,
                });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{logPrefix} UpdatePaymentStatus Error", logPrefix);
                return (false, ex.Message, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
            }
        }

        protected async Task<(string, string, string)> GetAddressId(string wardId, string districtId, string provinceId)
        {
            string provinceUrl = $"{hisConfig.Host}/Cities/GetCities";
            string districtUrl = $"{hisConfig.Host}/Districts/GetDistricts";
            string wardUrl = $"{hisConfig.Host}/Wards/GetWards";
            bool Result;
            string Message;

            Log.Information("{logPrefix} Call GetAddressId url {url}", logPrefix, provinceUrl);
            (Result, Message, List<AddressModel> provinces) = await
                    httpClientFactory.CreateClient().GetAddress(provinceUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call GetAddressId Res {Result} - Message {Message} - Response {@Response}", logPrefix, Result, Message, provinces);

            var provinceNames = provinces
                                    .Where(x => !string.IsNullOrEmpty(x.name))
                                    .Select(x => x.name ?? string.Empty).ToList();

            var localProvinces = await provinceRepository!.GetProvincedAsync();
            var localProvinceName = localProvinces?.Find(x => x.Id == provinceId)?.OtherName ?? string.Empty;
            //find closet string in local province
            var provinceName = provinceNames?.FindMatchedString(localProvinceName);
            if (string.IsNullOrEmpty(provinceName))
            {
                provinceName = provinceNames?.FindClosestString(localProvinceName);
            }

            string hisProvinceId = provinces.Find(x => x.name == provinceName)?.id ?? string.Empty;


            Log.Information("{logPrefix} Call GetAddressId url {url}", logPrefix, districtUrl);
            (Result, Message, List<AddressModel> districts) = await
                    httpClientFactory.CreateClient().GetAddress(districtUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call GetAddressId Res {Result} - Message {Message} - Response {@Response}", logPrefix, Result, Message, districts);

            districts.ForEach(x => x.name = StringHelper.NormalizeNumberInAddress(x.name ?? string.Empty));

            var districtsInProvince = districts
                                    .Where(x =>
                                       (string.IsNullOrEmpty(hisProvinceId) || x.city_id == hisProvinceId)
                                    && !string.IsNullOrEmpty(x.name)).ToList();
            var districtNames = districtsInProvince.Select(x => x.name ?? string.Empty).ToList();

            var localDistricts = await districtRepository!.GetDistrictByParentIdAsync(provinceId);
            var localDistrictName = localDistricts?.Find(x => x.Id == districtId)?.Name ?? string.Empty;

            //find closet string in local district
            var districtName = districtNames?.FindMatchedString(localDistrictName);
            if (string.IsNullOrEmpty(districtName))
            {
                districtName = districtNames?.FindClosestString(localDistrictName);
            }

            string hisDistrictId = districtsInProvince.Find(x => x.name == districtName)?.id ?? string.Empty;


            Log.Information("{logPrefix} Call GetAddressId url {url}", logPrefix, wardUrl);
            (Result, Message, List<AddressModel> wards) = await
                    httpClientFactory.CreateClient().GetAddress(wardUrl, hisConfig.EncryptKey, hisConfig.DecryptKey);
            Log.Information("{logPrefix} Call GetAddressId Res {Result} - Message {Message} - Response {@Response}", logPrefix, Result, Message, wards);

            wards.ForEach(x => x.name = StringHelper.NormalizeNumberInAddress(x.name ?? string.Empty));

            var wardInDistrict = wards
                                    .Where(x =>
                                       (string.IsNullOrEmpty(hisDistrictId) || x.district_id == hisDistrictId)
                                    && !string.IsNullOrEmpty(x.name)).ToList();
            var wardNames = wardInDistrict.Select(x => x.name ?? string.Empty).ToList();

            var localWards = await wardRepository!.GetWardByParentIdAsync(districtId, current.IsTwoLevelAddress);
            var localWardName = localWards?.Find(x => x.Id == wardId)?.Name ?? string.Empty;

            //find closet string in local ward
            var wardName = wardNames?.FindMatchedString(localWardName);
            if (string.IsNullOrEmpty(wardName))
            {
                wardName = wardNames?.FindClosestString(localWardName);
            }

            string hisWardId = wardInDistrict.Find(x => x.name == wardName)?.id ?? string.Empty;

            Log.Information("{LogPrefix} Get AddressId ---- Province: {province}, District: {district}, Ward: {ward}", logPrefix, hisProvinceId, hisDistrictId, hisWardId);
            return (hisProvinceId, hisDistrictId, hisWardId);
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }
        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

﻿using HisClientV8.Lib;
using HisClientV8.Lib.Model;
using HisClientV8.Lib.Request;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// BV Q1 (<PERSON>ồ<PERSON> không có phiếu đăng ký)
    /// </summary>
    public class HisV8Service : HisServiceParameters, IHisService
    {
        private readonly HisV8Config hisConfig;
        public HisV8Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV8Config>(configSerialize) ??
                throw new ArgumentNullException("HisV8Config is null");
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTierDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)>((true, string.Empty, ErrorTypeEnum.NoError, []));
        }

        public async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(Application.Features.HisLogic.Dtos.AutoDialerQueueAddNewRequest request)
        {
            AutoDialerQueueAddNewResponse response = new();

            string url = $"{hisConfig.Host}/qms_layso_dang_ky";

            bool result;
            string message;
            (result, message, HisClientV8.Lib.Response.AutoDialerQueueAddNewResponse resData) =
                await httpClientFactory.CreateClient().AutoDialerQueueAddNew(new HisClientV8.Lib.Request.AutoDialerQueueAddNewRequest
                {
                    ngay_layso = DateTimeHelper.GetCurrentLocalDateTime().ToString("dd/MM/yyyy"),
                    uu_tien = request.UuTien ?? 0
                }, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} AutoDialerQueueAddNew --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                if (int.TryParse(resData.stt, out int currentNumber))
                {
                    response = new AutoDialerQueueAddNewResponse
                    {
                        SoThuTu = currentNumber,
                        MaPhong = request.MaPhong ?? string.Empty,
                        UuTien = request.UuTien == 1,
                        NgayDangKy = DateTimeHelper.GetCurrentLocalDateTime(),
                    };
                }
                else
                {
                    result = false;
                    message = "Lỗi lấy số thứ tự";
                }
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, response);
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new object()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            bool result = false;
            string message = string.Empty;
            PaymentStatusDto status = new();

            string url = $"{hisConfig.Host}/trang-thai-phieu";

            var req = new CheckPaymentRequest()
            {
                so_phieu = refNo
            };

            Log.Information("{LogPrefix} CheckPaymentStatus Req --- {@Request}", logPrefix, req);

            (result, message, PaymentModel resData) =
                await httpClientFactory.CreateClient().CheckPayment(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                status.Number = resData.so_phieu;
                status.RefNo = resData.so_phieu;
                status.PaymentStatus = resData.da_thanh_toan ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, status);
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            string url = $"{hisConfig.Host}/dangky-kcb";
            bool result;
            string message;

            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Address ?? string.Empty,
                    ma_dantoc = request.Customer.NationalityId ?? string.Empty,
                    ma_quoctich = request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = request.Customer.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTime.UtcNow.AddHours(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_bhyt = request.Service.InsurancePrice ?? 0,
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMdd") + IdentityHelper.Guid(15);
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = "0",
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            string url = $"{hisConfig.Host}/khoa";

            var req = new GetClinicRequest()
            {
                id_loai_kham = exameTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetClinics Req --- {@Request}", logPrefix, req);

            (result, message, List<ClinicModel> resData) =
                await httpClientFactory.CreateClient().GetClinic(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData!.Select(x => new Clinic()
                {
                    Id = x.id_khoa ?? string.Empty,
                    Code = !string.IsNullOrEmpty(x.ma_khoa) ? x.ma_khoa : (x.id_khoa ?? string.Empty),
                    Name = x.ten_khoa ?? string.Empty,
                    ExameTypeID = exameTypeId ?? string.Empty,
                }).ToList();
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            string url = $"{hisConfig.Host}/bhyt";

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var request = new GetHealthInsuranceRequest()
            {
                so_gttt = checkCode ?? string.Empty,
                loai_gttt = "CCCD",
                ho_ten = customer.GetFullName(),
                ngay_sinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                gioi_tinh = customer.Sex == "Nam" ? 2 : 1
            };

            string message;
            bool result;
            (result, message, HealthInsuranceModel card) = await httpClientFactory.CreateClient().GetHealthInsurance(
                request, url, hisConfig.MerchantId, hisConfig.SecretKey);

            if (result)
            {
                data.HealthInsuranceId = card.ma_the_bhyt;
                data.CustomerId = customer.Id;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.ma_the_bhyt ?? string.Empty;
                data.InsuranceGroupID = string.Empty;
                data.FromDate = DateTimeHelper.FormatDate(card.gt_the_tu, "yyyy-MM-dd", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.gt_the_den, "yyyy-MM-dd", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ma_dkbd;
                data.RoutingType = "-1";
                data.ReferralLevel = card.tiep_nhan_bhyt == 1 ? "1" : "2";
                data.IsCorrectRouting = card.tiep_nhan_bhyt == 1;
                data.Description = card.ten_kq;
                data.RegisterAddress = card.dia_chi;

                result = true;
            }

            Log.Information("{LogPrefix} GetCustomerHealthInsurance --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/loai-kham";
            (result, message, List<ExameTypeModel> resData)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetExameTypes --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData.Select(x => new ExameType()
                {
                    Id = x.id_loai_kham.ToString(),
                    Name = x.ten_loai_kham ?? string.Empty,
                    IsInsurance = (x.ten_loai_kham ?? string.Empty).Contains("BẢO HIỂM", StringComparison.CurrentCultureIgnoreCase)
                                || (x.ten_loai_kham ?? string.Empty).Contains("BHYT", StringComparison.CurrentCultureIgnoreCase)
                }).ToList();
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            string url = $"{hisConfig.Host}/phong-kham";

            var req = new GetHealthServiceRequest()
            {
                id_khoa = request.ClinicId ?? string.Empty,
                ma_khoa = request.ClinicId ?? string.Empty,
                id_loai_kham = request.ExameTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthServices Req --- {@Request}", logPrefix, req);

            (result, message, List<HealthServiceModel> resData)
                = await httpClientFactory.CreateClient().GetHealthService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData!.OrderBy(x => x.thu_tu_sap_xep)
                .Select(x => new HealthService()
                {
                    Id = x.id_phong_kham ?? string.Empty,
                    Code = x.ma_phong_kham ?? string.Empty,
                    Name = x.ten_phong_kham ?? string.Empty,
                    UnitPrice = x.don_gia_phong_kham,
                    UnitPriceDisplay = x.don_gia_phong_kham.ToString("N0") + " đ",
                    InsurancePrice = current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt,
                    InsurancePriceDisplay = (current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt).ToString("N0") + " đ",
                    ExtraPrice = x.don_gia_thu_them,
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                    ExameTypeId = x.id_loai_kham,
                    ClinicId = x.ma_khoa ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                }).ToList();
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;

            string url = $"{hisConfig.Host}/benhnhan";

            string message;
            bool result;
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(
                new GetPatientRequest()
                {
                    so_gttt = customer.IdentityNo ?? string.Empty,
                    loai_gttt = "CCCD",
                    dien_thoai = customer.Phone ?? string.Empty
                }, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHis --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient.id_bn ?? string.Empty;
                patientCodeResponse = patient.ma_bn ?? string.Empty;
                careerId = patient.ma_nghe_nghiep ?? string.Empty;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new HisCustomerDto
            {
                PatientId = patientId,
                PatientCode = patientCodeResponse,
                CareerId = careerId
            });
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)>((true, string.Empty, ErrorTypeEnum.NoError, []));
        }

        public Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, string[]?)>((true, string.Empty, ErrorTypeEnum.NoError, null));
        }

        public Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()));
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            PushReceiptInfoResponseDto responseContent = new();

            var hospital = await databaseService.Hospitals.FindAsync(current.HospitalId);
            if (hospital is null)
            {
                return (false, PaymentConstant.NotFoundMerchant, ErrorTypeEnum.MediPayError, responseContent);
            }
            string url = $"{hisConfig.Host}/thong-tin-xuat-hoa-don";
            var client = httpClientFactory.CreateClient();

            var sendRequest = new SendReceiptInfoRequest
            {
                TEN_CONG_TY = request.CompanyName,
                DIA_CHI_CONG_TY = request.CompanyAddress,
                MA_SO_THUE = request.TaxNo,
                MA_BN = request.PatientCode,
                SO_PHIEU = request.RefNo,
                EMAIL = request.Email,
                SO_DIEN_THOAI = request.PhoneNumber,
                SO_TIEN = request.Amount,
            };

            bool result;
            string message;

            Log.Information("{LogPrefix} PushInvoiceInfo Req --- {@Request}", logPrefix, sendRequest);
            (result, message, HisClientV8.Lib.Response.PushReceiptInfoResponse response) = await client.PushInvoiceInfo(sendRequest, url, hisConfig.MerchantId, hisConfig.SecretKey);

            responseContent.Code = response.Code;
            responseContent.Message = response.Message;

            Log.Information("{LogPrefix} PushInvoiceInfo Res --- Result: {Result} - Message: {Message} - {@ResponseContent}", logPrefix, result, message, responseContent);

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, responseContent);
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

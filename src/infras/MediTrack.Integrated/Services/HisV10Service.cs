﻿using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Luồng riêng: Bãi Cháy, HIS tự xây tương tự V7 nhưng thay path "-" thành "_"
    /// Chia giá thu thêm và giá phòng khám
    /// BHYT luôn không thanh toán (giá 0)
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV10Service : HisServiceParameters, IHisService
    {
        private readonly HisV7Config hisConfig;

        public HisV10Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV10Config is null");
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTierDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            var config = await databaseService.HospitalMetaDatas
                    .FirstOrDefaultAsync(x => x.HospitalId == current.HospitalId && x.GroupType == "danh_sach_doi_tuong_kcb");

            if (config is null || string.IsNullOrEmpty(config.Value))
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            //parse string to list of GetMedicalTreatmentCategoryDto
            var list = JsonConvert.DeserializeObject<List<GetMedicalTreatmentCategoryDto>>(config.Value);
            if (list is null)
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            // Số ưu tiên nối tiếp số thường, bỏ qua Priority
            var currentQueue = await databaseService.DialerQueues.FirstOrDefaultAsync(a =>
                a.QueueDate.Date == DateTimeHelper.GetCurrentLocalDateTime().Date &&
                a.HospitalId == current.HospitalId &&
                (string.IsNullOrEmpty(request.MaPhong) || a.HealthServiceId == request.MaPhong)
            );

            if (currentQueue is not null)
            {
                currentQueue.QueueNumber += 1;
                databaseService.DialerQueues.Update(currentQueue);
            }
            else
            {
                currentQueue = new DialerQueue
                {
                    Id = Guid.NewGuid().ToString(),
                    HealthServiceId = request.MaPhong,
                    QueueDate = DateTimeHelper.GetCurrentLocalDateTime(),
                    QueueNumber = 1,
                    HospitalId = current.HospitalId,
                };
                databaseService.DialerQueues.Add(currentQueue);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
            {
                Id = currentQueue.Id,
                SoThuTu = currentQueue.QueueNumber,
                UuTien = request.UuTien == 1,
                NgayDangKy = currentQueue.QueueDate.AddHours(7),
                MaPhong = currentQueue.HealthServiceId,
            });
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new object()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            PaymentStatusDto status = new();

            if (!current.IsGenQR)
            {
                var payment = await databaseService.Payments
                .FirstOrDefaultAsync(x => x.RefNo == refNo && x.HospitalId == current.HospitalId);

                if (payment is null)
                {
                    return (false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError, new PaymentStatusDto());
                }

                return (true, string.Empty, ErrorTypeEnum.NoError, new PaymentStatusDto()
                {
                    Number = payment.Id ?? string.Empty,
                    PaymentStatus = payment.Status ?? PaymentConstant.WaitForPayment,
                    InvoiceInfoRef = payment.InvoiceInfoRef ?? string.Empty,
                    RefNo = payment.RefNo ?? string.Empty
                });
            }

            string url = $"{hisConfig.Host}/trang_thai_phieu";

            bool result;
            string? message;
            Log.Information("{LogPrefix} CheckPaymentStatus Req --- RefNo: {RefNo}", logPrefix, refNo);
            (result, message, PaymentModel resData) =
                await httpClientFactory.CreateClient().CheckPayment(new CheckPaymentRequest()
                {
                    so_phieu = refNo
                }, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                status.Number = resData.so_phieu;
                status.RefNo = resData.so_phieu;
                status.PaymentStatus = resData.da_thanh_toan ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
            }

            return (result, message, ErrorTypeEnum.NoError, status);
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;

            string url = $"{hisConfig.Host}/dangky_kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Address ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = request.Customer.NationalityId ?? string.Empty,
                    ma_quoctich = request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = request.Customer.CareerId ?? string.Empty,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = !string.IsNullOrEmpty(request.MedicalTreatmentCategoryId) ? request.MedicalTreatmentCategoryId : "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    ma_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = "0",
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            string url = $"{hisConfig.Host}/khoa";
            var req = new GetClinicRequest()
            {
                id_loai_kham = exameTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetClinics Req --- {@Request}", logPrefix, req);

            (result, message, List<ClinicModel> resData) =
                await httpClientFactory.CreateClient().GetClinic(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new Clinic()
                {
                    Id = x.id_khoa ?? string.Empty,
                    Code = !string.IsNullOrEmpty(x.ma_khoa) ? x.ma_khoa : (x.id_khoa ?? string.Empty),
                    Name = x.ten_khoa ?? string.Empty,
                    ExameTypeID = exameTypeId ?? string.Empty,
                    ProcessingNumber = x.sl_hien_tai,
                }).OrderBy(x => x.Name)];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            string url = $"{hisConfig.Host}/bhyt";

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var request = new GetHealthInsuranceRequest()
            {
                so_gttt = checkCode ?? string.Empty,
                loai_gttt = "CCCD",
                ho_ten = customer.GetFullName(),
                ngay_sinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                gioi_tinh = customer.Sex == "Nam" ? 2 : 1
            };

            bool result;
            string message;
            Log.Information("{LogPrefix} GetCustomerHealthInsurance Req --- {@Request}", logPrefix, request);
            (result, message, HealthInsuranceModel card) = await httpClientFactory.CreateClient().GetHealthInsurance(
                request, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Res --- Result: {Result} - Message: {Message} - {@Card}", logPrefix, result, message, card);

            if (result)
            {
                data.HealthInsuranceId = card.ma_the_bhyt;
                data.CustomerId = customer.Id;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.ma_the_bhyt ?? string.Empty;
                data.InsuranceGroupID = string.Empty;
                data.FromDate = DateTimeHelper.FormatDate(card.gt_the_tu, "yyyy-MM-dd", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.gt_the_den, "yyyy-MM-dd", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ma_dkbd;
                data.RoutingType = "-1";
                data.ReferralLevel = card.tiep_nhan_bhyt == 1 ? "1" : "2";
                data.IsCorrectRouting = card.tiep_nhan_bhyt == 1;
                data.Description = card.ten_kq;
                data.RegisterAddress = card.dia_chi;
                data.HisMessage = card.message ?? string.Empty;
                data.MedicalHistories = card.lich_su_kcb?.Select(x => new MedicalHistoryModel()
                {
                    RecordId = x.ma_ho_so ?? string.Empty,
                    HealthcareFacilityId = x.ma_cs_kcb ?? string.Empty,
                    AdmissionDate = x.ngay_vao ?? string.Empty,
                    DischargeDate = x.ngay_ra ?? string.Empty,
                    DiseaseName = x.ten_benh ?? string.Empty,
                    Condition = x.tinh_trang ?? string.Empty,
                    TreatmentResult = x.kq_dieu_tri ?? string.Empty,
                    AdmissionReason = x.ly_do_vv ?? string.Empty
                }).ToList() ?? [];
                result = true;
            }

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];

            string url = $"{hisConfig.Host}/loai_kham";
            (result, message, List<ExameTypeModel> resData)
                = await httpClientFactory.CreateClient().GetExameTypes(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData.Select(x => new ExameType()
                {
                    Id = x.id_loai_kham.ToString(),
                    Name = x.ten_loai_kham ?? string.Empty,
                    IsInsurance = (x.ten_loai_kham ?? string.Empty).Contains("BẢO HIỂM", StringComparison.CurrentCultureIgnoreCase)
                        || (x.ten_loai_kham ?? string.Empty).Contains("BHYT", StringComparison.CurrentCultureIgnoreCase),
                }).ToList();
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            string url = $"{hisConfig.Host}/phong_kham";
            var req = new GetHealthServiceRequest()
            {
                id_khoa = request.ClinicId ?? string.Empty,
                ma_khoa = request.ClinicCode ?? string.Empty,
                id_loai_kham = request.ExameTypeId ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthServices Req --- {@Request}", logPrefix, req);

            (result, message, List<HealthServiceModel> resData)
                = await httpClientFactory.CreateClient().GetHealthService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                var priceDisplay = await databaseService.HospitalMetaDatas
                    .Where(x => x.HospitalId == current.HospitalId
                    && x.GroupType == "price_display_default"
                    && (x.Code == "INSURANCE" || x.Code == "SERVICE")).ToListAsync();

                var servicePriceDisplay = priceDisplay.Find(x => x.Code == "SERVICE");
                var insurancePriceDisplay = priceDisplay.Find(x => x.Code == "INSURANCE");

                list = [.. resData!.OrderBy(x => x.thu_tu_sap_xep)
                .Select(x => new HealthService()
                {
                    Id = x.id_phong_kham ?? string.Empty,
                    Code = x.ma_phong_kham ?? string.Empty,
                    Name = x.ten_phong_kham ?? string.Empty,
                    UnitPrice = request.ExameTypeId == "2" ? x.don_gia_thu_them : x.don_gia_phong_kham, // 2: Khám thu phí
                    UnitPriceDisplay = servicePriceDisplay?.Value
                        ?? ((request.ExameTypeId == "2" ? x.don_gia_thu_them : x.don_gia_phong_kham).ToString("N0") + " đ"),
                    InsurancePrice = x.don_gia_bhyt,
                    InsurancePriceDisplay = insurancePriceDisplay?.Value ?? "0 đ",
                    ExtraPrice = 0,
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                    ExameTypeId = request.ExameTypeId ?? string.Empty,
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    WaitingPatientCount = (x.sl_hien_tai.HasValue && x.sl_da_kham.HasValue)
                                                ? Math.Max(x.sl_hien_tai.Value - x.sl_da_kham.Value, 0)
                                                : null,
                    ProcessingNumber = x.sl_hien_tai,
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;

            string url = $"{hisConfig.Host}/benhnhan";

            string message;
            bool result;
            var req = new GetPatientRequest()
            {
                so_gttt = customer.IdentityNo ?? string.Empty,
                loai_gttt = "CCCD",
                dien_thoai = customer.Phone ?? string.Empty
            };
            Log.Information("{LogPrefix} GetCustomerHis Req --- {@Request}", logPrefix, req);
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(
                req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient?.id_bn ?? string.Empty;
                patientCodeResponse = patient?.ma_bn ?? string.Empty;
                careerId = patient?.ma_nghe_nghiep ?? string.Empty;
            }

            return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto
            {
                PatientId = patientId,
                PatientCode = patientCodeResponse,
                CareerId = careerId
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            List<GetSocialCareerDto> list = [];

            string url = $"{hisConfig.Host}/nghe_nghiep";

            bool result;
            string message;
            (result, message, List<SocialCareerModel> resData)
                = await httpClientFactory.CreateClient().GetSocialCareers(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetSocialCareers Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = resData!.Where(x => !string.IsNullOrEmpty(x.MA_NGHE_NGHIEP))
                    .Select(x => new GetSocialCareerDto
                    {
                        Id = x.MA_NGHE_NGHIEP,
                        Name = x.TEN_NGHE_NGHIEP,
                        IsDefault = x.MAC_DINH.GetValueOrDefault()
                    }).ToList();
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, string[]?)>((true, string.Empty, ErrorTypeEnum.NoError, null));
        }

        public async Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            if (current.IsGenQR)
            {
                return (true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse());
            }

            var hospital = await databaseService.Hospitals.FindAsync(current.HospitalId);
            if (hospital is null)
            {
                return (false, PaymentConstant.NotFoundMerchant, ErrorTypeEnum.MediPayError, new UpdatePaymentStatusResponse());
            }

            //4. Send notification
            var config = new PaymentConfigModel()
            {
                SecretKey = hospital!.SecretKey,
                Url = hospital.IpnUrl
            };

            var ipnRequest = new CreateIpnRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = request.InvoiceId,
                TransactionId = request.TransactionId,
                Status = request.Status,
                TransactionAmount = request.PaidAmount,
                TransactionDescription = request.TransactionDescription,
                PaidAmount = request.PaidAmount,
                PaidDescription = request.PaidDescription,
                PaidTime = request.PaidTime.GetValueOrDefault().ToString("yyyyMMddHHmmss")
            };

            Log.Information("{HospitalId} Call Ipn Req {@Request}", current.HospitalId, ipnRequest);

            (bool ipnResult, string ipnMessage, string ipnRes, string? logRequest) = await PaymentClient.Lib.PaymentClient.CreateIpn(httpClientFactory.CreateClient(), ipnRequest, config);

            //5. Cập nhật trạng thái sau khi IPN
            Log.Information("{HospitalId} Call Ipn Res {Result} - Message {Message} - Response {Response}", current.HospitalId, ipnResult, ipnMessage, ipnRes);

            return (true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()
            {
                IpnResult = ipnResult,
                IpnMessage = ipnMessage,
                IpnResponse = ipnRes
            });
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}
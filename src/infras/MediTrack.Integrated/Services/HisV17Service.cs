﻿using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Luồng chung: TAG, EMR giống v7 nhưng có chọn thêm đối tượng (1: BHYT, 2: Viện Ph<PERSON>)
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV17Service : HisV7Service
    {
        private readonly HisV7Config hisConfig;

        public HisV17Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV17Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;
            var doiTuongKcb = request.MedicalTreatmentCategoryId;

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcb = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                : null;

                doiTuongKcb = defaultDoiTuongKcb?.Value;
            }

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                    : null;

                if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                {
                    var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                    if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                    {
                        doiTuongKcb = doiTuongKCBMap.ContainsKey(request.Service.ExameTypeId ?? string.Empty)
                            ? doiTuongKCBMap[request.Service.ExameTypeId ?? string.Empty] : "1";
                    }
                }
            }

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            var defaultHealthcareServiceTierId = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_healthcare_service_tier_id")
                : null;

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = careerId,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    ly_do_vv = request.ReasonForVisit ?? string.Empty,
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = doiTuongKcb ?? "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.IsInsurance ? "1" : "2", //1: BHYT, 2: Viện Phí
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                    phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                    tuyen = defaultHealthcareServiceTierId?.Value ??  //Lão Khoa nhận rỗng
                        (request.IsInsurance && !string.IsNullOrEmpty(request.HealthcareServiceTierId) ? request.HealthcareServiceTierId : "0"), //Thái Nguyên sẽ nhận là 0 khi khám viện phí
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                    cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                    cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                    cs_nhiet_do = request.Temperature.ToString() ?? string.Empty,
                    cs_mach = request.PulseRate.ToString() ?? string.Empty,
                    uu_tien = request.Priority,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicId ?? string.Empty,
                    ma_khoa = request.Service.ClinicCode ?? string.Empty,
                    id_nhom_phong_kham = request.Service.ClinicGroupId ?? string.Empty,
                    ma_nhom_phong_kham = request.Service.ClinicGroupCode ?? string.Empty,
                    ten_nhom_phong_kham = request.Service.ClinicGroup ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                bn_dichvu = !request.IsInsurance
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = "0",
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            List<GetSocialCareerDto> list = [];

            string url = $"{hisConfig.Host}/nghe-nghiep";

            bool result;
            string message;
            (result, message, List<SocialCareerModel> resData)
                = await httpClientFactory.CreateClient().GetSocialCareers(url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetSocialCareers Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Where(x => !string.IsNullOrEmpty(x.MA_NN_BV))
                    .Select(x => new GetSocialCareerDto
                    {
                        Id = x.MA_NN_BV,
                        Name = x.TEN_NGHE_NGHIEP,
                        OriginId = x.MA_NN_QĐ,
                        IsDefault = x.MAC_DINH.GetValueOrDefault()
                    })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }
    }
}
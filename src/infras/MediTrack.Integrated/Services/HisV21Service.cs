﻿using HisClientV7.Lib;
using HisClientV7.Lib.Model;
using HisClientV7.Lib.Request;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;
using System.Globalization;
using MediTrack.Domain.Helpers;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Domain.Enums;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Tương tự v7, config dịch vụ theo ngày (BV quân y 354)
    /// BV này có response các id khác với request
    /// ClinicIdRes: id_khoa (API HealthService, CreateRegisterForm)
    /// ClinicCodeRes: ma_khoa (API HealthService, CreateRegisterForm)
    /// </summary>
    public class HisV21Service : HisV7Service
    {
        private readonly HisV7Config hisConfig;

        public HisV21Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(current.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV7Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV21Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];
            string url = $"{hisConfig.Host}/phong-kham";
            var req = new GetHealthServiceRequest()
            {
                id_khoa = request.ClinicId ?? string.Empty,
                ma_khoa = request.ClinicCode ?? string.Empty,
                id_loai_kham = request.ExameTypeId ?? string.Empty,
                ma_the_bhyt = request.HealthInsuranceNo ?? string.Empty
            };

            Log.Information("{LogPrefix} GetHealthServices Req --- {@Request}", logPrefix, req);

            (result, message, List<HealthServiceModel> resData)
                = await httpClientFactory.CreateClient().GetHealthService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);
            if (result)
            {
                var hospitalMetaDatas = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(current.HospitalId)
                : null;

                var servicePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "SERVICE");
                var insurancePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "INSURANCE");

                list = [.. resData!.OrderBy(x => x.sl_hien_tai ?? 0).ThenBy(x => x.thu_tu_sap_xep ?? 0)
                .Select(x => new HealthService()
                    {
                        Id = x.id_phong_kham ?? string.Empty,
                        Code = x.ma_phong_kham ?? string.Empty,
                        Name = x.ten_phong_kham ?? string.Empty,
                        UnitPrice = current.IsAdvancePayment ? null : x.don_gia_phong_kham,
                        UnitPriceDisplay = servicePriceDisplay?.Value
                            ?? (current.IsAdvancePayment ? "Tạm ứng" : (x.don_gia_phong_kham.ToString("N0") + " đ")),
                        InsurancePrice = current.IsInsuranceAdvancePayment ? null : (current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt),
                        InsurancePriceDisplay = insurancePriceDisplay?.Value
                            ?? (current.IsInsuranceAdvancePayment
                                ? "Tạm ứng" : ((current.IsUseExtraFeeAsInsurancePrice ? x.don_gia_thu_them : x.don_gia_bhyt).ToString("N0") + " đ")),
                        ExtraPrice = x.don_gia_thu_them,
                        IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                        ExameTypeId = request.ExameTypeId ?? string.Empty, // giữ lại đối tượng khám để dùng get lại khi đăng kí 354
                        ExameTypeIdRes = x.id_loai_kham ?? string.Empty,
                        ClinicGroupId = x.id_nhom_phong_kham ?? string.Empty,
                        ClinicGroupCode = x.ma_nhom_phong_kham ?? string.Empty,
                        ClinicGroup = x.ten_nhom_phong_kham ?? string.Empty,
                        ClinicId = request.ClinicId ?? string.Empty,
                        ClinicCode = request.ClinicCode ?? string.Empty,
                        ClinicIdRes = x.id_khoa ?? string.Empty,
                        ClinicCodeRes = x.ma_khoa ?? string.Empty,
                        SubClinicId = request.SubClinicId ?? string.Empty,
                        ExaminationHour = string.Empty,
                        WaitingPatientCount = (x.sl_hien_tai.HasValue && x.sl_da_kham.HasValue)
                                                    ? Math.Max(x.sl_hien_tai.Value - x.sl_da_kham.Value, 0)
                                                    : null,
                        RemainingPatientCount = null,
                        TotalPatientCount = x.sl_toi_da,
                        ProcessingNumber = x.sl_hien_tai
                })];
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            bool result;
            string message;
            var doiTuongKcb = request.MedicalTreatmentCategoryId;

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcb = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                : null;

                doiTuongKcb = defaultDoiTuongKcb?.Value;
            }

            if (string.IsNullOrEmpty(doiTuongKcb))
            {
                var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                    : null;

                if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                {
                    var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                    if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                    {
                        doiTuongKcb = doiTuongKCBMap.ContainsKey(request.Service.ExameTypeId ?? string.Empty)
                            ? doiTuongKCBMap[request.Service.ExameTypeId ?? string.Empty] : "1";
                    }
                }
            }

            var nationalityId = request.Customer.NationalityId ?? string.Empty;

            var mapNationalities = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                    : null;

            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                }
            }

            var careerId = request.Customer.CareerId ?? string.Empty;
            var mapCareers = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                : null;

            if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
            {
                var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                if (careers is not null && careers.Count > 0)
                {
                    careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                }
            }

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string url = $"{hisConfig.Host}/dangky-kcb";
            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                    ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ho_ten = request.Customer.GetFullName().ToUpper(),
                    dia_chi = request.Customer.Street ?? string.Empty,
                    dia_chi_day_du = request.Customer.Address ?? string.Empty,
                    dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                    ma_dantoc = nationalityId,
                    ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                    matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                    mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                    maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                    ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                    ma_nghe_nghiep = careerId,
                    ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                    gt_the_tu = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    gt_the_den = request.IsInsurance ?
                        DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                        : string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ly_do_vnt = "Khám bệnh",
                    ly_do_vv = request.ReasonForVisit ?? string.Empty,
                    //  Đối tượng khám chữa bệnh
                    ma_doituong_kcb = doiTuongKcb ?? "1",
                    //  Loại hình KCB: "01" khám bệnh
                    ma_loai_kcb = "01",
                    ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                    phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                    dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                    cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                    cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                    cs_nhiet_do = request.Temperature.ToString() ?? string.Empty,
                    cs_mach = request.PulseRate.ToString() ?? string.Empty,
                    uu_tien = request.Priority,
                },
                thong_tin_dich_vu = new HealthServiceModel()
                {
                    id_khoa = request.Service.ClinicIdRes ?? string.Empty, //354 sẽ nhận id phòng khám từ response phong-kham qua đây
                    ma_khoa = request.Service.ClinicCodeRes ?? string.Empty, //354 sẽ nhận mã phòng khám từ response phong-kham qua đây
                    id_nhom_phong_kham = request.Service.ClinicGroupId ?? string.Empty, //354 sẽ nhận id đối tương khám qua đây
                    ma_nhom_phong_kham = request.Service.ClinicGroupCode ?? string.Empty,
                    ten_nhom_phong_kham = request.Service.ClinicGroup ?? string.Empty,
                    id_phong_kham = request.Service.Id ?? string.Empty,
                    ma_phong_kham = request.Service.Code ?? string.Empty,
                    ten_phong_kham = request.Service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    don_gia_thu_them = request.Service.ExtraPrice ?? 0,
                },
                du_phong = string.Empty,
                id_thiet_bi = request.DeviceId ?? string.Empty,
                id_loai_kham = request.Service.ExameTypeIdRes ?? string.Empty, //354 sẽ nhận id đối tương khám từ response phong-kham qua đây
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                bn_dichvu = !request.IsInsurance
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan.stt_lk.ToString(),
                    QueueNumberPriority = registerDto.thong_tin_benh_nhan.uu_tien?.ToString() ?? request.Priority.ToString(),
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = registerDto.thong_tin_dang_ky.ten_khu_vuc ?? string.Empty,
                    MedicalTreatmentCategoryName = registerDto.thong_tin_dang_ky.ten_doi_tuong ?? string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                    PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                    ExpectedAppointmentAt = string.IsNullOrEmpty(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk) ?
                            null : DateTimeHelper.ConvertStringToDateTime(registerDto.thong_tin_tiep_nhan.thoi_gian_kham_dk, "yyyy-MM-dd HH:mm:ss"),
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public override async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;
            string rejectReason = string.Empty;
            bool isReject = false;
            List<MedicalHistory> medicalHistory = [];

            string url = $"{hisConfig.Host}/benhnhan";

            string message;
            bool result;
            var req = new GetPatientRequest()
            {
                so_gttt = !string.IsNullOrEmpty(patientCode) ? patientCode : customer.IdentityNo ?? string.Empty,
                loai_gttt = requestType,
                dien_thoai = customer.Phone ?? string.Empty,
                ma_the_bhyt = customer.HealthInsuranceNo ?? string.Empty,
                ngay_sinh = customer.DateOfBirth?.ToString("yyyy-MM-dd") ?? string.Empty,
                ho_ten = customer.GetFullName(),
                gioi_tinh = customer.Sex == "Nam" ? 1 : 2,
            };
            Log.Information("{LogPrefix} GetCustomerHis Req --- {@Request}", logPrefix, req);
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().GetPatient(
                req, url, hisConfig.MerchantId, hisConfig.SecretKey);

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Patient}", logPrefix, result, message, patient);

            if (result)
            {
                patientId = patient?.id_bn ?? string.Empty;
                patientCodeResponse = patient?.ma_bn ?? string.Empty;
                careerId = patient?.ma_nghe_nghiep ?? string.Empty;
                (_, _, _, var exameTypes) = await GetExameTypes();
                medicalHistory = patient?.lich_su_kham_benh?.Select(x => new MedicalHistory()
                {
                    Id = x.id_lich_su_kham ?? string.Empty,
                    HealthServiceId = x.id_phong_kham ?? string.Empty,
                    HealthServiceCode = x.ma_phong_kham ?? string.Empty,
                    HealthServiceName = x.ten_phong_kham ?? string.Empty,
                    ExameTypeId = x.id_loai_kham,
                    ExameType = x.ten_loai_kham ?? string.Empty,
                    ClinicGroupId = x.id_nhom_phong_kham ?? string.Empty,
                    ClinicGroup = x.ten_nhom_phong_kham ?? string.Empty,
                    ClinicId = x.id_khoa ?? string.Empty,
                    ClinicCode = x.ma_khoa ?? string.Empty,
                    Clinic = x.ten_khoa ?? string.Empty,
                    ExaminationHour = x.ngay_dang_ky,
                    IsInsurance = exameTypes.Find(y => y.Id == x.id_loai_kham)?.IsInsurance ?? false,
                    Priority = x.uu_tien,
                })
                .OrderBy(mh => DateTime.TryParseExact(mh.ExaminationHour, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime examTime) ? examTime : DateTime.MaxValue)
                .ToList()
                ?? [];
                rejectReason = patient?.ly_do_tu_choi ?? string.Empty;
                isReject = patient?.tiep_nhan_kham == 0;
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, new HisCustomerDto
            {
                PatientId = patientId,
                PatientCode = patientCodeResponse,
                CareerId = careerId,
                RejectReason = rejectReason,
                IsReject = isReject,
                MedicalHistory = medicalHistory
            });
        }



        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            bool result = false;
            string message = string.Empty;
            string refNo = string.Empty;
            RegisterFormsResponseDto data = new();

            try
            {
                if (request.Service.Count == 0)
                {
                    return (false, "No services provided", ErrorTypeEnum.MediPayError, string.Empty, new RegisterFormsResponseDto());
                }

                var doiTuongKcb = request.MedicalTreatmentCategoryId;

                if (string.IsNullOrEmpty(doiTuongKcb))
                {
                    var defaultDoiTuongKcb = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_default")
                    : null;

                    doiTuongKcb = defaultDoiTuongKcb?.Value;
                }

                if (string.IsNullOrEmpty(doiTuongKcb))
                {
                    var defaultDoiTuongKcbMap = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "ma_doi_tuong_kcb_map_default")
                        : null;

                    if (defaultDoiTuongKcbMap is not null && !string.IsNullOrEmpty(defaultDoiTuongKcbMap.Value))
                    {
                        var doiTuongKCBMap = JsonConvert.DeserializeObject<Dictionary<string, string>>(defaultDoiTuongKcbMap?.Value ?? string.Empty);
                        if (doiTuongKCBMap is not null && doiTuongKCBMap.Count > 0)
                        {
                            doiTuongKcb = doiTuongKCBMap.ContainsKey(request.ExameTypeId ?? string.Empty)
                                ? doiTuongKCBMap[request.ExameTypeId ?? string.Empty] : "1";
                        }
                    }
                }

                var nationalityId = request.Customer.NationalityId ?? string.Empty;

                var mapNationalities = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                        : null;

                if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
                {
                    var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                    if (nationalities is not null && nationalities.Count > 0)
                    {
                        nationalityId = nationalities.TryGetValue(nationalityId, out string? value) ? value : nationalityId;
                    }
                }

                var careerId = request.Customer.CareerId ?? string.Empty;
                var mapCareers = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_careers")
                    : null;

                if (mapCareers is not null && !string.IsNullOrEmpty(mapCareers.Value))
                {
                    var careers = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapCareers?.Value ?? string.Empty);
                    if (careers is not null && careers.Count > 0)
                    {
                        careerId = careers.TryGetValue(careerId, out string? value) ? value : careerId;
                    }
                }

                var defaultNation = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                    : null;

                // Create a list of ServiceInfoModel objects from the request
                var serviceInfoModels = request.Service.Select(service => new ServiceInfoModel
                {
                    id_khoa = service.ClinicIdRes ?? string.Empty, //354 sẽ nhận id phòng khám từ response phong-kham qua đây
                    id_phong_kham = service.Id ?? string.Empty,
                    id_nhom_phong_kham = service.ClinicGroupId ?? string.Empty, //354 sẽ nhận id đối tương khám qua đây
                    ten_phong_kham = service.Name ?? string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (service.InsurancePrice ?? 0) : (service.UnitPrice ?? 0),
                    id_khung_thoi_gian = string.Empty,
                    id_loai_kham = service.ExameTypeIdRes ?? string.Empty, //354 sẽ nhận id đối tương khám từ response phong-kham qua đây
                }).ToList();

                string url = $"{hisConfig.Host}/dangky-n-kcb";
                var req = new CreateRegisterFormWithMultiServiceRequest()
                {
                    thong_tin_benh_nhan = new HisClientV7.Lib.Request.PatientInfoModel()
                    {
                        id_bn = request.CustomerHospital?.PatientId ?? string.Empty,
                        ma_bn = request.CustomerHospital?.PatientCode ?? string.Empty,
                        ten_bn = request.Customer.LastName ?? string.Empty,
                        ho_bn = request.Customer.FirstName ?? string.Empty,
                        ho_ten = request.Customer.GetFullName().ToUpper(),
                        dia_chi = request.Customer.Street ?? string.Empty,
                        dia_chi_day_du = request.Customer.Address ?? string.Empty,
                        dia_chi_bhyt = request.Insurance?.RegisterAddress ?? string.Empty,
                        ma_dantoc = nationalityId,
                        ma_quoctich = defaultNation?.Value ?? request.Customer.Nation ?? string.Empty,
                        matinh_cutru = request.Customer.ProvinceId ?? string.Empty,
                        mahuyen_cu_tru = request.Customer.DistrictId ?? string.Empty,
                        maxa_cu_tru = request.Customer.WardId ?? string.Empty,
                        ma_dinh_danh = request.Customer.IdentityNo ?? string.Empty,
                        gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                        dien_thoai = request.Customer.Phone ?? string.Empty,
                        ma_the_bhyt = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                        ma_nghe_nghiep = careerId,
                        ma_nghe_nghiep_his = request.CustomerHospital?.CareerId ?? string.Empty,
                        nhom_mau = string.Empty,
                        so_gttt = request.Customer.IdentityNo ?? string.Empty,
                        ma_dkbd = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                        ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                        ngay_du_5_nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                        gt_the_tu = request.IsInsurance ?
                            DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                            : string.Empty,
                        gt_the_den = request.IsInsurance ?
                            DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd")
                            : string.Empty,
                        ngay_cap_gttt = request.Customer.IdentityIssueDate?.ToString("yyyy-MM-dd") ?? string.Empty,
                        noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                        ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                        ngay_vao_noi_tru = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                        ly_do_vnt = "Khám bệnh",
                        ma_doituong_kcb = doiTuongKcb ?? "1",
                        ma_loai_kcb = "01",
                        ma_doituong_kcb_his = request.MedicalTreatmentCategoryHisId ?? string.Empty,
                        ma_kv = request.Insurance?.AreaCode ?? string.Empty,
                        noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                        dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                        quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                        ho_ten_nt = request.CustomerRelationship.GetFullName(),
                        ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                            ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                            : string.Empty,
                        dia_chi_nt = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                        phan_tuyen = request.IsInsurance ? int.Parse(request.Insurance?.ReferralLevel ?? string.Empty) : null,
                        dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                        ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                        chan_doan_tuyen_duoi = request.TransferReferralDiagnosisInfo ?? string.Empty,
                        cs_can_nang = request.Weight?.ToString() ?? string.Empty,
                        cs_chieu_cao = request.Height?.ToString() ?? string.Empty,
                        cs_nhiet_do = request.Temperature?.ToString() ?? string.Empty,
                        cs_mach = request.PulseRate?.ToString() ?? string.Empty,
                        uu_tien = request.Priority,
                    },
                    thong_tin_dich_vu = serviceInfoModels,
                    du_phong = string.Empty,
                    id_thiet_bi = request.DeviceId ?? string.Empty,
                    so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                    ngay_chuyen_tuyen = string.Empty,
                    ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                    don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty,
                };

                Log.Information("{LogPrefix} CreateRegisterFormWithMultiService Req --- {@Request}", logPrefix, req);

                var client = httpClientFactory.CreateClient();
                (result, message, RegisterFormWithMultiServiceModel registerDto) = await client.CreateRegisterFormWithMultiService(req, url, hisConfig.MerchantId, hisConfig.SecretKey);

                Log.Information("{LogPrefix} CreateRegisterFormWithMultiService Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

                if (result)
                {
                    refNo = registerDto.thong_tin_tiep_nhan.id_lk ?? string.Empty;
                    data = new RegisterFormsResponseDto
                    {
                        QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                        QueueNumberPriority = request.Priority.ToString(),
                        RegisterNumber = refNo,
                        ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                        ExaminationLocation = string.Empty,
                        MedicalTreatmentCategoryName = string.Empty,
                        RateOfInsurance = "0",
                        PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty,
                        PatientId = registerDto.thong_tin_benh_nhan?.id_bn ?? string.Empty,
                        RegisterDetails = [.. registerDto.thong_tin_dang_ky.Select(detail => new RegisterDetailDto
                        {
                            RegisterDetailId = detail.id_dang_ky,
                            RegisterId = refNo,
                            ExaminationLocation = detail.ten_khu_vuc ?? string.Empty,
                            HealthServiceId = detail.chi_tiet_dang_ky.id_phong_kham,
                            HealthServiceName = detail.chi_tiet_dang_ky.ten_phong_kham,
                            ServiceType = detail.chi_tiet_dang_ky.loai_dich_vu,
                            Unit = detail.chi_tiet_dang_ky.don_vi,
                            Quantity = detail.chi_tiet_dang_ky.so_luong,
                            UnitOfMeasure = detail.chi_tiet_dang_ky.dvt,
                            UnitPrice = detail.don_gia_dang_ky,
                            Status = detail.chi_tiet_dang_ky.trang_thai,
                            QueueNumber = detail.stt_lk
                        })],
                    };
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"Error creating register form with multiple services: {ex.Message}";
                Log.Error(ex, "{LogPrefix} Error creating register form with multiple services", logPrefix);
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }
    }
}
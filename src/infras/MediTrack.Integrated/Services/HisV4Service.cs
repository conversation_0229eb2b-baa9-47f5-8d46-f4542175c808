﻿using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using HisClientV4.Lib;
using HisClientV4.Lib.Model;
using MediTrack.Domain.Helpers;
using MediTrack.Domain.Constants;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using Serilog;
using MediTrack.Application.Features.HisLogic.Dtos;
using Microsoft.EntityFrameworkCore;
using HisClientV4.Lib.Request;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using MediTrack.Application.Helpers;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Ultils.Extensions;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using System.Text.RegularExpressions;
using MediTrack.Domain.Enums;
using MediTrack.Application.Features.SearchLogic.Dtos;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// VNPT L1: Bến Lức - Long An, Bệnh viện Ninh Phước, Bệnh viện Ninh Sơn, TTYT Ba Đồn Quảng Bình, BV. Đa khoa tỉnh Đăk Nông
    /// Ẩn giá lúc lấy dịch vụ, lấy giá khi response tạo phiếu
    /// </summary>
    /// <param name="current"></param>
    /// <param name="hisConfig"></param>
    public class HisV4Service : HisServiceParameters, IHisService
    {
        private readonly HisV4Config hisConfig;

        public HisV4Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV4Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV4Config is null");
            hisConfig.TenThuNgan = string.IsNullOrEmpty(hisConfig.TenThuNgan) ? hisConfig.UserName : hisConfig.TenThuNgan;
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            //get healthcare service tier from hospital metadata
            var tiers = await hospitalMetadataRepository!.GetHospitalMetadataByKeyAsync(current.HospitalId, "healthcare_service_tiers");

            if (tiers is null || string.IsNullOrEmpty(tiers.Value))
            {
                return (false, "Không tìm thấy cấu hình thông tin tuyến", ErrorTypeEnum.HisError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, JsonConvert.DeserializeObject<List<GetHealthcareServiceTierDto>>(tiers.Value) ?? []);
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)>((true, string.Empty, ErrorTypeEnum.NoError, new List<GetMedicalTreatmentCategoryDto>()));
        }

        public async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            // Số ưu tiên nối tiếp số thường, bỏ qua Priority
            var currentQueue = await databaseService.DialerQueues.FirstOrDefaultAsync(a =>
                a.QueueDate.Date == DateTimeHelper.GetCurrentLocalDateTime().Date &&
                a.HospitalId == current.HospitalId &&
                (string.IsNullOrEmpty(request.MaPhong) || a.HealthServiceId == request.MaPhong)
            );

            if (currentQueue is not null)
            {
                currentQueue.QueueNumber += 1;
                databaseService.DialerQueues.Update(currentQueue);
            }
            else
            {
                currentQueue = new DialerQueue
                {
                    Id = Guid.NewGuid().ToString(),
                    HealthServiceId = request.MaPhong,
                    QueueDate = DateTimeHelper.GetCurrentLocalDateTime(),
                    QueueNumber = 1,
                    HospitalId = current.HospitalId,
                };
                databaseService.DialerQueues.Add(currentQueue);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
            {
                Id = currentQueue.Id,
                SoThuTu = currentQueue.QueueNumber,
                UuTien = request.UuTien == 1,
                NgayDangKy = currentQueue.QueueDate.AddHours(7),
                MaPhong = currentQueue.HealthServiceId,
            });
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new object()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            var payment = await databaseService.Payments
                .FirstOrDefaultAsync(x => x.RefNo == refNo && x.HospitalId == current.HospitalId);

            if (payment is null)
            {
                return (false, PaymentConstant.NotFound, ErrorTypeEnum.HisError, new PaymentStatusDto());
            }

            if (!current.IsGenQR || payment.Status == PaymentConstant.Success)
            {
                return (true, "Success", ErrorTypeEnum.NoError, new PaymentStatusDto()
                {
                    Number = payment.Id ?? string.Empty,
                    PaymentStatus = payment.Status ?? PaymentConstant.WaitForPayment,
                    InvoiceInfoRef = payment.InvoiceInfoRef ?? string.Empty,
                    RefNo = refNo,
                });
            }

            PaymentStatusDto status = new();

            string checkSum = SignatureHelper.Sha256Hash($"{hisConfig.HospitalId}|{refNo}|{hisConfig.Dvtt}|{hisConfig.SecrectKey}");

            string url = $"{hisConfig.PaymentUrl}/public/{hisConfig.Dvtt}/check-trans/{hisConfig.HospitalId}?sohoadon={refNo}&checksum={checkSum}";

            Log.Information("{LogPrefix} CheckPaymentStatus --- Url: {Url}", logPrefix, url);
            (bool result, string message, CheckPaymentModel resHis) = await httpClientFactory.CreateClient().CheckPayment(url);
            Log.Information("{LogPrefix} CheckPaymentStatus --- Result: {Result} - Message: {Message} - {@ResHis}", logPrefix, result, message, resHis);

            if (result)
            {
                status.Number = payment.Id ?? string.Empty;
                status.RefNo = refNo;
                status.InvoiceInfoRef = payment.InvoiceInfoRef ?? string.Empty;
                status.PaymentStatus = resHis.trangThaiThanhToan == "00" ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
            }

            return (result, message, ErrorTypeEnum.NoError, status);
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            //1. Auth
            bool result;
            string message;
            (result, message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, refNo, data);
            }

            var relationShipId = request.CustomerRelationshipId ?? string.Empty;
            var mapRelationShips = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "id_quan_he_nt_map")
                : null;

            if (mapRelationShips is not null && !string.IsNullOrEmpty(mapRelationShips.Value))
            {
                var relationShips = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(mapRelationShips.Value);
                if (relationShips is not null && relationShips.Count > 0)
                {
                    relationShipId = relationShips.FirstOrDefault(x => x.OriginId == request.CustomerRelationshipId)?.Id ?? relationShipId;
                }
            }

            var mapNationalities = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                        : null;
            int? nationalityId = null;
            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    var id = nationalities.ContainsKey(request.Customer?.NationalityId ?? string.Empty)
                    ? nationalities[request.Customer?.NationalityId ?? string.Empty] : string.Empty;

                    if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int resultId))
                    {
                        nationalityId = resultId;
                    }
                }
            }

            //2. Create register form
            string createRegisterUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/generator";

            if (string.IsNullOrEmpty(request.HealthcareServiceTierId)
                || !int.TryParse(request.HealthcareServiceTierId, out int healthcareServiceTierId))
            {
                if (!int.TryParse(DataConstant.ReferralLevelToRoutingType[
                    string.IsNullOrEmpty(request.Insurance?.ReferralLevel) ? "1" : request.Insurance?.ReferralLevel ?? "1"], out healthcareServiceTierId))
                {
                    healthcareServiceTierId = 1;
                }
            }

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            var req = new GeneratorRequest()
            {
                tenNb = request.Customer!.GetFullName(),
                ngaySinh = request.Customer!.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                uuTien = request.Priority != 0,
                dichVuId = int.Parse(request.Service.ClinicId ?? string.Empty),
                phongId = int.Parse(request.Service.Id ?? string.Empty),
                doiTuong = int.Parse(request.Service.ExameTypeId ?? string.Empty),
                khuVucId = 1,
                maTheBhyt = request.IsInsurance ? request.Insurance?.InsuranceNo : string.Empty,
                tinhThanhPhoId = request.Customer.ProvinceId ?? string.Empty,
                quanHuyenId = request.Customer.DistrictId ?? string.Empty,
                xaPhuongId = request.Customer.WardId ?? string.Empty,
                maNoiDKBD = request.IsInsurance ? request.Insurance?.RegisterPlaceID ?? string.Empty : string.Empty,
                diaChiTheBhyt = request.IsInsurance ? request.Insurance?.RegisterAddress ?? string.Empty : string.Empty,
                tuNgayTheBhyt = DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd"),
                denNgayTheBhyt = DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd"),
                tuyen = healthcareServiceTierId,
                soDienThoai = request.Customer.Phone,
                gioiTinh = request.Customer.Sex == "Nam" ? 1 : 0,
                nbDichVu = !request.IsInsurance,
                maNb = request.CustomerHospital.PatientCode ?? string.Empty,
                diaChi = request.Customer.Address,
                soCanCuoc = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                lyDoVaoVien = request.ReasonForVisit ?? string.Empty,
                ngheNghiepId = int.Parse(request.CustomerHospital.CareerId ?? string.Empty),
                danTocId = nationalityId ?? request.Customer.NationalityId.ToInt(),
                ngayBhytNamNam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                KhuVucSongBhyt = request.IsInsurance ? DataConstant.AreaCode[request.Insurance?.AreaCode ?? string.Empty] : string.Empty,
                tenICDChanDoanTuyenDuoi = request.IsInsurance ? request.TransferReferralDiseaseCodeAndName : string.Empty,
                noiChuyen = request.IsInsurance ? request.TransferReferralUnit : string.Empty,
                soChuyenTuyen = request.IsInsurance ? request.TransferReferralDocumentNumber : string.Empty,
                loaiDungTuyen = request.IsInsurance ? "0" : string.Empty, //0: giấy chuyển,
                ngayChuyenTuyen = request.IsInsurance ? DateTimeHelper.FormatDate(request.TransferReferralDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty, //(dd/MM/yyyy)
                hinhThucChuyenTuyen = request.IsInsurance ? request.TransferReferralType : string.Empty,
                lyDoChuyenTuyen = request.IsInsurance ? request.TransferReferralReason : string.Empty,
                hoTenNguoiGiamHo = request.CustomerRelationship?.GetFullName() ?? string.Empty,
                moiQuanhe = relationShipId ?? string.Empty,
                diaChiNguoiNha = request.CustomerRelationship?.Address ?? string.Empty,
                dienThoaiNguoiNha = request.CustomerRelationship?.Phone ?? string.Empty,
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(createRegisterUrl, accessToken, req);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, refNo, data);
            }

            refNo = registerDto.maHoSo ?? string.Empty;

            // Nếu là tạm ứng thì không lấy giá từ HIS trả về
            decimal? responeUnitPrice = (decimal)registerDto.tongTienThanhToan;

            if ((request.IsInsurance && current.IsInsuranceAdvancePayment)
                || (!request.IsInsurance && current.IsAdvancePayment))
            {
                responeUnitPrice = null;
            }

            data = new RegisterFormResponseDto
            {
                Clinic = request.Service.ClinicId ?? string.Empty,
                QrCode = string.Empty,
                QueueNumber = registerDto.sttKham,
                QueueNumberPriority = registerDto.uuTien ? "1" : "0",
                RegisterNumber = refNo,
                ReceiptRefNo = refNo,
                PaymentRefNo = refNo,
                RefDocNo = registerDto.phieuThuId.ToString(),
                ExaminationLocation = request.Service.ExaminationLocation ?? string.Empty,
                RateOfInsurance = "0",
                PatientCode = registerDto.maNb ?? string.Empty,
                ResponseUnitPrice = responeUnitPrice,
                HealthcareServiceTierId = registerDto.tuyen.ToString(),
            };
            var errorType = ErrorTypeEnum.NoError;

            //nếu bệnh viện tự gen qr thì trả về qr code
            if (current.IsGenQR && registerDto.tongTienThanhToan > 0 && request.PaymentType == "qr")
            {
                (result, message, errorType, QrPaymentDto payment) = await GenQRPayment(refNo);

                if (result && payment != null && payment.Amount.HasValue)
                {
                    data.QrCode = payment.QrCode;
                    data.ResponseUnitPrice = payment.Amount;
                    data.PaymentRefNo = payment.Number;
                    data.ReceiptRefNo = payment.Number;
                }
            }

            return (result, message, errorType, refNo, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (authResult)
            {
                string getClinicUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/service-catalog?chuyenKhoaId=0&doiTuongBnId={exameTypeId}&khuVucId=1&page=5&size=1";

                (bool getResult, string messageResult, List<ClinicModel> data) = await
                    httpClientFactory.CreateClient().GetClinic(getClinicUrl, accessToken);

                Log.Information("{LogPrefix} GetClinics --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, messageResult, data);

                if (getResult)
                {
                    list = [.. data.Select(a => new Clinic
                    {
                        Id = a.id.ToString() ?? string.Empty,
                        Code = a.ma ?? string.Empty,
                        Name = a.ten ?? string.Empty,
                        ExameTypeID = exameTypeId ?? string.Empty,
                    })];

                    result = true;
                    message = messageResult;
                }
            }
            else
            {
                message = authMessage;
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            string message = string.Empty;
            List<ExameType> list = [];

            (bool getResult, message, List<ExameTypeModel> exameTypeModels) = HisIntegrateV4Client.GetExameTypes();

            Log.Information("{LogPrefix} GetExameTypes --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, message, exameTypeModels);

            if (getResult)
            {
                list = [.. exameTypeModels.Select(x => new ExameType()
                {
                    Id = x.id_loai_kham,
                    Name = x.ten_loai_kham,
                    IsInsurance = x.is_insurance,
                })];
            }

            return Task.FromResult((getResult, message, ErrorTypeEnum.NoError, list));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (authResult)
            {
                // Lấy lại clinic để có đơn giá
                string getClinicUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/service-catalog?chuyenKhoaId=0&doiTuongBnId={request.ExameTypeId}&khuVucId=1&page=5&size=1";
                (bool getResult, string messageResult, List<ClinicModel> clinics) = await httpClientFactory.CreateClient().GetClinic(getClinicUrl, accessToken);

                if (!getResult)
                {
                    return (getResult, messageResult, ErrorTypeEnum.HisError, list);
                }

                var clinic = clinics.FirstOrDefault(x => x.id.ToString() == request.ClinicId);

                string getHealthServiceUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/waiting-room?dichVuId={request.ClinicId}&chuyenKhoaId=0&khuVucId=1&page=5&size=1";

                Log.Information("{LogPrefix} GetHealthServices --- Url: {Url}", logPrefix, getHealthServiceUrl);

                (getResult, messageResult, List<HealthServiceModel> data) = await
                    httpClientFactory.CreateClient().GetHealthServices(getHealthServiceUrl, accessToken, request.ClinicId ?? string.Empty);

                Log.Information("{LogPrefix} GetHealthServices --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, messageResult, data);

                if (getResult)
                {
                    var priceDisplay = await databaseService.HospitalMetaDatas
                    .Where(x => x.HospitalId == current.HospitalId
                    && x.GroupType == "price_display_default"
                    && (x.Code == "INSURANCE" || x.Code == "SERVICE")).ToListAsync();

                    var servicePriceDisplay = priceDisplay.Find(x => x.Code == "SERVICE");
                    var insurancePriceDisplay = priceDisplay.Find(x => x.Code == "INSURANCE");

                    list = [.. data.Select(a => new HealthService
                    {
                        Id = a.id.ToString() ?? string.Empty,
                        Name = a.ten ?? string.Empty,
                        UnitPrice = current.IsAdvancePayment ? null
                            : (request.ExameTypeId == "1" ? clinic?.donGiaVienPhi ?? 0 : clinic?.donGiaDichVu ?? 0), // 1: Khám viện phí, 2: Khám BHYT, 3: Khám dịch vụ
                        UnitPriceDisplay = servicePriceDisplay?.Value
                            ?? (current.IsAdvancePayment ? "Tạm ứng" : ((request.ExameTypeId == "1" ? clinic?.donGiaVienPhi ?? 0 : clinic?.donGiaDichVu ?? 0).ToString("N0") ?? "0") + " đ"),
                        InsurancePrice = current.IsInsuranceAdvancePayment ? null : (clinic?.donGiaBaoHiem ?? 0),
                        ExameTypeId = request.ExameTypeId ?? string.Empty,
                        ClinicId = request.ClinicId ?? string.Empty,
                        ClinicCode = request.ClinicCode ?? string.Empty,
                        SubClinicId = request.SubClinicId ?? string.Empty,
                        InsurancePriceDisplay = insurancePriceDisplay?.Value
                            ?? (current.IsInsuranceAdvancePayment ? "Tạm ứng" : (clinic?.donGiaBaoHiem.ToString("N0") ?? "0") + " đ"),
                        ExaminationLocation = a.diaChi ?? string.Empty,
                        IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                        WaitingPatientCount = a.choKham,
                        ProcessingNumber = a.dangKy,
                    })];

                    result = true;
                    message = messageResult;
                }
            }
            else
            {
                message = authMessage;
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            bool result;
            string message;

            (result, message, string accessToken) = await GetAccessToken();

            if (result)
            {
                string url = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/get-information-patient";
                var checkReq = new GetCustomerRequest()
                {
                    code = customer.IdentityNo ?? string.Empty,
                    option = 1
                };

                Log.Information("{LogPrefix} GetCustomerHis --- Req: {@Req}", logPrefix, checkReq);
                (result, message, GetCustomerModel data) = await httpClientFactory.CreateClient().GetCustomerHis(url, accessToken, checkReq);
                Log.Information("{LogPrefix} GetCustomerHis --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, result, message, data);

                return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto
                {
                    PatientCode = data.MaNb ?? string.Empty,
                    PatientId = data.MaNb ?? string.Empty,
                    CareerId = data.NgheNghiepId.ToString(),
                });
            }

            return (result, message, ErrorTypeEnum.HisError, new HisCustomerDto
            {
                PatientCode = string.Empty,
                PatientId = string.Empty,
                CareerId = string.Empty,
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            bool result = false;
            string message = string.Empty;
            List<GetSocialCareerDto> list = [];

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (authResult)
            {
                string url = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/career";

                (bool getResult, string messageResult, List<SocialCareerModel> data) = await httpClientFactory.CreateClient().GetSocialCareers(url, accessToken);

                Log.Information("{LogPrefix} GetSocialCareers --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, messageResult, data);

                string defaultSocialCareer = hospitalMetadataRepository != null
                        ? (await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_social_career"))?.Value ?? string.Empty
                        : string.Empty;

                if (getResult)
                {
                    list = [.. data.Select(a => new GetSocialCareerDto
                    {
                        Id = a.id ?? string.Empty,
                        Name = a.ten ?? string.Empty,
                        IsDefault = a.id == defaultSocialCareer
                    })];
                    result = true;
                    message = messageResult;
                }
            }
            else
            {
                message = authMessage;
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }
            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            if (current.IsInsuranceAdvancePayment || current.IsAdvancePayment)
            {
                //get advance money from db
                var advancedMoneyMeta = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "advance_money")
                    : null;

                if (advancedMoneyMeta != null && !string.IsNullOrEmpty(advancedMoneyMeta.Value))
                {
                    return (true, string.Empty, ErrorTypeEnum.NoError, advancedMoneyMeta.Value.Split(";") ?? []);
                }
            }
            return (false, string.Empty, ErrorTypeEnum.HisError, null);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        /// <summary>
        /// Cập nhật trạng thái thanh toán sang HIS (Luồng MediPay gen QR)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            UpdatePaymentStatusResponse data = new();

            //1. Auth
            bool result;
            string message;

            (result, message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, data);
            }

            //2. Update payment status
            var register = await databaseService.Registers.FindAsync(request.RegisterNumber);
            if (register is null)
            {
                return (false, "Không tìm thấy thông tin đăng ký", ErrorTypeEnum.HisError, data);
            }
            string url = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/advance-payment";
            var req = new UpdatePaymentRequest
            {
                maHoSo = request.InvoiceId,
                maNb = request.PatientCode,
                maGiaoDich = request.TransactionId,
                thoiGianThanhToanUTC = (request.PaidTime ?? DateTime.UtcNow).ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                trangThaiThanhToan = request.Status == "00",
                hinhThucThanhToan = 2, //Hình thức thanh toán. Định nghĩa: 1-POS, 2: QR
                maThietBi = request.KioskId,
                tenThuNgan = hisConfig.TenThuNgan,
                maPOS = string.Empty,
                soTien = request.PaidAmount
            };

            // nếu là tạm ứng
            if ((register.HealthInsurance == 1 && current.IsInsuranceAdvancePayment)
                || (register.HealthInsurance == 0 && current.IsAdvancePayment))
            {
                req.loaiPhieuThuId = "3";
                req.checksum = SignatureHelper.ComputeMD5Hash($"{request.InvoiceId}|{request.TransactionId}|{req.thoiGianThanhToanUTC}|{request.PatientCode}|{(request.Status == "00" ? "true" : "false")}|2|{request.KioskId}|{hisConfig.TenThuNgan}||{request.PaidAmount}|{hisConfig.SecrectKey}");

                Log.Information("{LogPrefix} UpdateAdvancePaymentStatus Req --- {@Request}", logPrefix, req);
                (result, message, UpdatePaymentResponse response) = await httpClientFactory.CreateClient().UpdatePaymentStatus(url, accessToken, req);
                Log.Information("{LogPrefix} UpdateAdvancePaymentStatus Res --- Result: {Result} - Message: {Message} - {@Response}", logPrefix, result, message, response);

                data.IpnResult = result;
                data.IpnMessage = message;
                data.IpnResponse = message;
            }
            else // nếu không phải luồng tạm ứng
            {
                url = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/update-payment";

                req.phieuThuId = int.Parse(request.PaymentRefDocNo);
                req.checksum = SignatureHelper.ComputeMD5Hash($"{request.InvoiceId}|{request.PatientCode}|{request.TransactionId}|2|{request.KioskId}|{string.Empty}|{request.PaidAmount}|{request.PaymentRefDocNo}|{hisConfig.SecrectKey}");

                Log.Information("{LogPrefix} UpdatePaymentStatus Req --- {@Request}", logPrefix, req);
                (result, message, UpdatePaymentResponse response) = await httpClientFactory.CreateClient().UpdatePaymentStatus(url, accessToken, req);
                Log.Information("{LogPrefix} UpdatePaymentStatus Res --- Result: {Result} - Message: {Message} - {@Response}", logPrefix, result, message, response);

                data.IpnResult = result;
                data.IpnMessage = message;
                data.IpnResponse = message;
            }

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        /// <summary>
        /// Cập nhật trạng thái thanh toán cận lâm sàng HIS (Luồng MediPay gen QR)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            UpdateParaclinicalPaymentStatusResponse data = new();
            bool result = true;
            string message = string.Empty;

            //Get lại Indications
            (result, message, ErrorTypeEnum errorType, var indicationSearch) = await GetParaclinicalIndications(new IndicationSearchRequest
            {
                Code = request.InvoiceId,
                Option = 3,
            });
            await cachedService.SetAsync($"PARACLINICALINDICATIONS_{current.HospitalId}{request.InvoiceId}3", indicationSearch, expireSecond: 900);

            if (!result)
            {
                return (result, message, errorType, data);
            }

            (result, message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, data);
            }

            string url = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/sublinical-payment";
            var paidTime = request.PaidTime ?? DateTime.UtcNow;
            var req = new UpdateParaclinicalPaymentRequest
            {
                maHoSo = request.InvoiceId,
                maNb = indicationSearch.PatientCode ?? string.Empty,
                maGiaoDich = request.TransactionId,
                chiDinh = indicationSearch.Indications?.Select(a => new ChiDinh
                {
                    soPhieu = a.IndicationId ?? string.Empty,
                    chiTietChiDinh = a.Services?.Select(b => new ChiTietChiDinh
                    {
                        dichVuId = long.TryParse(b.ServiceId, out long serviceId) ? serviceId : 0,
                        soTien = b.PaymentAmount ?? 0,
                    }).ToList()
                }).ToList(),
                thoiGianThanhToanUTC = paidTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                trangThaiThanhToan = request.Status == "00",
                hinhThucThanhToan = 2, //Hình thức thanh toán. Định nghĩa: 1-POS, 2: QR
                maThietBi = request.KioskId,
                tenThuNgan = hisConfig.TenThuNgan,
                maPOS = string.Empty,
                soTien = request.PaidAmount,
                checksum = SignatureHelper.ComputeMD5Hash($"{request.InvoiceId}|{request.TransactionId}|{paidTime:yyyy-MM-ddTHH:mm:ss.fffZ}|{indicationSearch.PatientCode}|{(request.Status == "00" ? "true" : "false")}|2|{request.KioskId}|{hisConfig.TenThuNgan}||{request.PaidAmount}|{hisConfig.SecrectKey}")
            };

            Log.Information("{LogPrefix} UpdateParaclinicalPaymentStatus Req --- {@Request}", logPrefix, req);

            (result, message, UpdatePaymentResponse response) = await httpClientFactory.CreateClient().UpdateParaclinicalPaymentStatus(url, accessToken, req);

            Log.Information("{LogPrefix} UpdateParaclinicalPaymentStatus Res --- Result: {Result} - Message: {Message} - {@Response}", logPrefix, result, message, response);

            data.IpnResult = result;
            data.IpnMessage = message;
            data.IpnResponse = response.hoaDon?.soHoaDon;
            data.Url = url;

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            bool result = false;
            string message = string.Empty;
            IndicationSearchResponse data = new();

            Customer customer = request.Option == 2
                ? await databaseService.Customers.FirstOrDefaultAsync(x => x.IdentityNo == request.Code) ?? new()
                : new();

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (authResult)
            {
                string code = request.Code;
                int option = request.Option;
                string getParaclinicUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/his/get-sublinical-payment?option={option}&code={code}";

                Log.Information("{LogPrefix} GetParaclinicalIndications Req --- url: {url}", logPrefix, getParaclinicUrl);
                (bool getResult, string messageResult, ParaclinicalModel paraclinicalModel) = await
                    httpClientFactory.CreateClient().GetIndications(getParaclinicUrl, accessToken);

                Log.Information("{LogPrefix} GetParaclinicalIndications Res --- Result: {Result} - Message: {Message} - Data: {@Data}", logPrefix, getResult, messageResult, paraclinicalModel);

                if (getResult)
                {
                    data = new IndicationSearchResponse
                    {
                        HealthInsuranceNo = paraclinicalModel.MaTheBhyt ?? customer.HealthInsuranceNo ?? string.Empty,
                        PatientCode = paraclinicalModel.MaNb ?? string.Empty,
                        FullName = paraclinicalModel.TenNb ?? customer.GetFullName() ?? string.Empty,
                        Sex = paraclinicalModel.GioiTinh == 1 ? "Nam" : "Nữ",
                        Phone = paraclinicalModel.SoDienThoai ?? customer.Phone ?? string.Empty,
                        DateOfBirth = paraclinicalModel.NgaySinh ?? customer.DateOfBirth,
                        Address = paraclinicalModel.DiaChi ?? customer.Address ?? string.Empty,
                        RefNo = paraclinicalModel.MaHoSo ?? string.Empty,
                        Indications = paraclinicalModel.ChiDinh?.Select(b => new Indication()
                        {
                            IndicationId = b.SoPhieu ?? string.Empty,
                            IndicationName = b.LoaiChiDinh ?? string.Empty,
                            IndicationType = b.LoaiChiDinhId.ToString(),
                            ClinicName = b.ChuyenKhoa ?? string.Empty,
                            TotalAmount = b.TongTien,
                            PaymentTotalAmount = b.TongTienNguoiBenhTra,
                            PaymentStatus = b.TrangThaiThanhToan ? "Success" : "Fail",
                            Services = b.ChiTietChiDinh?.Select(c => new Service()
                            {
                                ServiceId = c.DichVuKhamBenhID.ToString(),
                                ServiceCode = c.MaDichVu ?? string.Empty,
                                ServiceName = c.TenDichVu ?? string.Empty,
                                Quantity = c.SoLuong,
                                UnitPrice = c.DonGia,
                                PaymentAmount = c.TongTienNguoiBenhTra,
                                Amount = c.TongTien,
                            }).ToList()
                        }).ToList(),
                        TotalAmount = paraclinicalModel.TongTien,
                    };
                    result = true;
                }
                message = messageResult;
            }
            else
            {
                message = authMessage;
                return (authResult, authMessage, ErrorTypeEnum.HisError, data);
            }
            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            var result = false;
            var message = string.Empty;
            QrPaymentDto data = new();
            string checkSum = SignatureHelper.Sha256Hash($"{hisConfig.HospitalId}|{refNo}|{hisConfig.Dvtt}|{hisConfig.SecrectKey}");
            string createPaymentUrl = $"{hisConfig.PaymentUrl}/public/{hisConfig.Dvtt}/vantin/{hisConfig.HospitalId}";
            var reqHis = new CreatePaymentRequest()
            {
                maHoSo = refNo,
                checksum = checkSum,
            };

            Log.Information("{LogPrefix} GenQRPayment Req --- Url: {Url} - Request: {@Request}", logPrefix, createPaymentUrl, reqHis);
            (result, message, PaymentModel payment) = await httpClientFactory.CreateClient().CreatePayment(createPaymentUrl, reqHis);
            Log.Information("{LogPrefix} GenQRPayment Res --- Result: {Result} - Message: {Message} - {@Payment}", logPrefix, result, message, payment);
            if (result && payment != null)
            {
                data = new QrPaymentDto
                {
                    Number = payment.soHoaDon ?? string.Empty,
                    Amount = decimal.TryParse(payment.soTien ?? string.Empty, out var amount) ? amount : null,
                    QrCode = payment.qrCode ?? string.Empty,
                    TransactionDescription = payment.maHoSo ?? string.Empty,
                };
            }
            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, data);
        }
        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        private async Task<(bool, string, string)> GetAccessToken()
        {
            string accessToken = await cachedService.GetAsync<string>($"AccessToken_{current.HospitalId}") ?? string.Empty;
            if (!string.IsNullOrEmpty(accessToken))
            {
                return (true, string.Empty, accessToken);
            }
            string authUrl = $"{hisConfig.Host}/vnpthis/kiosk/api/v1/authen/login";
            bool result;
            string message;
            (result, message, accessToken, int expires_in) = await
                httpClientFactory.CreateClient().GetAccessToken(new AuthRequest()
                {
                    username = hisConfig.UserName,
                    password = hisConfig.Password,
                    hospitalId = hisConfig.HospitalId,
                }, authUrl);
            if (result && expires_in > 0)
            {
                await cachedService.SetAsync($"AccessToken_{current.HospitalId}", accessToken, expireSecond: expires_in);
            }
            return (result, message, accessToken);
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

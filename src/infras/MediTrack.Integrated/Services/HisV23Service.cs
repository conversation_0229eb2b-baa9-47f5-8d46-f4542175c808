﻿using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Integrated.Config;
using HisClientV15.Lib;
using HisClientV15.Lib.Model;
using MediTrack.Domain.Helpers;
using Serilog;
using HisClientV15.Lib.Request;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using MediTrack.Ultils.Extensions;
using System.Text.RegularExpressions;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Domain.Enums;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// VNPT L3: Tương tự VPNT L3 nhưng tuyến dành riêng cho Gò Công
    /// [
    // {"Id":"1.1","Name":"Đúng tuyến"},
    // {"Id":"1.3","Name":"Chuyển tuyến"},
    // {"Id":"1.5","Name":"<PERSON><PERSON><PERSON> kh<PERSON><PERSON>"},
    // {"Id":"2","Name":"<PERSON><PERSON><PERSON> c<PERSON><PERSON>"},
    // {"Id":"3.5","Name":"<PERSON>r<PERSON><PERSON> tuyến"}
    // ]
    /// </summary>
    public class HisV23Service : HisV15Service
    {
        private readonly HisV15Config hisConfig;

        public HisV23Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV15Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV23Config is null");
        }

        public override async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            //1. Auth
            bool result;
            string message;
            (result, message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, refNo, data);
            }

            var relationShipId = request.CustomerRelationshipId ?? string.Empty;
            var mapRelationShips = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "id_quan_he_nt_map")
                : null;

            if (mapRelationShips is not null && !string.IsNullOrEmpty(mapRelationShips.Value))
            {
                var relationShips = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(mapRelationShips.Value);
                if (relationShips is not null && relationShips.Count > 0)
                {
                    relationShipId = relationShips.FirstOrDefault(x => x.OriginId == request.CustomerRelationshipId)?.Id ?? relationShipId;
                }
            }

            var mapNationalities = hospitalMetadataRepository != null
                        ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                        : null;
            int? nationalityId = null;
            if (mapNationalities is not null && !string.IsNullOrEmpty(mapNationalities.Value))
            {
                var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
                if (nationalities is not null && nationalities.Count > 0)
                {
                    var id = nationalities.ContainsKey(request.Customer?.NationalityId ?? string.Empty)
                    ? nationalities[request.Customer?.NationalityId ?? string.Empty] : string.Empty;

                    if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int resultId))
                    {
                        nationalityId = resultId;
                    }
                }
            }

            //2. Create register form
            string createRegisterUrl = $"{hisConfig.Host}/web_his/kiosk/api/v1/his/generator";

            //Nếu truyền null
            if (string.IsNullOrEmpty(request.HealthcareServiceTierId))
            {
                //Tự lấy mặc định theo referral level
                //Nếu không có referral level thì lấy mặc định là 1.1
                if (string.IsNullOrEmpty(DataConstant.ReferralLevelToRoutingTypeV23[
                    string.IsNullOrEmpty(request.Insurance?.ReferralLevel) ? "1" : request.Insurance?.ReferralLevel ?? "1"]))
                {
                    request.HealthcareServiceTierId = "1.1";
                }
            }

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            var req = new GeneratorRequest()
            {
                tenNb = request.Customer!.GetFullName(),
                ngaySinh = request.Customer!.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                uuTien = "0",
                dichVuId = int.Parse(request.Service.ClinicId ?? string.Empty),
                phongId = int.Parse(request.Service.Id ?? string.Empty),
                doiTuong = int.Parse(request.Service.ExameTypeId ?? string.Empty),
                khuVucId = 1,
                maTheBhyt = request.IsInsurance ? request.Insurance?.InsuranceNo : string.Empty,
                tinhThanhPhoId = request.Customer.ProvinceId ?? string.Empty,
                quanHuyenId = request.Customer.DistrictId ?? string.Empty,
                xaPhuongId = request.Customer.WardId ?? string.Empty,
                maNoiDKBD = request.IsInsurance ? request.Insurance?.RegisterPlaceID ?? string.Empty : string.Empty,
                diaChiTheBhyt = request.IsInsurance ? request.Insurance?.RegisterAddress ?? string.Empty : string.Empty,
                tuNgayTheBhyt = DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd"),
                denNgayTheBhyt = DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd"),
                tuyen = request.HealthcareServiceTierId ?? "1.1",
                soDienThoai = request.Customer.Phone,
                gioiTinh = request.Customer.Sex == "Nam" ? 1 : 0,
                nbDichVu = !request.IsInsurance,
                maNb = request.CustomerHospital.PatientCode ?? string.Empty,
                diaChi = request.Customer.Address,
                soCanCuoc = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                lyDoVaoVien = request.ReasonForVisit ?? string.Empty,
                ngheNghiepId = int.Parse(request.CustomerHospital.CareerId ?? string.Empty),
                dantocId = nationalityId ?? request.Customer.NationalityId.ToInt(),
                ngayBhytNamNam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                KhuVucSongBhyt = request.IsInsurance ? request.Insurance?.AreaCode ?? string.Empty : string.Empty,
                tenICDChanDoanTuyenDuoi = request.IsInsurance ? request.TransferReferralDiseaseCodeAndName : string.Empty,
                noiChuyen = request.IsInsurance ? request.TransferReferralUnit : string.Empty,
                soChuyenTuyen = request.IsInsurance ? request.TransferReferralDocumentNumber : string.Empty,
                loaiDungTuyen = request.IsInsurance ? "0" : string.Empty, //0: giấy chuyển,
                ngayChuyenTuyen = request.IsInsurance ? DateTimeHelper.FormatDate(request.TransferReferralDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty, //(dd/MM/yyyy)
                hinhThucChuyenTuyen = request.IsInsurance ? request.TransferReferralType : string.Empty,
                lyDoChuyenTuyen = request.IsInsurance ? request.TransferReferralReason : string.Empty,
                hoTenNguoiGiamHo = request.CustomerRelationship?.GetFullName() ?? string.Empty,
                moiQuanhe = relationShipId ?? string.Empty,
                diaChiNguoiNha = request.CustomerRelationship?.Address ?? string.Empty,
                dienThoaiNguoiNha = request.CustomerRelationship?.Phone ?? string.Empty,
                chanDoanVao = request.DiagnosisBeforeAdmission ?? string.Empty,
                icdChanDoanVao = request.DiagnosisBeforeAdmissionICD ?? string.Empty
            };

            // Nếu bệnh viện không có luồng người thân thì các field này khi serialize sẽ không xuất hiện trong json
            if (!current.IsRegisterRelativeDefault)
            {
                req.hoTenNguoiGiamHo = null;
                req.moiQuanhe = null;
                req.diaChiNguoiNha = null;
                req.dienThoaiNguoiNha = null;
            }

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(createRegisterUrl, accessToken, req);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (!result)
            {
                return (result, message, ErrorTypeEnum.HisError, refNo, data);
            }

            refNo = registerDto.maHoSo ?? string.Empty;
            data = new RegisterFormResponseDto
            {
                Clinic = request.Service.ClinicId ?? string.Empty,
                QrCode = string.Empty,
                QueueNumber = registerDto.sttKham,
                QueueNumberPriority = "0",
                RegisterNumber = refNo,
                ReceiptRefNo = refNo,
                PaymentRefNo = refNo,
                RefDocNo = registerDto.phieuThuId.ToString()!,
                ExaminationLocation = request.Service.ExaminationLocation ?? string.Empty,
                RateOfInsurance = "0",
                PatientCode = registerDto.maNb ?? string.Empty,
                ResponseUnitPrice = (decimal)registerDto.tongTienThanhToan,
            };

            var errorType = ErrorTypeEnum.NoError;
            //nếu bệnh viện tự gen qr thì trả về qr code
            if (current.IsGenQR)
            {
                (result, message, errorType, QrPaymentDto payment) = await GenQRPayment(refNo);

                if (result && payment != null && payment.Amount.HasValue)
                {
                    data.QrCode = payment.QrCode;
                    data.ResponseUnitPrice = payment.Amount;
                    data.PaymentRefNo = payment.Number;
                    data.ReceiptRefNo = payment.Number;
                }
            }

            return (result, message, errorType, refNo, data);
        }
    }
}

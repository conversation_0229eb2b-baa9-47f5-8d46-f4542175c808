using System.Text.RegularExpressions;
using HisClientV2.Lib;
using HisClientV2.Lib.Model;
using HisClientV2.Lib.Request;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Integrated.Config;
using MediTrack.Ultils.Extensions;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// Minh Lộ: Hòa Bình, Khánh Hòa
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV2Service : HisServiceParameters, IHisService
    {
        private readonly HisV2Config hisConfig;

        public HisV2Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV2Config>(configSerialize) ??
                        throw new ArgumentNullException("HisV2Config is null");
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> categories) = await GetCategoryModels("dmtuyendk", accessToken);
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            //get healthcare service tier default from hospital metadata
            var tierDefault = await hospitalMetadataRepository!.GetHospitalMetadataByKeyAsync(current.HospitalId, "healthcare_service_tier_default");
            var tierDefaultId = tierDefault?.Value ?? string.Empty;

            List<GetHealthcareServiceTierDto> response = [.. categories.Select(a => new GetHealthcareServiceTierDto
            {
                Id = a.Id ?? string.Empty,
                Name = a.Name ?? string.Empty,
                IsDefault = a.Id == tierDefaultId
            })];

            return (result, message, ErrorTypeEnum.NoError, response);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> categories) = await GetCategoryModels("loaihinhkcb", accessToken);
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            List<GetHealthcareServiceTypeDto> response = [.. categories.Select(a => new GetHealthcareServiceTypeDto
            {
                Id = a.Id ?? string.Empty,
                Name = a.Name ?? string.Empty,
            })];

            return (result, message, ErrorTypeEnum.NoError, response);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> categories) = await GetCategoryModels("doituongkcb", accessToken);
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            List<GetMedicalTreatmentCategoryDto> response = [.. categories.Select(a => new GetMedicalTreatmentCategoryDto
            {
                Id = a.Id ?? string.Empty,
                Name = a.Name ?? string.Empty,
            })];

            return (result, message, ErrorTypeEnum.NoError, response);
        }

        public async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            if (current.IsGenQueueNumberByHis)
            {
                (bool result, string message, string accessToken) = await GetAccessToken();
                if (!result)
                {
                    return (false, message, ErrorTypeEnum.HisError, new AutoDialerQueueAddNewResponse());
                }
                string categoryUrl = $"{hisConfig.Host}/api/GetQueueNumbers";
                var queueRequest = new CreateQueueRequest
                {
                    NhomPhatSo = 0,
                    NhomQuay = request.MaPhong?.ToInt() ?? 0,
                    TenQuay = request.TenPhong ?? string.Empty,
                    DauSo = "0",
                    DinhDangSo = 4,
                    TruDauSo0 = false,
                    SoBatDau = 0,
                    UuTien = request.UuTien ?? 0,
                };
                (result, message) = await httpClientFactory.CreateClient().GetQueueNumbers(queueRequest, categoryUrl, accessToken);
                if (!result)
                {
                    Log.Error($"GetQueueNumbers failed: {message}");
                    return (false, message, ErrorTypeEnum.HisError, new AutoDialerQueueAddNewResponse());
                }

                return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
                {
                    Id = string.Empty,
                    SoThuTu = message.ToInt(),
                    UuTien = request.UuTien == 1,
                    MaPhong = request.MaPhong,
                });
            }
            else
            {
                // Số ưu tiên nối tiếp số thường, bỏ qua Priority
                var currentQueue = await databaseService.DialerQueues.FirstOrDefaultAsync(a =>
                    a.QueueDate.Date == DateTimeHelper.GetCurrentLocalDateTime().Date &&
                    a.HospitalId == current.HospitalId &&
                    (string.IsNullOrEmpty(request.MaPhong) || a.HealthServiceId == request.MaPhong)
                );

                if (currentQueue is not null)
                {
                    currentQueue.QueueNumber += 1;
                    databaseService.DialerQueues.Update(currentQueue);
                }
                else
                {
                    currentQueue = new DialerQueue
                    {
                        Id = Guid.NewGuid().ToString(),
                        HealthServiceId = request.MaPhong,
                        QueueDate = DateTimeHelper.GetCurrentLocalDateTime(),
                        QueueNumber = 1,
                        HospitalId = current.HospitalId,
                    };
                    databaseService.DialerQueues.Add(currentQueue);
                }
                return (true, string.Empty, ErrorTypeEnum.NoError, new AutoDialerQueueAddNewResponse
                {
                    Id = currentQueue.Id,
                    SoThuTu = currentQueue.QueueNumber,
                    UuTien = request.UuTien == 1,
                    NgayDangKy = currentQueue.QueueDate.AddHours(7),
                    MaPhong = currentQueue.HealthServiceId,
                });
            }
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new object()));
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        protected async Task<(bool, string, List<CategoryModel>)> GetCategoryModels(string tenDanhMuc, string accessToken)
        {
            string categoryUrl = $"{hisConfig.Host}/api/getCategory";

            //get from cache
            var cacheKey = $"HisCategory_{current.HospitalId}_{tenDanhMuc}";
            var cacheValue = await cachedService.GetAsync<List<CategoryModel>>(cacheKey);
            if (cacheValue is not null && cacheValue.Count > 0)
            {
                return (true, "Get from cache", cacheValue);
            }

            (bool result, string message, List<CategoryModel> data) = await httpClientFactory.CreateClient().GetCategories(new GetCategoriesRequest()
            {
                TenDanhMuc = tenDanhMuc
            }, categoryUrl, accessToken);

            if (result && data is not null && data.Count > 0)
            {
                await cachedService.SetAsync(cacheKey, data);
            }

            return (result, message, data ?? []);
        }

        protected async Task<(string?, string?, string?)> GetAdministrativeUnitsAsync(
            string accessToken, string? localProvinceId, string? localDistrictId, string? localWardId)
        {
            (bool _, string _, List<CategoryModel> provinces) = await GetCategoryModels("dmtinhthanh", accessToken);

            var isTwoLevelAddress = current.IsTwoLevelAddress == true;

            var provinceId = provinces.Find(a => a.Code == localProvinceId)?.Id;
            if (string.IsNullOrEmpty(provinceId))
            {
                var localProvinces = await provinceRepository!.GetProvincedAsync();

                var provinceNames = provinces
                    .Where(x => !string.IsNullOrEmpty(x.Name))
                    .Select(x => x.Name ?? string.Empty).ToList();

                var localProvince = localProvinces.Find(x => (isTwoLevelAddress ? x.NewId : x.Id) == localProvinceId);
                var localProvinceName = isTwoLevelAddress
                    ? localProvince?.NewName ?? string.Empty
                    : localProvince?.Name ?? string.Empty;

                //find closet string in local province
                var provinceName = provinceNames?.FindMatchedString(localProvinceName);

                if (string.IsNullOrEmpty(provinceName))
                {
                    provinceName = provinceNames?.FindClosestString(localProvinceName);
                }

                provinceId = provinces.Find(x => x.Name == provinceName)?.Id;
            }

            string? districtId = null;

            //Viettel mặc dù địa chỉ đã đổi thành 2 cấp nhưng bắt buộc phải lấy districtId mặc định (tên quận huyện empty, chỉ lấy Id)
            (bool _, string _, List<CategoryModel> ditricts) = await GetCategoryModels("dmquanhuyen", accessToken);

            if (!isTwoLevelAddress)
            {
                districtId = ditricts.Find(a => a.Code == localDistrictId)?.Id;

                if (string.IsNullOrEmpty(districtId))
                {
                    // normalize number in ditricts
                    ditricts.ForEach(x => x.Name = StringHelper.NormalizeNumberInAddress(x.Name ?? string.Empty));

                    var localDistricts = await districtRepository!.GetDistrictByParentIdAsync(localProvinceId);

                    var districtNames = ditricts
                        .Where(x => x.ParentId == provinceId && !string.IsNullOrEmpty(x.Name))
                        .Select(x => x.Name ?? string.Empty).ToList();

                    var localDistrictName = localDistricts.Find(x => x.Id == localDistrictId)?.Name ?? string.Empty;

                    //find closet string in local district
                    var districtName = districtNames?.FindMatchedString(localDistrictName);

                    if (string.IsNullOrEmpty(districtName))
                    {
                        districtName = districtNames?.FindClosestString(localDistrictName);
                    }

                    districtId = ditricts.Find(x => x.ParentId == provinceId && x.Name == districtName)?.Id;
                }
            }
            else // nếu là 2 cấp thì lấy districtId mặc định (tên quận huyện empty, chỉ lấy Id)
            {
                districtId = ditricts.FirstOrDefault(a => a.ParentId == provinceId)?.Id;
            }

            (bool _, string _, List<CategoryModel> wards) = await GetCategoryModels("dmphuongxa", accessToken);
            var foundWards = wards.Where(a => a.Code == localWardId).ToList();

            string? wardId = null;
            int wardCount = foundWards.Count;

            // chỉ có 1 ward thì lấy wardId, nhiều hơn thì phải lọc tên
            if (wardCount == 1)
            {
                wardId = foundWards.FirstOrDefault()?.Id;
            }

            if (string.IsNullOrEmpty(wardId))
            {
                // normalize number in wards
                if (wardCount > 1)
                {
                    foundWards.ForEach(x => x.Name = StringHelper.NormalizeNumberInAddress(x.Name ?? string.Empty));
                }
                else
                {
                    wards.ForEach(x => x.Name = StringHelper.NormalizeNumberInAddress(x.Name ?? string.Empty));
                }

                List<string>? wardNames = [];

                if (wardCount > 1)
                {
                    wardNames = [.. foundWards
                        .Where(x => x.ParentId == districtId && !string.IsNullOrEmpty(x.Name))
                        .Select(x => x.Name ?? string.Empty)];
                }
                else
                {
                    //get all wards in district
                    wardNames = [.. wards
                        .Where(x => x.ParentId == districtId && !string.IsNullOrEmpty(x.Name))
                        .Select(x => x.Name ?? string.Empty)];
                }

                var localWard = await wardRepository!.GetWardByIdAsync(localWardId!, current.IsTwoLevelAddress);

                var localWardName = isTwoLevelAddress
                    ? localWard?.NewName ?? string.Empty
                    : localWard?.Name ?? string.Empty;

                //find closet string in local ward
                var wardName = wardNames?.FindMatchedString(localWardName);

                if (string.IsNullOrEmpty(wardName))
                {
                    wardName = wardNames?.FindClosestString(localWardName);
                }

                wardId = wards.Find(x => x.ParentId == districtId && x.Name == wardName)?.Id;
            }

            return (provinceId, districtId, wardId);
        }

        public virtual async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, string.Empty, new());
            }

            (bool _, string _, List<CategoryModel> categories) = await GetCategoryModels("dmcauhinhtamung", accessToken);

            (string? provinceId, string? districtId, string? wardId)
                = await GetAdministrativeUnitsAsync(accessToken, request.Customer.ProvinceId, request.Customer.DistrictId, request.Customer.WardId);

            var mapNationalities = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "map_nationalities")
                : null;

            if (mapNationalities is null || string.IsNullOrEmpty(mapNationalities.Value))
            {
                return (false, "Không tìm thấy dữ liệu liên kết Dân tộc", ErrorTypeEnum.MediPayError, string.Empty, new());
            }

            var nationalities = JsonConvert.DeserializeObject<Dictionary<string, string>>(mapNationalities?.Value ?? string.Empty);
            if (nationalities is null || nationalities.Count == 0)
            {
                return (false, "Dữ liệu liên kết Dân tộc không hợp lệ", ErrorTypeEnum.MediPayError, string.Empty, new());
            }

            var nationalityId = nationalities.ContainsKey(request.Customer?.NationalityId ?? string.Empty)
                ? nationalities[request.Customer?.NationalityId ?? string.Empty] : string.Empty;

            var defaultNation = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_nation")
                : null;

            string getClinicUrl = $"{hisConfig.Host}/api/KioskRegister";
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "malydotc")?.Name, out int maLyDoTC) == false)
                maLyDoTC = 3;
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "mahinhthuctt")?.Name, out int maHinhThucTT) == false)
                maHinhThucTT = 2;
            if (int.TryParse(categories.FirstOrDefault(a => a.Code == "maquyentc")?.Name, out int maQuyenTC) == false)
                maQuyenTC = 1;

            (bool _, string _, List<CategoryModel> dmGioiTinh) = await GetCategoryModels("dmgioitinh", accessToken);
            string? gioiTinh = dmGioiTinh.FirstOrDefault(a => a.Name == request.Customer?.Sex)?.Id;

            var relationShipId = request.CustomerRelationshipId ?? string.Empty;
            var mapRelationShips = hospitalMetadataRepository != null
                ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "id_quan_he_nt_map")
                : null;

            if (mapRelationShips is not null && !string.IsNullOrEmpty(mapRelationShips.Value))
            {
                var relationShips = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(mapRelationShips.Value);
                if (relationShips is not null && relationShips.Count > 0)
                {
                    relationShipId = relationShips.FirstOrDefault(x => x.OriginId == request.CustomerRelationshipId)?.Id ?? relationShipId;
                }
            }

            bool isUseInsuranceNoToValidate = false;
            // Check nếu là người thân và có số cccd không đúng format
            if (!string.IsNullOrEmpty(request.CustomerRelationshipId)
                && !string.IsNullOrEmpty(request.Customer!.IdentityNo)
                && !Regex.IsMatch(request.Customer!.IdentityNo ?? string.Empty, @"^\d{12}$"))
            {
                isUseInsuranceNoToValidate = true;
            }

            var req = new CreateRegisterFormRequest()
            {
                MaBenhNhan = request.CustomerHospital.PatientCode ?? string.Empty,
                NgayDangKy = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm"),
                HoVaTen = request.Customer!.GetFullName().ToUpper(),
                NgayThangNamSinh = request.Customer!.DateOfBirth.HasValue
                    ? request.Customer.DateOfBirth.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                GioiTinh = !string.IsNullOrEmpty(gioiTinh) ? gioiTinh : (request.Customer.Sex == "Nam" ? "1" : "2"),
                DanToc = nationalityId ?? string.Empty, // Dân tộc Việt Nam theo Id HIS
                QuocTich = defaultNation?.Value ?? string.Empty, // Quốc tịch Việt Nam theo Id HIS
                SoNha = request.Customer.Street ?? string.Empty,
                DienThoai = request.Customer.Phone ?? string.Empty,
                SoCMND = isUseInsuranceNoToValidate ? string.Empty : request.Customer.IdentityNo ?? string.Empty,
                NgayCapCMND = request.Customer.IdentityIssueDate.HasValue
                    ? request.Customer.IdentityIssueDate.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                NoiCapCMND = request.Customer.IdentityIssuePlace ?? string.Empty,
                NoiLamViec = request.Customer.WorkPlace ?? string.Empty,
                MaTinhThanh = provinceId ?? string.Empty,
                MaQuanHuyen = districtId ?? string.Empty,
                MaPhuongXa = wardId ?? string.Empty,
                MaDoiTuong = request.Service.ExameTypeId ?? string.Empty,
                MaPhong = request.Service.ClinicId ?? string.Empty,
                MaNoiCap = request.IsInsurance ? (request.Insurance?.InsuranceNo.SubStringExt(2, 3) ?? string.Empty) : string.Empty,
                MaThe = request.IsInsurance ? (request.Insurance?.InsuranceNo ?? string.Empty) : string.Empty,
                NgayBatDau = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FromDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                NgayKetThuc = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.ExpiredDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                Ngay5Nam = request.IsInsurance ? DateTimeHelper.FormatDate(request.Insurance?.FullFiveYearDate ?? string.Empty, "dd/MM/yyyy", "yyyy-MM-dd") : string.Empty,
                MaNoiDangKy = request.IsInsurance ? (request.Insurance?.RegisterPlaceID ?? string.Empty) : string.Empty,
                DiaChiThe = request.IsInsurance ? (request.Insurance?.RegisterAddress ?? string.Empty) : string.Empty,
                MaTuyenDangKy = !string.IsNullOrEmpty(request.HealthcareServiceTierId) ? request.HealthcareServiceTierId : "1",
                LyDoKham = !string.IsNullOrEmpty(request.ReasonForVisit) ? request.ReasonForVisit : "Khám bệnh",
                MaNgheNghiep = request.CustomerHospital.CareerId ?? string.Empty,
                DieuTriNgoaiTru = "1",
                MaLyDoTC = maLyDoTC,
                MaHinhThucTT = maHinhThucTT,
                MaDoiTuongKCB = request.MedicalTreatmentCategoryId ?? string.Empty,
                SoTienTU = (int)(request.Service.UnitPrice ?? 0),
                MaQuyenTC = maQuyenTC,
                MaSinhSong = request.IsInsurance ? (request.Insurance?.AreaCode?.SubStringEndExt(1) ?? string.Empty) : string.Empty,
                MaHinhThuc = request.HealthcareServiceTypeId ?? string.Empty,
                MaGoi = request.Service.Id,
                NguoiNha = (!string.IsNullOrEmpty(request.CustomerRelationshipName) ? (request.CustomerRelationshipName + ": ") : string.Empty)
                    + request.CustomerRelationship.GetFullName(),
                NgaySinhNguoiNha = request.CustomerRelationship.DateOfBirth.HasValue
                    ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                    : string.Empty,
                DienThoaiNguoiNha = request.CustomerRelationship.Phone ?? string.Empty,
                DiaChiBaoTin = request.CustomerRelationship.Address?.Trim() ?? string.Empty,
                QuanHe = relationShipId,
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel data) = await httpClientFactory.CreateClient().CreateRegisterForm(req, getClinicUrl, accessToken);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, data);

            string regNo = string.Empty;
            RegisterFormResponseDto res = new();

            if (result)
            {
                regNo = !string.IsNullOrEmpty(data.SoPhieu) ? data.SoPhieu : data.MaBenhNhan;
                res = new RegisterFormResponseDto
                {
                    PatientCode = !string.IsNullOrEmpty(data?.MaBenhNhan) ? data.MaBenhNhan : (request.CustomerHospital.PatientCode ?? string.Empty),
                    Clinic = data!.TenPhong,
                    QrCode = data.QRCode,
                    QueueNumber = data.ThuTuKham,
                    QueueNumberPriority = "0",
                    RegisterNumber = regNo,
                    ReceiptRefNo = regNo,
                    PaymentRefNo = regNo,
                    ExaminationLocation = data.TenGopHDKB,
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, regNo, res);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            string getClinicUrl = $"{hisConfig.Host}/api/GetOutpatientClinic";
            (result, message, List<ClinicModel> data) = await httpClientFactory.CreateClient().GetClinic(new GetClinicRequest()
            {
            }, getClinicUrl, accessToken);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            List<Clinic> list = [];
            if (result && data is not null && data.Count > 0)
            {
                //get hidden clinic from db
                var hiddenClinicMeta = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "hidden_clinics")
                    : null;

                if (hiddenClinicMeta is not null && !string.IsNullOrEmpty(hiddenClinicMeta.Value))
                {
                    var hiddenClinics = hiddenClinicMeta.Value.Split(";");
                    data = [.. data.Where(x => !hiddenClinics.Contains(x.MaPhong))];
                }

                list = [.. data.Select(x => new Clinic()
                {
                    Id = x.MaPhong,
                    Code = x.MaPhong,
                    Name = x.TenPhong,
                    WaitingPatientCount = x.ChoKham,
                    ProcessingNumber = x.DangKy,
                })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> data) = await GetCategoryModels("dmgoi", accessToken);
            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            List<HealthService> res = [];
            if (result)
            {
                res = [.. data.Select(a => new HealthService
                {
                    Id = a.Id ?? string.Empty,
                    Name = a.Name ?? string.Empty,
                    UnitPrice = a.DonGiaDV,
                    UnitPriceDisplay = (a.DonGiaDV?.ToString("N0") ?? "0") + " đ",
                    InsurancePrice = a.DonGiaBHYT,
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    InsurancePriceDisplay = (a.DonGiaBHYT?.ToString("N0") ?? "0") + " đ",
                    ExameTypeId = request.ExameTypeId ?? string.Empty,
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                })];
            }

            // Do YHCT Khánh Hòa chỉ làm luồng tạm ứng, nên id gói tạm thời cố định, không cần lấy từ HIS
            if (res.Count == 0)
            {
                res =
                [
                    new() {
                        Name = HealthServiceConstant.HealthServiceDefault,
                        Id = HealthServiceConstant.HealthServiceIdDefault,
                        ClinicId = request.ClinicId,
                        SubClinicId = request.SubClinicId ?? string.Empty,
                        ClinicCode = request.ClinicCode ?? string.Empty,
                        ExameTypeId = request.ExameTypeId ?? string.Empty,
                        InsurancePrice = 0,
                        IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                    }
                ];

                result = true;
                message = "Success";
            }

            return (result, message, ErrorTypeEnum.NoError, res);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientCodeResponse = string.Empty;
            string patientCareerId = string.Empty;

            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, new HisCustomerDto());
            }

            string getClinicUrl = $"{hisConfig.Host}/api/CheckPatientStatus";

            var req = new CreatePatientRequest()
            {
                SoCMND = customer.IdentityNo ?? string.Empty,
                HoVaTen = customer.GetFullName(),
                NgayThangNamSinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                GioiTinh = customer.Sex ?? string.Empty,
                KiemTra = "0",
                TatCa = "0",
            };

            Log.Information("{LogPrefix} GetCustomerHis Req --- {@Request}", logPrefix, req);

            (result, message, PatientModel data) = await
                httpClientFactory.CreateClient().CheckPatient(req, getClinicUrl, accessToken);

            Log.Information("{LogPrefix} GetCustomerHis Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            if (result && !string.IsNullOrEmpty(data.MaBenhNhan))
            {
                patientCodeResponse = data.MaBenhNhan;
                patientCareerId = data.MaNgheNghiep;
            }

            return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto
            {
                PatientCode = patientCodeResponse,
                PatientId = patientCodeResponse,
                CareerId = patientCareerId,
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, new());
            }

            string getClinicUrl = $"{hisConfig.Host}/api/CheckPaymentStatus";
            var req = new CheckPaymentRequest()
            {
                SoPhieu = refNo
            };

            Log.Information("{LogPrefix} CheckPaymentStatus Req --- {@Request}", logPrefix, req);

            (result, message, PaymentResultModel data) = await
                httpClientFactory.CreateClient().CheckPaymentStatus(req, getClinicUrl, accessToken);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            PaymentStatusDto response = new();
            if (result)
            {
                response = new PaymentStatusDto
                {
                    Number = data.SoPhieu,
                    PaymentStatus = data.DaTTKhongTienMat ? PaymentConstant.Success : PaymentConstant.WaitForPayment,
                    RefNo = data.SoPhieu,
                };
            }

            return (result, message, ErrorTypeEnum.NoError, response);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();

            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> data) = await GetCategoryModels("dmdoituong", accessToken);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            List<ExameType> res = [];
            if (result && data is not null && data.Count > 0)
            {
                res = [.. data.Select(x => new ExameType
                {
                    Id = x.Id ?? string.Empty,
                    Name = x.Name ?? string.Empty,
                    IsInsurance = x.Name?.ToUpper() == "BHYT"
                })];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, res);
        }

        public async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, new());
            }

            string categoryUrl = $"{hisConfig.Host}/api/KQNhanLichSuKCB2024";

            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            var req = new GetHealthInsuranceRequest()
            {
                MaThe = checkCode ?? string.Empty,
                HoTen = customer.GetFullName(),
                NgaySinh = customer.DateOfBirth.GetValueOrDefault().ToString("dd/MM/yyyy"),
            };

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Req --- {@Request}", logPrefix, req);

            (result, message, GetHealthInsuranceResponse data) = await httpClientFactory.CreateClient().GetHealthInsurance(req, categoryUrl, accessToken);

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Res --- Result: {Result} - Message: {Message} - {@Data}", logPrefix, result, message, data);

            CustomerHealthInsurance res = new();
            if (result)
            {
                res.HealthInsuranceId = data.MaThe;
                res.CustomerId = customer.Id;
                res.IdentityNo = customer.IdentityNo ?? string.Empty;
                res.InsuranceNo = data.MaThe ?? string.Empty;
                res.InsuranceGroupID = string.Empty;
                res.FromDate = data.GtTheTu;
                res.ExpiredDate = data.GtTheDen;
                res.RegisterPlaceID = !string.IsNullOrEmpty(data.MaDKBDMoi) ? data.MaDKBDMoi : data.MaDKBD ?? string.Empty;
                res.RegisterPlaceName = !string.IsNullOrEmpty(data.TenDKBDMoi) ? data.TenDKBDMoi : string.Empty;
                res.RoutingType = "-1";
                res.IsCorrectRouting = data.GtTheDen?.ConvertStringToDateTime("dd/MM/yyyy")?.Date >= DateTimeHelper.GetCurrentLocalDateTime().Date;
                res.ReferralLevel = res.IsCorrectRouting ? "1" : "2";
                res.Description = data.GhiChu;
                res.FullFiveYearDate = data.NgayDu5Nam;
                res.RegisterAddress = data.DiaChi;
                res.AreaCode = data.MaKV;
                res.MedicalHistories = data.DsLichSuKCB2018?.Select(x => new MedicalHistoryModel()
                {
                    RecordId = x.MaHoSo ?? string.Empty,
                    HealthcareFacilityId = x.MaCSKCB ?? string.Empty,
                    AdmissionDate = x.NgayVao ?? string.Empty,
                    DischargeDate = x.NgayRa ?? string.Empty,
                    DiseaseName = x.TenBenh ?? string.Empty,
                    Condition = x.TinhTrang ?? string.Empty,
                    TreatmentResult = x.KqDieuTri ?? string.Empty,
                    AdmissionReason = x.LyDoVV ?? string.Empty
                }).ToList() ?? [];
                res.CheckInsuranceHistories = data.DsLichSuKT2018?.Select(x => new CheckInsuranceHistoryModel()
                {
                    CheckUser = x.UserKT ?? string.Empty,
                    CheckTime = x.ThoiGianKT ?? string.Empty,
                    Description = x.ThongBao ?? string.Empty,
                    ErrorCode = x.MaLoi ?? string.Empty
                }).ToList() ?? [];
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, res);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> data) = await GetCategoryModels("dmnghenghiep", accessToken);

            List<GetSocialCareerDto> res = [];
            if (result)
            {
                string defaultSocialCareer = hospitalMetadataRepository != null
                    ? (await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "default_social_career"))?.Value ?? string.Empty
                    : string.Empty;

                if (data is not null && data.Count > 0)
                {
                    res = [.. data!.Select(x => new GetSocialCareerDto()
                    {
                        Id = x.Id ?? string.Empty,
                        Name = x.Name ?? string.Empty,
                        IsDefault = x.Id == defaultSocialCareer // 99 hoà bình, 2021 khánh hòa
                    })];
                }
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, res);
        }

        public async Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            if (current.IsInsuranceAdvancePayment || current.IsAdvancePayment)
            {
                //get advance money from db
                var advancedMoneyMeta = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.HospitalId, "advance_money")
                    : null;

                if (advancedMoneyMeta != null && !string.IsNullOrEmpty(advancedMoneyMeta.Value))
                {
                    return (true, string.Empty, ErrorTypeEnum.NoError, advancedMoneyMeta.Value.Split(";") ?? []);
                }
            }

            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> data) = await GetCategoryModels("dmcauhinhtamung", accessToken);

            string[]? res = null;
            if (result && data is not null && data.Count > 0)
            {
                var advanceMoneyStr = data.FirstOrDefault(x => x.Code == "sotientu")?.Name ?? string.Empty;
                res = string.IsNullOrEmpty(advanceMoneyStr) ? null : advanceMoneyStr.Split(";");
            }

            return (result, message, ErrorTypeEnum.NoError, res ?? []);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()));
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }
        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        protected async Task<(bool, string, string)> GetAccessToken()
        {
            string authUrl = $"{hisConfig.Host}/api/GetToken";

            (bool result, string message, string accessToken) = await
                httpClientFactory.CreateClient().GetAccessToken(new GetTokenRequest()
                {
                    UserName = hisConfig.UserName,
                    Password = hisConfig.PassWord,
                }, authUrl);

            return (result, message, accessToken);
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            (bool result, string message, string accessToken) = await GetAccessToken();
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            (result, message, List<CategoryModel> categories) = await GetCategoryModels("dmdichvu", accessToken);
            if (!result)
            {
                return (false, message, ErrorTypeEnum.HisError, []);
            }

            List<HealthServiceSearchDto> res = [];
            if (categories is not null && categories.Count > 0)
            {
                List<(string id, bool isBHYT)> mapExameTypeInsurance = [];
                //check exist field donGia
                if (categories.Any(x => x.DonGia is not null))
                {
                    (bool exameTypeResult, _, _, var exameTypes) = await GetExameTypes();

                    if (exameTypeResult)
                    {
                        mapExameTypeInsurance = [.. exameTypes.Select(x => (x.Id, x.IsInsurance))];
                    }
                }

                res = [.. categories.Select(x =>
                {
                    decimal insurancePrice = 0;
                    decimal unitPrice = 0;

                    if (mapExameTypeInsurance.Count > 0)
                    {
                        var bhytId = mapExameTypeInsurance.FirstOrDefault(z => z.isBHYT).id;
                        var nonBhytId = mapExameTypeInsurance.FirstOrDefault(z => !z.isBHYT).id;

                        insurancePrice = x.DonGia?.FirstOrDefault(y => y.MaDoiTuong?.ToString() == bhytId)?.GiaDichVu ?? 0;
                        unitPrice = x.DonGia?.FirstOrDefault(y => y.MaDoiTuong?.ToString() == nonBhytId)?.GiaDichVu ?? 0;
                    }
                    else
                    {
                        insurancePrice = x.DonGiaBHYT ?? 0;
                        unitPrice = x.DonGiaDV ?? 0;
                    }

                    return new HealthServiceSearchDto
                    {
                        ClinicId = x.ParentId,
                        ClinicName = x.ParentName,
                        ServiceId = x.Id ?? string.Empty,
                        InsuranceServiceId = x.Id ?? string.Empty,
                        ServiceName = x.Name ?? string.Empty,
                        UnitPrice = unitPrice,
                        InsurancePrice = insurancePrice
                    };
                })];

                result = true;
                message = "Success";
            }

            return (result, message, ErrorTypeEnum.NoError, res);
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

﻿using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Domain;
using MediTrack.Integrated.Config;
using HisClientV5.Lib;
using MediTrack.Domain.Helpers;
using MediTrack.Ultils.Helpers;
using HisClientV5.Lib.Model;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using Microsoft.EntityFrameworkCore;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using Newtonsoft.Json;
using Serilog;
using HisClientV5.Lib.Request;
using MediTrack.Domain.Constants;
using MediTrack.Application.Helpers;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Domain.Enums;
using Amazon.Runtime;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Helpers;

namespace MediTrack.Integrated.Services
{
    /// <summary>
    /// EHIS: TTYT Điện Biên...
    /// </summary>
    /// <param name="hisConfig"></param>
    public class HisV5Service : HisServiceParameters, IHisService
    {
        private readonly HisV5Config hisConfig;

        public HisV5Service(HisServiceConfiguration config) : base(config)
        {
            var configSerialize = JsonConvert.SerializeObject(config.CurrentHospital.HisConfig);
            hisConfig = JsonConvert.DeserializeObject<HisV5Config>(configSerialize) ??
                            throw new ArgumentNullException("HisV5Config is null");
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers()
        {
            //get healthcare service tier from hospital metadata
            var tiers = await hospitalMetadataRepository!.GetHospitalMetadataByKeyAsync(current.HospitalId, "healthcare_service_tiers");

            if (tiers is null || string.IsNullOrEmpty(tiers.Value))
            {
                return (false, "Không tìm thấy cấu hình thông tin tuyến", ErrorTypeEnum.MediPayError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, JsonConvert.DeserializeObject<List<GetHealthcareServiceTierDto>>(tiers.Value) ?? []);
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetHealthcareServiceTypeDto>()));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories()
        {
            var config = await databaseService.HospitalMetaDatas
                    .FirstOrDefaultAsync(x => x.HospitalId == current.HospitalId && x.GroupType == "danh_sach_doi_tuong_kcb");

            if (config is null || string.IsNullOrEmpty(config.Value))
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            //parse string to list of GetMedicalTreatmentCategoryDto
            var list = JsonConvert.DeserializeObject<List<GetMedicalTreatmentCategoryDto>>(config.Value);
            if (list is null)
            {
                Log.Information("{LogPrefix} GetMedicalTreatmentCategories --- Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", logPrefix);
                return (false, "Không tìm thấy cấu hình danh sách đối tượng khám chữa bệnh", ErrorTypeEnum.MediPayError, []);
            }

            return (true, string.Empty, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request)
        {
            AutoDialerQueueAddNewResponse response = new();

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, response);
            }

            string url = $"{hisConfig.Host}/api/hdbank/capstt";
            (bool result, string message, HisClientV5.Lib.Response.AutoDialerQueueAddNewResponse resData) =
                await httpClientFactory.CreateClient().AutoDialerQueueAddNew(url, accessToken);

            Log.Information("{LogPrefix} AutoDialerQueueAddNew Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                response.Id = resData.stt.ToString() ?? string.Empty;
                response.SoThuTu = resData.stt ?? 0;
                response.UuTien = request.UuTien == 1;
                response.NgayDangKy = DateTimeHelper.GetCurrentLocalDateTime();
            }

            return (result, message, ErrorTypeEnum.NoError, response);
        }

        public Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new object()));
        }

        public async Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo)
        {
            PaymentStatusDto status = new();

            bool result;
            string? message;
            //1. Auth
            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, new PaymentStatusDto());
            }

            string url = $"{hisConfig.Host}/api/hdbank/trang-thai-phieu";

            var req = new CheckPaymentRequest()
            {
                so_phieu = refNo
            };

            Log.Information("{LogPrefix} CheckPaymentStatus Req --- {@Request}", logPrefix, req);

            (result, message, PaymentModel resData) =
                await httpClientFactory.CreateClient().CheckPayment(req, url, accessToken);

            Log.Information("{LogPrefix} CheckPaymentStatus Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                status.Number = resData.so_phieu;
                status.RefNo = resData.so_phieu;
                status.PaymentStatus = resData.da_thanh_toan ? PaymentConstant.Success : PaymentConstant.WaitForPayment;
            }

            return (result, message, ErrorTypeEnum.NoError, status);
        }

        public Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request)
        {
            throw new NotImplementedException();
        }

        public Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request)
        {
            string refNo = string.Empty;
            RegisterFormResponseDto data = new();

            //1. Auth
            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, refNo, data);
            }

            string getPatientUrl = $"{hisConfig.Host}/api/hdbank/benhnhan";
            string message;
            bool result;
            (result, message, PatientModel patient) = await httpClientFactory.CreateClient().CreatePatient(new CreatePatientRequest()
            {
                loai_gttt = "CCCD",
                so_gttt = request.Customer.IdentityNo ?? string.Empty
            }, getPatientUrl, accessToken);

            //2. Call API 
            string createUrl = $"{hisConfig.Host}/api/KhamSucKhoe/dangky-kcb";

            string address = request.Customer.Address ?? string.Empty;
            string provinceId = request.Customer.ProvinceId ?? string.Empty;
            string districtId = request.Customer.DistrictId ?? string.Empty;
            string wardId = request.Customer.WardId ?? string.Empty;

            // Nếu khám bảo hiểm và có địa chỉ đăng ký bảo hiểm, thì sử dụng địa chỉ đăng ký bảo hiểm
            if (request.IsInsurance
                && !string.IsNullOrEmpty(request.Insurance?.RegisterAddress)
                && provinceRepository is not null)
            {
                //0. Prepare data
                var provinces = await provinceRepository!.GetProvincedAsync(cancellationToken: CancellationToken.None);
                var districts = await districtRepository!.GetDistrictByParentIdAsync(null, cancellationToken: CancellationToken.None);
                var wards = await wardRepository!.GetWardByParentIdAsync(null, false, cancellationToken: CancellationToken.None);

                (ProvinceDto? provinceDto, DistrictDto? districtDto, WardDto? wardDto, _, bool isContainWard) =
                        ApplicationCustomerHelper.GetLocationDto(request.Insurance?.RegisterAddress, provinces, districts, wards);

                if (provinceDto != null
                    && districtDto != null
                    && (wardDto != null || !isContainWard))
                {
                    address = request.Insurance?.RegisterAddress ?? string.Empty;
                    provinceId = current.IsTwoLevelAddress
                        ? provinceDto.NewId
                        : provinceDto.Id;
                    districtId = current.IsTwoLevelAddress
                        ? string.Empty
                        : districtDto.Id;
                    wardId = current.IsTwoLevelAddress
                        ? wardDto?.NewId ?? string.Empty
                        : wardDto?.Id ?? string.Empty;
                }
            }

            //gán lại khoa nếu là khám bảo hiểm và có lý do khám
            var clinicId = request.Service.ClinicId ?? string.Empty;
            if (request.IsInsurance && HisClinicConstant.ClinicType.ContainsKey(request.HealthcareServiceTierId ?? string.Empty))
            {
                clinicId = HisClinicConstant.ClinicType[request.HealthcareServiceTierId ?? string.Empty];
            }

            var req = new CreateRegisterFormRequest()
            {
                thong_tin_benh_nhan = new PatientModel()
                {
                    id_bn = patient.id_bn,
                    ma_bn = patient.ma_bn,
                    ho_ten = request.Customer.GetFullName(),
                    ho_bn = request.Customer.FirstName ?? string.Empty,
                    ten_bn = request.Customer.LastName ?? string.Empty,
                    dien_thoai = request.Customer.Phone ?? string.Empty,
                    ngay_sinh = request.Customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd"),
                    dia_chi = address,
                    matinh_cutru = provinceId,
                    mahuyen_cu_tru = districtId,
                    maxa_cu_tru = wardId,
                    ma_dantoc = request.Customer.NationalityId ?? string.Empty,
                    ma_quoctich = request.Customer.Nation ?? string.Empty,
                    gioi_tinh = request.Customer.Sex == "Nam" ? 1 : 2,
                    nhom_mau = string.Empty,
                    so_gttt = request.Customer.IdentityNo ?? string.Empty,
                    ngay_cap_gttt = request.Customer.IdentityIssueDate.HasValue ? request.Customer.IdentityIssueDate.GetValueOrDefault().ToString("yyyy-MM-dd") : string.Empty,
                    noi_cap_gttt = request.Customer.IdentityIssuePlace ?? string.Empty,
                    ma_the_bhyt = request.Insurance?.InsuranceNo ?? string.Empty,
                    ma_dkbd = request.Insurance?.RegisterPlaceID ?? string.Empty,
                    gt_the_tu = request.Insurance?.FromDate ?? string.Empty,
                    gt_the_den = request.Insurance?.ExpiredDate ?? string.Empty,
                    ma_doituong_kcb = request.MedicalTreatmentCategoryId ?? string.Empty,
                    ngay_vao = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    ma_nghe_nghiep = request.Customer.CareerId ?? string.Empty,
                    ma_nghe_nghiep_his = request.CustomerHospital.CareerId ?? string.Empty,
                    van_hoa = request.Customer.EducationLevel ?? string.Empty,
                    noi_lam_viec = request.Customer.WorkPlace ?? string.Empty,
                    dia_chi_lam_viec = request.Customer.WorkAddress ?? string.Empty,
                    anh_bn_cccd = request.Customer.Image ?? string.Empty,
                    ly_do_vv = request.HealthcareServiceTierId ?? string.Empty,
                    ly_do_den_kham = request.ReasonForVisit ?? string.Empty,
                    quan_he_nt = request.CustomerRelationshipName ?? string.Empty,
                    ho_ten_nt = request.CustomerRelationship.GetFullName(),
                    ngay_sinh_nt = request.CustomerRelationship.DateOfBirth.HasValue
                        ? request.CustomerRelationship.DateOfBirth.Value.ToString("yyyy-MM-dd")
                        : string.Empty,
                    dia_chi_nt = string.IsNullOrWhiteSpace(request.CustomerRelationship.Address) ? "Cùng Địa Chỉ" : request.CustomerRelationship.Address.Trim(),
                    dien_thoai_nt = request.CustomerRelationship.Phone ?? string.Empty,
                    ma_dinh_danh_nt = request.CustomerRelationship.IdentityNo ?? string.Empty,
                    dieu_tri_arv = request.IsOnARVTreatment
                },
                thong_tin_dich_vu = new RegisterFromServiceModel()
                {
                    id_khoa = request.Service.Id ?? string.Empty,
                    //Phòng khám của EHIS chính là khoa của MediPay
                    id_phong_kham = clinicId,
                    ten_phong_kham = string.Empty,
                    don_gia_phong_kham = request.IsInsurance ? (request.Service.InsurancePrice ?? 0) : (request.Service.UnitPrice ?? 0),
                    id_khoa_ct = request.Service.SubClinicId ?? string.Empty,
                },
                id_loai_kham = request.Service.ExameTypeId ?? string.Empty,
                so_giay_chuyen_tuyen = request.TransferReferralDocumentNumber ?? string.Empty,
                ma_benh_chuyen_tuyen = request.TransferReferralDiseaseCode ?? string.Empty,
                don_vi_chuyen_tuyen = request.TransferReferralUnit ?? string.Empty
            };

            Log.Information("{LogPrefix} CreateRegisterForm Req --- {@Request}", logPrefix, req);

            (result, message, RegisterFormModel registerDto) = await httpClientFactory.CreateClient().CreateRegisterForm(req, createUrl, accessToken);

            Log.Information("{LogPrefix} CreateRegisterForm Res --- Result: {Result} - Message: {Message} - {@RegisterDto}", logPrefix, result, message, registerDto);

            if (result)
            {
                refNo = registerDto.thong_tin_tiep_nhan?.id_lk != null
                    ? registerDto.thong_tin_tiep_nhan?.id_lk ?? string.Empty
                    : string.Empty;
                data = new RegisterFormResponseDto
                {
                    Clinic = request.Service.ClinicId ?? string.Empty,
                    QrCode = registerDto.thong_tin_thanh_toan?.qr_code ?? string.Empty,
                    QueueNumber = registerDto.thong_tin_tiep_nhan?.stt_lk.ToString() ?? "0",
                    QueueNumberPriority = "0",
                    RegisterNumber = refNo,
                    ReceiptRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    PaymentRefNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    RefDocNo = registerDto.thong_tin_thanh_toan?.so_phieu ?? string.Empty,
                    ExaminationLocation = string.Empty,
                    RateOfInsurance = "0",
                    PatientCode = registerDto.thong_tin_benh_nhan?.ma_bn ?? string.Empty
                };
            }

            return (result, message, result ? ErrorTypeEnum.NoError : ErrorTypeEnum.HisError, refNo, data);
        }

        public Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<AllCurrentNumberResponse> { }));
        }

        public async Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null)
        {
            bool result = false;
            string message = string.Empty;
            List<Clinic> list = [];

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }

            string url = $"{hisConfig.Host}/api/hdbank/khoa?id_loai_kham={exameTypeId}";
            (result, message, List<ClinicModel> resData) = await httpClientFactory.CreateClient().GetClinic(url, accessToken);

            Log.Information("{LogPrefix} GetClinics Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new Clinic()
                {
                    Id = x.ma_khoa ?? string.Empty,
                    Code = x.ma_khoa ?? string.Empty,
                    Name = x.ten_khoa ?? string.Empty,
                    ExameTypeID = exameTypeId ?? string.Empty,
                    Children = x.childs?.Select(y => new Clinic()
                    {
                        Id = y.id_child ?? string.Empty,
                        Name = y.name_child ?? string.Empty,
                        ExameTypeID = x.ma_khoa ?? string.Empty,
                        Children = []
                    }).ToList() ?? []
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false)
        {
            CustomerHealthInsurance data = new();

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, data);
            }

            string url = $"{hisConfig.Host}/api/hdbank/bhyt";
            bool result;
            string message;
            var checkCode = isCheckByInsuranceNo ? customer.HealthInsuranceNo : customer.IdentityNo;
            (result, message, HealthInsuranceModel card) =
                await httpClientFactory.CreateClient().CheckHealthInsurance(new CheckHealthInsuranceRequest()
                {
                    SO_GTTT = checkCode ?? string.Empty,
                    LOAI_GTTT = "CCCD",
                    HO_TEN = customer.GetFullName().ToUpper(),
                    NGAY_SINH = customer.DateOfBirth.HasValue ?
                        customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd") : string.Empty,
                    GIOI_TINH = customer.Sex == "Nam" ? "1" : "2"
                }, url, accessToken);

            Log.Information("{LogPrefix} GetCustomerHealthInsurance Res --- Result: {Result} - Message: {Message} - {@Card}", logPrefix, result, message, card);

            if (result)
            {
                data.InsuranceGroupID = string.Empty;
                data.HealthInsuranceId = card.ma_the_bhyt ?? string.Empty;
                data.IdentityNo = customer.IdentityNo ?? string.Empty;
                data.InsuranceNo = card.ma_the_bhyt ?? string.Empty;
                data.FromDate = DateTimeHelper.FormatDate(card.gt_the_tu, "yyyy-MM-dd", "dd/MM/yyyy");
                data.ExpiredDate = DateTimeHelper.FormatDate(card.gt_the_den, "yyyy-MM-dd", "dd/MM/yyyy");
                data.RegisterPlaceID = card.ma_dkbd ?? string.Empty;
                data.RoutingType = card.phan_tuyen.ToString() ?? "-1";
                data.ReferralLevel = (card.phan_tuyen == 0 || card.phan_tuyen == 2) ? "1" : "2";
                data.IsCorrectRouting = card.tiep_nhan_bhyt == 1;
                data.CustomerId = customer.Id;
                data.Description = card.ten_kq ?? string.Empty;
                data.RegisterAddress = card.dia_chi ?? string.Empty;
                data.MedicalHistories = card.dsLichSuKCB?.Select(x => new MedicalHistoryModel()
                {
                    RecordId = x.maHoSo ?? string.Empty,
                    HealthcareFacilityId = x.maCSKCB ?? string.Empty,
                    AdmissionDate = x.ngayVao ?? string.Empty,
                    DischargeDate = x.ngayRa ?? string.Empty,
                    DiseaseName = x.tenBenh ?? string.Empty,
                    Condition = x.tinhTrang ?? string.Empty,
                    TreatmentResult = x.kqDieuTri ?? string.Empty,
                    AdmissionReason = x.lyDoVV ?? string.Empty
                }).ToList() ?? [];
                data.CheckInsuranceHistories = card.dsLichSuKT2018?.Select(x => new CheckInsuranceHistoryModel()
                {
                    CheckUser = x.userKT ?? string.Empty,
                    CheckTime = x.thoiGianKT ?? string.Empty,
                    Description = x.thongBao ?? string.Empty,
                    ErrorCode = x.maLoi ?? string.Empty
                }).ToList() ?? [];
            }

            return (result, message, ErrorTypeEnum.NoError, data);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes()
        {
            bool result = false;
            string message = string.Empty;
            List<ExameType> list = [];


            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }

            string url = $"{hisConfig.Host}/api/hdbank/loai-kham";
            (result, message, List<ExameTypeModel> resData) = await httpClientFactory.CreateClient().GetExameTypes(url, accessToken);

            Log.Information("{LogPrefix} GetExameTypes Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                list = [.. resData!.Select(x => new ExameType()
                {
                    Id = x.ExameTypeId ?? string.Empty,
                    Name = x.ExameTypeName ?? string.Empty,
                    IsInsurance = (x.ExameTypeName ?? string.Empty).Contains("BHYT"),
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request)
        {
            bool result = false;
            string message = string.Empty;
            List<HealthService> list = [];

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, list);
            }

            string url = $"{hisConfig.Host}/api/hdbank/dichvu?ID_LOAI_KHAM={request.ExameTypeId}&ID_KHOA_CT={request.SubClinicId}";
            (result, message, List<HealthServiceModel> resData) = await httpClientFactory.CreateClient().GetHealthService(url, accessToken);

            Log.Information("{LogPrefix} GetHealthServices Res --- Result: {Result} - Message: {Message} - {@ResData}", logPrefix, result, message, resData);

            if (result)
            {
                var hospitalMetaDatas = hospitalMetadataRepository != null
                    ? await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(current.HospitalId)
                    : null;
                var insurancePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "INSURANCE");
                var freePriceDisplay = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "price_display_default" && x.Code == "FREE");

                list = [.. resData!.Select(x => new HealthService()
                {
                    Id = x.id_dich_vu ?? string.Empty,
                    Name = x.ten_dich_vu ?? string.Empty,
                    UnitPrice = x.don_gia_dich_vu,
                    UnitPriceDisplay = (x.don_gia_dich_vu == 0 && freePriceDisplay != null) ? freePriceDisplay.Value : x.don_gia_dich_vu.ToString("N0") + " đ",
                    ClinicId = request.ClinicId ?? string.Empty,
                    SubClinicId = request.SubClinicId ?? string.Empty,
                    ClinicCode = request.ClinicCode ?? string.Empty,
                    InsurancePrice = x.don_gia_bhyt,
                    InsurancePriceDisplay = insurancePriceDisplay?.Value ?? (x.don_gia_bhyt.ToString("N0") + " đ"),
                    ExameTypeId = request.ExameTypeId ?? string.Empty,
                    IsIgnoreInsurancePayment = current.IsIgnoreInsurancePayment,
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public async Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD")
        {
            string patientId = string.Empty;
            string patientCodeResponse = string.Empty;
            string careerId = string.Empty;

            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, new HisCustomerDto
                {
                    PatientCode = string.Empty,
                    CareerId = string.Empty,
                    PatientId = string.Empty
                });
            }

            string url = $"{hisConfig.Host}/api/hdbank/benhnhan";
            bool result;
            string message;
            (result, message, PatientModel resData) =
                await httpClientFactory.CreateClient().CreatePatient(new CreatePatientRequest()
                {
                    loai_gttt = "CCCD",
                    so_gttt = customer.IdentityNo ?? string.Empty
                },
                url, accessToken);

            if (result)
            {
                patientId = resData.id_bn ?? string.Empty;
                patientCodeResponse = resData.ma_bn ?? string.Empty;
                careerId = resData.ma_nghe_nghiep_his ?? string.Empty;
            }

            return (result, message, ErrorTypeEnum.NoError, new HisCustomerDto
            {
                PatientCode = patientCodeResponse,
                CareerId = careerId,
                PatientId = patientId
            });
        }

        public async Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers()
        {
            bool result = false;
            string message = string.Empty;
            List<GetSocialCareerDto> list = [];


            (bool authResult, string authMessage, string accessToken) = await GetAccessToken();

            if (!authResult)
            {
                return (authResult, authMessage, ErrorTypeEnum.HisError, []);
            }

            string url = $"{hisConfig.Host}/api/hdbank/nghenghiep";
            (result, message, List<SocialCareerModel> resData) =
                await httpClientFactory.CreateClient().GetSocialCareers(url, accessToken);

            if (result)
            {
                list = [.. resData!.Select(x => new GetSocialCareerDto()
                {
                    Id = x.Id ?? string.Empty,
                    Name = x.Text ?? string.Empty,
                    IsDefault = x.Id == "99"
                })];
            }

            return (result, message, ErrorTypeEnum.NoError, list);
        }

        public Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney()
        {
            return Task.FromResult<(bool, string, ErrorTypeEnum, string[]?)>((true, string.Empty, ErrorTypeEnum.NoError, null));
        }

        public Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdatePaymentStatusResponse()));
        }

        public Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request)
        {
            return HospitalHelper.GetDefaultHealthServices(current.HospitalId, request.codeMetaData, databaseService);
        }

        public Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new UpdateParaclinicalPaymentStatusResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new IndicationSearchResponse()));
        }

        public Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new QrPaymentDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new PushReceiptInfoResponseDto()));
        }

        private async Task<(bool, string, string)> GetAccessToken()
        {
            string accessToken = await cachedService.GetAsync<string>($"AccessToken_{current.HospitalId}") ?? string.Empty;

            if (!string.IsNullOrEmpty(accessToken))
            {
                return (true, string.Empty, accessToken);
            }
            string authUrl = $"{hisConfig.Host}/api/auth";

            (bool authResult, string authMessage, accessToken) = await
                httpClientFactory.CreateClient().GetAccessToken(new AuthRequest()
                {
                    UserName = hisConfig.UserName,
                    Password = hisConfig.Password,
                }, authUrl);
            if (authResult && !string.IsNullOrEmpty(accessToken))
            {
                await cachedService.SetAsync($"AccessToken_{current.HospitalId}", accessToken, expireSecond: 1500);
            }
            return (authResult, authMessage, accessToken);
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackageType>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthPackage>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<TechnicalService>()));
        }

        public Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request)
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new RegisterFormPackageResponseDto()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<HealthServiceSearchDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetAccidentCodeDto>()));
        }

        public Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes()
        {
            return Task.FromResult((true, string.Empty, ErrorTypeEnum.NoError, new List<GetBloodTypeDto>()));
        }
    }
}

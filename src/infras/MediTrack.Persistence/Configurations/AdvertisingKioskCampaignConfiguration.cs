using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations
{
    public class AdvertisingKioskCampaignConfiguration : IEntityTypeConfiguration<AdvertisingKioskCampaign>
    {
        public void Configure(EntityTypeBuilder<AdvertisingKioskCampaign> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_AdvertisingKioskCampaign");

            builder.HasIndex(x => new { x.KioskId, x.CampaignId })
                .HasDatabaseName("IX_AdvertisingKioskCampaign_KioskId_CampaignId");

            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.HasOne(x => x.Kiosk)
                .WithMany(x => x.CampaignParticipations)
                .HasForeignKey(x => x.KioskId)
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired();

            builder.HasOne(x => x.Campaign)
                .WithMany(x => x.KioskParticipations)
                .HasForeignKey(x => x.CampaignId)
                .OnDelete(DeleteBehavior.Cascade)
                .IsRequired();

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}

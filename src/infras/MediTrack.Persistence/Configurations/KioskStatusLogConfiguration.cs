﻿using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Persistence.Configurations
{
    public class KioskStatusLogConfiguration : IEntityTypeConfiguration<KioskStatusLog>
    {
        public void Configure(EntityTypeBuilder<KioskStatusLog> builder)
        {
            builder.HasKey(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_KioskStatusLog");
            builder.HasIndex(x => x.KioskId)
                .HasDatabaseName("IX_KioskStatusLog_KioskId");
            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}

using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations
{
    public class MembershipConfiguration : IEntityTypeConfiguration<Membership>
    {
        public void Configure(EntityTypeBuilder<Membership> builder)
        {
            builder.HasKey(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_Membership");
            builder.HasIndex(x => x.MemberCode)
                .HasDatabaseName("IX_Membership_MemberCode");
            builder.HasIndex(x => x.ContactPhone)
                .HasDatabaseName("IX_Membership_ContactPhone");
            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            // Relationship with Customer
            builder.HasOne(x => x.Customer)
                .WithMany()
                .HasForeignKey(x => x.CustomerId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}

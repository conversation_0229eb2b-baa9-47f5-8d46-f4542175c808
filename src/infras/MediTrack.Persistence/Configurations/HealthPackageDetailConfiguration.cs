using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations
{
    public class HealthPackageDetailConfiguration : IEntityTypeConfiguration<HealthPackageDetail>
    {
        public void Configure(EntityTypeBuilder<HealthPackageDetail> builder)
        {
            builder.HasKey(x => x.Id);
            builder.HasIndex(x => new { x.PackageServiceId, x.HealthServiceId })
                .HasDatabaseName("IX_HealthPackageDetail_PackageService_HealthService");
            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);

            // Relationship with PackageService
            builder.HasOne(x => x.PackageService)
                .WithMany(x => x.HealthPackageDetails)
                .HasForeignKey(x => x.PackageServiceId)
                .OnDelete(DeleteBehavior.Cascade);

            // Relationship with HealthService
            builder.HasOne(x => x.HealthService)
                .WithMany()
                .HasForeignKey(x => x.HealthServiceId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}

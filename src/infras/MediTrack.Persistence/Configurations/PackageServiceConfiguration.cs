using System.Collections.Generic;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace MediTrack.Persistence.Configurations
{
    public class PackageServiceConfiguration : IEntityTypeConfiguration<PackageService>
    {
        public void Configure(EntityTypeBuilder<PackageService> builder)
        {
            builder.<PERSON><PERSON>ey(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_PackageService");
            builder.HasQueryFilter(x => x.IsDeleted != true);

             builder.Property(x => x.Tags)
                    .HasColumnType("jsonb")
                    .HasConversion(
                        v => JsonConvert.SerializeObject(v),
                        v => JsonConvert.DeserializeObject<List<string>>(v) ?? new());

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}
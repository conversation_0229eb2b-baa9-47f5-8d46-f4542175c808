using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations
{
    public class AdvertisingPartnerMetaDataConfiguration : IEntityTypeConfiguration<AdvertisingPartnerMetaData>
    {
        public void Configure(EntityTypeBuilder<AdvertisingPartnerMetaData> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_AdvertisingPartnerMetaData");

            builder.HasIndex(x => new { x.GroupType, x.Code })
                .HasDatabaseName("IX_AdvertisingPartnerMetaData_GroupType_Code");
            
            builder.HasIndex(x => x.AdvertisingPartnerId)
                .HasDatabaseName("IX_AdvertisingPartnerMetaData_AdvertisingPartnerId");

            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}

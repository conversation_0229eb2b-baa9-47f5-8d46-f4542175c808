using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace MediTrack.Persistence.Configurations
{
    public class HospitalPatientBookingConfiguration : IEntityTypeConfiguration<HospitalPatientBooking>
    {
        public void Configure(EntityTypeBuilder<HospitalPatientBooking> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_HospitalPatientBooking");
            
            builder.HasIndex(x => x.HospitalId);
            builder.HasIndex(x => x.DateBooking);

            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}

using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace MediTrack.Persistence.Configurations
{
    public class HospitalNotificationConfiguration : IEntityTypeConfiguration<HospitalNotification>
    {
        public void Configure(EntityTypeBuilder<HospitalNotification> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_HospitalNotification");
            builder.HasQueryFilter(x => !x.IsDeleted);

            builder.Property(x => x.NotiConfig)
                    .HasColumnType("jsonb")
                    .HasConversion(
                        v => JsonConvert.SerializeObject(v),
                        v => JsonConvert.DeserializeObject<List<NotificationConfig>>(v) ?? new());

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Persistence.Configurations
{
    public class AppNotificationHistoryConfiguration
        : IEntityTypeConfiguration<AppNotificationHistory>
    {
        public void Configure(EntityTypeBuilder<AppNotificationHistory> builder)
        {
            builder.<PERSON><PERSON><PERSON>(x => x.Id);
            builder.HasIndex(x => x.Id)
                .HasDatabaseName("PK_AppNotificationHistory");
            builder.HasIndex(x => x.UserId)
                .HasDatabaseName("PK_AppNotificationHistory_UserId");
            builder.HasIndex(x => x.Type)
                .HasDatabaseName("PK_AppNotificationHistory_Type");

            builder.HasIndex(x => x.SentAt)
                .HasDatabaseName("PK_AppNotificationHistory_SentAt");

            builder.HasIndex(x => x.ReadAt)
                .HasDatabaseName("PK_AppNotificationHistory_ReadAt");

            builder.HasQueryFilter(x => x.IsDeleted != true);

            builder.Property(x => x.CreatedBy)
                .HasMaxLength(50);
            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(50);

            builder.Property(x => x.CreatedAt)
               .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
            builder.Property(x => x.CreatedBy)
                .ValueGeneratedOnAdd().Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore);
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using MediTrack.Persistence.Services;
using Npgsql;

namespace MediTrack.Persistence
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<DatabaseService>
    {
        public DatabaseService CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<DatabaseService>();
            
            // Sử dụng connection string mặc định cho design time
            // Bạn có thể thay đổi connection string này theo môi trường của bạn
            var connectionString = Environment.GetEnvironmentVariable("DATABASE") 
                ?? "Host=localhost;Database=meditrack_dev;Username=********;Password=********";

            var dataSource = new NpgsqlDataSourceBuilder(connectionString)
                .EnableDynamicJson()
                .Build();

            optionsBuilder.UseNpgsql(dataSource, c =>
            {
                c.EnableRetryOnFailure(5);
            });

            return new DatabaseService(optionsBuilder.Options);
        }
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddPackageServiceIdToMembership : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PackageServiceId",
                table: "Memberships",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Memberships_PackageServiceId",
                table: "Memberships",
                column: "PackageServiceId");

            migrationBuilder.AddForeignKey(
                name: "FK_Memberships_PackageServices_PackageServiceId",
                table: "Memberships",
                column: "PackageServiceId",
                principalTable: "PackageServices",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Memberships_PackageServices_PackageServiceId",
                table: "Memberships");

            migrationBuilder.DropIndex(
                name: "IX_Memberships_PackageServiceId",
                table: "Memberships");

            migrationBuilder.DropColumn(
                name: "PackageServiceId",
                table: "Memberships");
        }
    }
}

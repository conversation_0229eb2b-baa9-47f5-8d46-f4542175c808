﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddRegisterDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON>",
                table: "RegisterDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ClinicCode",
                table: "RegisterDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ClinicId",
                table: "RegisterDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExameType",
                table: "RegisterDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExameTypeId",
                table: "RegisterDetails",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "QueueNumber",
                table: "RegisterDetails",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RefNo",
                table: "RegisterDetails",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "RegisterDetails",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Clinic",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "ClinicCode",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "ClinicId",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "ExameType",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "ExameTypeId",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "QueueNumber",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "RefNo",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "RegisterDetails");
        }
    }
}

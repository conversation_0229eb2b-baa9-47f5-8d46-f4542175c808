﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddMemberShipIdToReceipt : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MembershipId",
                table: "Receipts",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AppNotificationHistories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    SubType = table.Column<string>(type: "text", nullable: false),
                    Title = table.Column<string>(type: "text", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    Metas = table.Column<Dictionary<string, string>>(type: "jsonb", nullable: true),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppNotificationHistories", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "PK_AppNotificationHistory",
                table: "AppNotificationHistories",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "PK_AppNotificationHistory_ReadAt",
                table: "AppNotificationHistories",
                column: "ReadAt");

            migrationBuilder.CreateIndex(
                name: "PK_AppNotificationHistory_SentAt",
                table: "AppNotificationHistories",
                column: "SentAt");

            migrationBuilder.CreateIndex(
                name: "PK_AppNotificationHistory_Type",
                table: "AppNotificationHistories",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "PK_AppNotificationHistory_UserId",
                table: "AppNotificationHistories",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppNotificationHistories");

            migrationBuilder.DropColumn(
                name: "MembershipId",
                table: "Receipts");
        }
    }
}

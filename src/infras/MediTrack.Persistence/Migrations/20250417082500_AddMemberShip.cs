﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddMemberShip : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MembershipId",
                table: "Registers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountAmount",
                table: "RegisterDetails",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "DiscountUnit",
                table: "RegisterDetails",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountValue",
                table: "RegisterDetails",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "Memberships",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    MemberCode = table.Column<string>(type: "text", nullable: false),
                    CardName = table.Column<string>(type: "text", nullable: false),
                    ContactPhone = table.Column<string>(type: "text", nullable: false),
                    UsageCount = table.Column<int>(type: "integer", nullable: true),
                    FromAge = table.Column<int>(type: "integer", nullable: true),
                    ToAge = table.Column<int>(type: "integer", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MinimumBalance = table.Column<decimal>(type: "numeric", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric", nullable: false),
                    CustomerId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Memberships", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Memberships_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "PackageServices",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Image = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Unit = table.Column<string>(type: "text", nullable: false),
                    OriginalPrice = table.Column<decimal>(type: "numeric", nullable: false),
                    PromotionPrice = table.Column<decimal>(type: "numeric", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    PackageType = table.Column<int>(type: "integer", nullable: false),
                    UsageCount = table.Column<int>(type: "integer", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Gender = table.Column<string>(type: "text", nullable: true),
                    FromAge = table.Column<int>(type: "integer", nullable: true),
                    ToAge = table.Column<int>(type: "integer", nullable: true),
                    ExpirationDays = table.Column<int>(type: "integer", nullable: true),
                    MembershipPrefix = table.Column<string>(type: "text", nullable: true),
                    DiscountValue = table.Column<decimal>(type: "numeric", nullable: false),
                    DiscountUnit = table.Column<string>(type: "text", nullable: true),
                    Balance = table.Column<decimal>(type: "numeric", nullable: false),
                    MinimumBalance = table.Column<decimal>(type: "numeric", nullable: false),
                    SortIndex = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PackageServices", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HealthPackageDetails",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PackageServiceId = table.Column<string>(type: "text", nullable: false),
                    HealthServiceId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HealthPackageDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HealthPackageDetails_HealthServices_HealthServiceId",
                        column: x => x.HealthServiceId,
                        principalTable: "HealthServices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HealthPackageDetails_PackageServices_PackageServiceId",
                        column: x => x.PackageServiceId,
                        principalTable: "PackageServices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Registers_MembershipId",
                table: "Registers",
                column: "MembershipId");

            migrationBuilder.CreateIndex(
                name: "IX_HealthPackageDetail_PackageService_HealthService",
                table: "HealthPackageDetails",
                columns: new[] { "PackageServiceId", "HealthServiceId" });

            migrationBuilder.CreateIndex(
                name: "IX_HealthPackageDetails_HealthServiceId",
                table: "HealthPackageDetails",
                column: "HealthServiceId");

            migrationBuilder.CreateIndex(
                name: "IX_Membership_ContactPhone",
                table: "Memberships",
                column: "ContactPhone");

            migrationBuilder.CreateIndex(
                name: "IX_Membership_MemberCode",
                table: "Memberships",
                column: "MemberCode");

            migrationBuilder.CreateIndex(
                name: "IX_Memberships_CustomerId",
                table: "Memberships",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "PK_Membership",
                table: "Memberships",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "PK_PackageService",
                table: "PackageServices",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Registers_Memberships_MembershipId",
                table: "Registers",
                column: "MembershipId",
                principalTable: "Memberships",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Registers_Memberships_MembershipId",
                table: "Registers");

            migrationBuilder.DropTable(
                name: "HealthPackageDetails");

            migrationBuilder.DropTable(
                name: "Memberships");

            migrationBuilder.DropTable(
                name: "PackageServices");

            migrationBuilder.DropIndex(
                name: "IX_Registers_MembershipId",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "MembershipId",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "DiscountAmount",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "DiscountUnit",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "DiscountValue",
                table: "RegisterDetails");
        }
    }
}

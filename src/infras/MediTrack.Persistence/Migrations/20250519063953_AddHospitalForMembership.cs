﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddHospitalForMembership : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "HospitalId",
                table: "Memberships",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_Memberships_HospitalId",
                table: "Memberships",
                column: "HospitalId");

            migrationBuilder.AddForeignKey(
                name: "FK_Memberships_Hospitals_HospitalId",
                table: "Memberships",
                column: "HospitalId",
                principalTable: "Hospitals",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Memberships_Hospitals_HospitalId",
                table: "Memberships");

            migrationBuilder.DropIndex(
                name: "IX_Memberships_HospitalId",
                table: "Memberships");

            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "Memberships");
        }
    }
}

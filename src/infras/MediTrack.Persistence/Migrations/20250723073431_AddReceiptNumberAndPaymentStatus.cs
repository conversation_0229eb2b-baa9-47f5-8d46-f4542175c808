﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddReceiptNumberAndPaymentStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DonationCampaignId",
                table: "Receipts",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PaymentStatus",
                table: "DonationHistories",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ReceiptNumber",
                table: "DonationHistories",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_DonationHistories_ReceiptNumber",
                table: "DonationHistories",
                column: "ReceiptNumber");

            migrationBuilder.AddForeignKey(
                name: "FK_DonationHistories_Receipts_ReceiptNumber",
                table: "DonationHistories",
                column: "ReceiptNumber",
                principalTable: "Receipts",
                principalColumn: "Number",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DonationHistories_Receipts_ReceiptNumber",
                table: "DonationHistories");

            migrationBuilder.DropIndex(
                name: "IX_DonationHistories_ReceiptNumber",
                table: "DonationHistories");

            migrationBuilder.DropColumn(
                name: "DonationCampaignId",
                table: "Receipts");

            migrationBuilder.DropColumn(
                name: "PaymentStatus",
                table: "DonationHistories");

            migrationBuilder.DropColumn(
                name: "ReceiptNumber",
                table: "DonationHistories");
        }
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddIsBlockUnder16YearsOld : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsBlockUnder16YearsOld",
                table: "Hospitals",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "ClinicCodeRes",
                table: "HealthServices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClinicIdRes",
                table: "HealthServices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExameTypeIdRes",
                table: "HealthServices",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsBlockUnder16YearsOld",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "ClinicCodeRes",
                table: "HealthServices");

            migrationBuilder.DropColumn(
                name: "ClinicIdRes",
                table: "HealthServices");

            migrationBuilder.DropColumn(
                name: "ExameTypeIdRes",
                table: "HealthServices");
        }
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditAdvertising : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Kiosks_AdvertisingCampaigns_CampaignId",
                table: "Kiosks");

            migrationBuilder.DropIndex(
                name: "IX_Kiosk_CampaignId",
                table: "Kiosks");

            migrationBuilder.DropColumn(
                name: "CampaignId",
                table: "Kiosks");

            migrationBuilder.AddColumn<bool>(
                name: "IsAutoOpenUltraViewer",
                table: "Kiosks",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "CampaignParticipations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    KioskId = table.Column<string>(type: "text", nullable: false),
                    CampaignId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignParticipations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CampaignParticipations_AdvertisingCampaigns_CampaignId",
                        column: x => x.CampaignId,
                        principalTable: "AdvertisingCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CampaignParticipations_Kiosks_KioskId",
                        column: x => x.KioskId,
                        principalTable: "Kiosks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KioskStatusLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    ApiAppStatus = table.Column<int>(type: "integer", nullable: true),
                    MediPayAppStatus = table.Column<int>(type: "integer", nullable: true),
                    AdvertisingAppStatus = table.Column<int>(type: "integer", nullable: true),
                    UltraViewAppStatus = table.Column<int>(type: "integer", nullable: true),
                    NetworkPing = table.Column<string>(type: "text", nullable: true),
                    UsedRamPercent = table.Column<string>(type: "text", nullable: true),
                    UsedCpuPercent = table.Column<string>(type: "text", nullable: true),
                    Uptime = table.Column<string>(type: "text", nullable: true),
                    KioskId = table.Column<string>(type: "text", nullable: true),
                    LogDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KioskStatusLogs", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdvertisingKioskCampaign_KioskId_CampaignId",
                table: "CampaignParticipations",
                columns: new[] { "KioskId", "CampaignId" });

            migrationBuilder.CreateIndex(
                name: "IX_CampaignParticipations_CampaignId",
                table: "CampaignParticipations",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "PK_AdvertisingKioskCampaign",
                table: "CampaignParticipations",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_KioskStatusLog_KioskId",
                table: "KioskStatusLogs",
                column: "KioskId");

            migrationBuilder.CreateIndex(
                name: "PK_KioskStatusLog",
                table: "KioskStatusLogs",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CampaignParticipations");

            migrationBuilder.DropTable(
                name: "KioskStatusLogs");

            migrationBuilder.DropColumn(
                name: "IsAutoOpenUltraViewer",
                table: "Kiosks");

            migrationBuilder.AddColumn<string>(
                name: "CampaignId",
                table: "Kiosks",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Kiosk_CampaignId",
                table: "Kiosks",
                column: "CampaignId");

            migrationBuilder.AddForeignKey(
                name: "FK_Kiosks_AdvertisingCampaigns_CampaignId",
                table: "Kiosks",
                column: "CampaignId",
                principalTable: "AdvertisingCampaigns",
                principalColumn: "Id");
        }
    }
}

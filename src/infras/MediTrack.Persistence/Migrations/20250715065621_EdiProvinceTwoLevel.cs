﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EdiProvinceTwoLevel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OtherName",
                table: "Wards",
                newName: "NewLevel");

            migrationBuilder.RenameColumn(
                name: "OtherName",
                table: "Provinces",
                newName: "NewLevel");

            migrationBuilder.AddColumn<string>(
                name: "NewId",
                table: "Wards",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NewName",
                table: "Wards",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NewProvinceId",
                table: "Wards",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NewId",
                table: "Provinces",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "NewName",
                table: "Provinces",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NewId",
                table: "Wards");

            migrationBuilder.DropColumn(
                name: "NewName",
                table: "Wards");

            migrationBuilder.DropColumn(
                name: "NewProvinceId",
                table: "Wards");

            migrationBuilder.DropColumn(
                name: "NewId",
                table: "Provinces");

            migrationBuilder.DropColumn(
                name: "NewName",
                table: "Provinces");

            migrationBuilder.RenameColumn(
                name: "NewLevel",
                table: "Wards",
                newName: "OtherName");

            migrationBuilder.RenameColumn(
                name: "NewLevel",
                table: "Provinces",
                newName: "OtherName");
        }
    }
}

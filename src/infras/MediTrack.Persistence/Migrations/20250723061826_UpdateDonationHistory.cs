﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class UpdateDonationHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UserName",
                table: "DonationHistories",
                newName: "FullName");

            migrationBuilder.AddColumn<string>(
                name: "Message",
                table: "DonationHistories",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Message",
                table: "DonationHistories");

            migrationBuilder.RenameColumn(
                name: "FullName",
                table: "DonationHistories",
                newName: "UserName");
        }
    }
}

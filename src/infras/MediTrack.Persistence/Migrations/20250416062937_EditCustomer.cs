﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditCustomer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EIDTransactionId",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FaceMatchingTransactionId",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HospitalId",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LivenessTransactionId",
                table: "Customers",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EIDTransactionId",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "FaceMatchingTransactionId",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "LivenessTransactionId",
                table: "Customers");
        }
    }
}

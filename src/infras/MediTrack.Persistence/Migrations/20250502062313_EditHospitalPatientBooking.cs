﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditHospitalPatientBooking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ClinicId",
                table: "HospitalPatientBookings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClinicName",
                table: "HospitalPatientBookings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthServiceId",
                table: "HospitalPatientBookings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthServiceName",
                table: "HospitalPatientBookings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClinicId",
                table: "HospitalPatientBookingConfigs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClinicName",
                table: "HospitalPatientBookingConfigs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthServiceId",
                table: "HospitalPatientBookingConfigs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthServiceName",
                table: "HospitalPatientBookingConfigs",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClinicId",
                table: "HospitalPatientBookings");

            migrationBuilder.DropColumn(
                name: "ClinicName",
                table: "HospitalPatientBookings");

            migrationBuilder.DropColumn(
                name: "HealthServiceId",
                table: "HospitalPatientBookings");

            migrationBuilder.DropColumn(
                name: "HealthServiceName",
                table: "HospitalPatientBookings");

            migrationBuilder.DropColumn(
                name: "ClinicId",
                table: "HospitalPatientBookingConfigs");

            migrationBuilder.DropColumn(
                name: "ClinicName",
                table: "HospitalPatientBookingConfigs");

            migrationBuilder.DropColumn(
                name: "HealthServiceId",
                table: "HospitalPatientBookingConfigs");

            migrationBuilder.DropColumn(
                name: "HealthServiceName",
                table: "HospitalPatientBookingConfigs");
        }
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddIsAllowOnlyOneQueuePerDay : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAllowOnlyOneQueuePerDay",
                table: "Hospitals",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "DialerQueueHistories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    IdentityNo = table.Column<string>(type: "text", nullable: true),
                    QueueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    QueueNumber = table.Column<int>(type: "integer", nullable: false),
                    DialerQueueId = table.Column<string>(type: "text", nullable: false),
                    HospitalId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DialerQueueHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DialerQueueHistories_DialerQueues_DialerQueueId",
                        column: x => x.DialerQueueId,
                        principalTable: "DialerQueues",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_DialerQueueHistories_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DialerQueueHistories_DialerQueueId",
                table: "DialerQueueHistories",
                column: "DialerQueueId");

            migrationBuilder.CreateIndex(
                name: "IX_DialerQueueHistories_HospitalId",
                table: "DialerQueueHistories",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "PK_DialerQueueHistory",
                table: "DialerQueueHistories",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DialerQueueHistories");

            migrationBuilder.DropColumn(
                name: "IsAllowOnlyOneQueuePerDay",
                table: "Hospitals");
        }
    }
}

﻿// <auto-generated />
using System;
using System.Collections.Generic;
using MediTrack.Persistence.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    [DbContext(typeof(DatabaseService))]
    [Migration("20250508025920_EditMemberShip")]
    partial class EditMemberShip
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingCampaign", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<TimeSpan?>("DailyEndTime")
                        .HasColumnType("interval");

                    b.Property<TimeSpan?>("DailyStartTime")
                        .HasColumnType("interval");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AdvertisingCampaign");

                    b.ToTable("AdvertisingCampaigns");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingKioskCampaign", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CampaignId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("KioskId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AdvertisingKioskCampaign");

                    b.HasIndex("KioskId", "CampaignId")
                        .HasDatabaseName("IX_AdvertisingKioskCampaign_KioskId_CampaignId");

                    b.ToTable("CampaignParticipations");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingPartner", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<DateTime>("ContractDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ContractNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxMediaPerCampaign")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PartnerCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("RepresentativeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Zone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AdvertisingPartner");

                    b.ToTable("AdvertisingPartners");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingPartnerCampaign", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CampaignId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("DurationInSeconds")
                        .HasColumnType("integer");

                    b.Property<string>("FileFormat")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("Height")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMediaActive")
                        .HasColumnType("boolean");

                    b.Property<int>("MediaOrder")
                        .HasColumnType("integer");

                    b.Property<string>("MediaType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PartnerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("Width")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AdvertisingPartnerCampaign");

                    b.HasIndex("PartnerId", "CampaignId")
                        .HasDatabaseName("IX_AdvertisingPartnerCampaign_PartnerId_CampaignId");

                    b.ToTable("AdvertisingPartnerCampaigns");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingPartnerMetaData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdvertisingPartnerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AdvertisingPartnerId")
                        .HasDatabaseName("IX_AdvertisingPartnerMetaData_AdvertisingPartnerId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AdvertisingPartnerMetaData");

                    b.HasIndex("GroupType", "Code")
                        .HasDatabaseName("IX_AdvertisingPartnerMetaData_GroupType_Code");

                    b.ToTable("AdvertisingPartnerMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AppBanner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<bool?>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<float?>("Latitude")
                        .HasColumnType("real");

                    b.Property<float?>("Longitude")
                        .HasColumnType("real");

                    b.Property<string>("Province")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AppBanner");

                    b.ToTable("AppBanners");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AppHome", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("GroupType")
                        .HasDatabaseName("PK_AppHome_GroupType");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_AppHome");

                    b.ToTable("AppHomes");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.BankBranch", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.ToTable("BankBranches");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Career", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BankCareerCodeRef")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("ParentId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Career");

                    b.ToTable("Careers");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Clinic", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExameTypeID")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int?>("ProcessingNumber")
                        .HasColumnType("integer");

                    b.Property<int?>("RemainingPatientCount")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalPatientCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("WaitingPatientCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Clinic");

                    b.ToTable("Clinics");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Customer", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountStatus")
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("BankTranId")
                        .HasColumnType("text");

                    b.Property<string>("CareerId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DistrictId")
                        .HasColumnType("text");

                    b.Property<string>("EIDTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("EducationLevel")
                        .HasColumnType("text");

                    b.Property<string>("FaceMatchingTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("HealthInsuranceNo")
                        .HasColumnType("text");

                    b.Property<string>("HealthInsurancePlace")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("IdentityIssueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IdentityIssuePlace")
                        .HasColumnType("text");

                    b.Property<string>("IdentityNo")
                        .HasColumnType("text");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("LivenessTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("Nation")
                        .HasColumnType("text");

                    b.Property<string>("NationalityId")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("PlaceOfOrigin")
                        .HasColumnType("text");

                    b.Property<string>("PlaceOfResidence")
                        .HasColumnType("text");

                    b.Property<string>("PositionId")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceId")
                        .HasColumnType("text");

                    b.Property<int>("RegistrationType")
                        .HasColumnType("integer");

                    b.Property<string>("Sex")
                        .HasColumnType("text");

                    b.Property<string>("Street")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WardId")
                        .HasColumnType("text");

                    b.Property<string>("WorkAddress")
                        .HasColumnType("text");

                    b.Property<string>("WorkPlace")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Customer");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerHospital", b =>
                {
                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<string>("CareerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFavourite")
                        .HasColumnType("boolean");

                    b.Property<string>("PatientCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PatientId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double?>("Rating")
                        .HasColumnType("double precision");

                    b.Property<string>("Review")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("CustomerId", "HospitalId");

                    b.HasIndex("HospitalId");

                    b.HasIndex("CustomerId", "HospitalId")
                        .HasDatabaseName("PK_CustomerHospital");

                    b.ToTable("CustomerHospitals");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerRelationShip", b =>
                {
                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("CustomerIdRefer")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("RelationshipId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("CustomerId", "CustomerIdRefer");

                    b.HasIndex("RelationshipId");

                    b.HasIndex("CustomerId", "CustomerIdRefer")
                        .HasDatabaseName("PK_CustomerRelationship");

                    b.ToTable("CustomerRelationShips");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerReview", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Problem")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<List<string>>("ServiceIds")
                        .HasColumnType("jsonb");

                    b.Property<string>("TypeId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_CustomerReview");

                    b.ToTable("CustomerReviews");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.DialerQueue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExameTypeId")
                        .HasColumnType("text");

                    b.Property<string>("HealthServiceId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPriority")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("QueueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("QueueNumber")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_DialerQueue");

                    b.ToTable("DialerQueues");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.District", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Level")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OtherName")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Distirct");

                    b.HasIndex("ProvinceId")
                        .HasDatabaseName("IX_District_ProvinceId");

                    b.ToTable("Districts");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentCatalog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentModelId")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentTypeCode")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentTypeName")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentVendorId")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<string>("ReferenceCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentModelId");

                    b.HasIndex("EquipmentVendorId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentCatalog");

                    b.ToTable("EquipmentCatalogs");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentModel", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentTypeCode")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentTypeName")
                        .HasColumnType("text");

                    b.Property<bool?>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentModel");

                    b.ToTable("EquipmentModels");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevoke", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HdBankBranchId")
                        .HasColumnType("text");

                    b.Property<string>("HdBankBranchName")
                        .HasColumnType("text");

                    b.Property<string>("HopistalId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("RevokeAccount")
                        .HasColumnType("text");

                    b.Property<string>("RevokeAddress")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RevokeAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RevokePhone")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WarehousePartnerId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentRevoke");

                    b.HasIndex("WarehousePartnerId");

                    b.ToTable("EquipmentRevokes");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevokeDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentCatalogId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EquipmentRevokeId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("Serial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentCatalogId");

                    b.HasIndex("EquipmentRevokeId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentRevokeDetail");

                    b.ToTable("EquipmentRevokeDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevokeHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentRevokeId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Flag")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentRevokeId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentRevokeHistory");

                    b.ToTable("EquipmentRevokeHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentVendor", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentVendor");

                    b.ToTable("EquipmentVendors");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentWarranty", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentId")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentModelId")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Serial")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("WarrantyEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("WarrantyStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentModelId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_EquipmentWarranty");

                    b.ToTable("EquipmentWarranties");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExameType", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDisabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIgnoreAutoCheckInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInsurance")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ExameType");

                    b.ToTable("ExameTypes");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExportedAccount")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExportedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HdBankBranchId")
                        .HasColumnType("text");

                    b.Property<string>("HdBankBranchName")
                        .HasColumnType("text");

                    b.Property<string>("HopistalId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("ReceiverAddress")
                        .HasColumnType("text");

                    b.Property<string>("ReceiverName")
                        .HasColumnType("text");

                    b.Property<string>("ReceiverPhone")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WarehousePartnerId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ExportedEquipment");

                    b.HasIndex("WarehousePartnerId");

                    b.ToTable("ExportedEquipments");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipmentDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentCatalogId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExportedEquipmentId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("Serial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentCatalogId");

                    b.HasIndex("ExportedEquipmentId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ExportedEquipmentDetail");

                    b.ToTable("ExportedEquipmentDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipmentHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExportedEquipmentId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Flag")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ExportedEquipmentId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ExportedEquipmentHistory");

                    b.ToTable("ExportedEquipmentHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ForgotPasswordSession", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ForgotPasswordSessions");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HealthPackageDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HealthServiceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("PackageServiceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("HealthServiceId");

                    b.HasIndex("PackageServiceId", "HealthServiceId")
                        .HasDatabaseName("IX_HealthPackageDetail_PackageService_HealthService");

                    b.ToTable("HealthPackageDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HealthService", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ClinicCode")
                        .HasColumnType("text");

                    b.Property<string>("ClinicCodeRes")
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroup")
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupCode")
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupId")
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .HasColumnType("text");

                    b.Property<string>("ClinicIdRes")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("ExameTypeId")
                        .HasColumnType("text");

                    b.Property<string>("ExameTypeIdRes")
                        .HasColumnType("text");

                    b.Property<string>("ExaminationHour")
                        .HasColumnType("text");

                    b.Property<string>("ExaminationLocation")
                        .HasColumnType("text");

                    b.Property<decimal?>("ExtraPrice")
                        .HasColumnType("numeric");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<decimal?>("InsurancePrice")
                        .HasColumnType("numeric");

                    b.Property<string>("InsurancePriceDisplay")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIgnoreInsurancePayment")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("ProcessingNumber")
                        .HasColumnType("integer");

                    b.Property<int?>("RemainingPatientCount")
                        .HasColumnType("integer");

                    b.Property<int>("SortIndex")
                        .HasColumnType("integer");

                    b.Property<string>("SubClinicId")
                        .HasColumnType("text");

                    b.Property<int?>("TotalPatientCount")
                        .HasColumnType("integer");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<string>("UnitPriceDisplay")
                        .HasColumnType("text");

                    b.Property<decimal?>("UnitPriceOfCurrency")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("WaitingPatientCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ClinicId")
                        .HasDatabaseName("IX_HealthService_HealthServiceId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HealthService");

                    b.ToTable("HealthServices");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HealthServiceMetaData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<string>("HealthServiceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HealthServiceId")
                        .HasDatabaseName("IX_HealthServiceMetaData_HealthServiceId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HealthServiceMetaData_Id");

                    b.HasIndex("GroupType", "Code")
                        .HasDatabaseName("PK_HealthServiceMetaData_GroupType_Code");

                    b.ToTable("HealthServiceMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.His", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DeploymentStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.ToTable("His");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HisMetaData", b =>
                {
                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<string>("HisId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("GroupType", "Code");

                    b.HasIndex("HisId");

                    b.HasIndex("GroupType", "Code")
                        .HasDatabaseName("PK_HisMetaData");

                    b.ToTable("HisMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Hospital", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountHolderName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Announcement")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("AutoLogoutTimeSeconds")
                        .HasColumnType("integer");

                    b.Property<string>("BankArea")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BankBranchCode")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BankTranPrefix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BankTransferId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BankZone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DeploymentMethod")
                        .HasColumnType("text");

                    b.Property<string>("DeploymentStatus")
                        .HasColumnType("text");

                    b.Property<string>("DistrictId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("FaceMatchingRateAccepted")
                        .HasColumnType("double precision");

                    b.Property<Dictionary<string, string>>("HisConfig")
                        .HasColumnType("jsonb");

                    b.Property<string>("HisId")
                        .HasColumnType("text");

                    b.Property<string>("HisInsuranceKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("HisUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("HisVersion")
                        .HasColumnType("text");

                    b.Property<List<string>>("Images")
                        .HasColumnType("text[]");

                    b.Property<string>("IpnUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAdvancePayment")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowCustomerRetryByPatientCode")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowInputAdvancePaymentAmount")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowPaymentCheckIn")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowPaymentGuarantee")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowPushQueueInfoToHis")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowSelectMultipleServicesDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAllowSelectNewPatient")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAppRegisterCheckInKioskDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAutoCheckAndSelectInsuranceType")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAutoSelectObject130")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBlockForDuplicatedVisitInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBlockInsuranceOnWeekend")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBlockUnder16YearsOld")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGenQR")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGenQRWhenCreateRegister")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGenQueueNumberByHis")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGenQueueNumberDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHealthCheckRegisterDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHideChangeAddressField")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHideInsuranceToggleButton")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHidePriorityQueueToggleButton")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHideQrCodePaymentDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHideReferralInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHisIgnoreCreateRegister")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIgnoreCheckInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIgnoreInsurancePayment")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInputReasonForVisit")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInputTransferReferralInfo")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInsuranceAdvancePayment")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMaintenanceMode")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNeedAuthenticateBeforeGetQueueNumber")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsNewFlow")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsParaclinicalExaminationDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPaymentDocumentDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReExaminationByDoctorDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReExaminationDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRePrintRegisterDocumentDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRegisterDocumentDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRegisterRelativeDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSearchPatientByInsuranceNo")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowArvTreatment")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowBloodOxygen")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowBloodPressure")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowCareer")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowEducationLevel")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowHeartRate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowHeight")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowInputEInvoiceInfo")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowPulseRate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowRespiratoryRate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowSocialCareer")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowTemperature")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowTransferReferralDate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowTransferReferralDiagnosisInfo")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowTransferReferralReason")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowTransferReferralType")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowWeight")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowWorkAddress")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsShowWorkPlace")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSkipGetInsuranceServices")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSkipGetServices")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSkipInputPhoneNumber")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSupportInsuranceDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSupportInsuranceInServiceScreenDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSupportVNeIDDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUseExtraFeeAsInsurancePrice")
                        .HasColumnType("boolean");

                    b.Property<string>("KioskVersion")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<float?>("Latitude")
                        .HasColumnType("real");

                    b.Property<int>("LivenessType")
                        .HasColumnType("integer");

                    b.Property<string>("LogoUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<float?>("Longitude")
                        .HasColumnType("real");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ParentId")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Dictionary<string, bool>>("RequireInputConfig")
                        .HasColumnType("jsonb");

                    b.Property<string>("SecretKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TaxCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WardId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BankBranchCode");

                    b.HasIndex("HisId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Hospital");

                    b.HasIndex("ParentId");

                    b.ToTable("Hospitals");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalMetaData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("GroupType")
                        .HasDatabaseName("PK_HospitalMetaData_GroupType");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HospitalMetaData");

                    b.ToTable("HospitalMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalNotification", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("NotiConfig")
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HospitalNotification");

                    b.ToTable("HospitalNotifications");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalPatientBooking", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .HasColumnType("text");

                    b.Property<string>("ClinicName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("CurrentBooking")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DateBooking")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("DayOfWeek")
                        .HasColumnType("integer");

                    b.Property<string>("HealthServiceId")
                        .HasColumnType("text");

                    b.Property<string>("HealthServiceName")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaxBooking")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("DateBooking");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HospitalPatientBooking");

                    b.ToTable("HospitalPatientBookings");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalPatientBookingConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AfternoonTimeZone")
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .HasColumnType("text");

                    b.Property<string>("ClinicName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("DayOfWeek")
                        .HasColumnType("integer");

                    b.Property<string>("HealthServiceId")
                        .HasColumnType("text");

                    b.Property<string>("HealthServiceName")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaxBooking")
                        .HasColumnType("integer");

                    b.Property<string>("MorningTimeZone")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_HospitalPatientBookingConfig");

                    b.ToTable("HospitalPatientBookingConfigs");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalUser", b =>
                {
                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("HospitalId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("HospitalUsers");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentVendorId")
                        .HasColumnType("text");

                    b.Property<string>("ImportedAccount")
                        .HasColumnType("text");

                    b.Property<string>("ImportedAddress")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ImportedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ImportedPhone")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("WarehousePartnerId")
                        .HasColumnType("text");

                    b.Property<string>("WarehousePartnerImportId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentVendorId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ImportedEquipment");

                    b.HasIndex("WarehousePartnerId");

                    b.HasIndex("WarehousePartnerImportId");

                    b.ToTable("ImportedEquipments");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipmentDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentCatalogId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ImportedEquipmentId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("Serial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentCatalogId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ImportedEquipmentDetail");

                    b.HasIndex("ImportedEquipmentId");

                    b.ToTable("ImportedEquipmentDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipmentHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("Flag")
                        .HasColumnType("integer");

                    b.Property<string>("ImportedEquipmentId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ImportedEquipmentHistory");

                    b.HasIndex("ImportedEquipmentId");

                    b.ToTable("ImportedEquipmentHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Kiosk", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Announcement")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Checksum")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("EffectiveDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Environment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsAdvancePayment")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsAllowSelectMultipleServices")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsAppRegisterCheckInKiosk")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAutoOpenUltraViewer")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAutoUpdate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsGenQueueNumber")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsHealthCheckRegister")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsHideQrCodePayment")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsInsuranceAdvancePayment")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMaintenanceMode")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsParaclinicalExamination")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsParticipatingInCampaign")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsPaymentDocument")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsReExamination")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsReExaminationByDoctor")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsRePrintRegisterDocument")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsRegisterDocument")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsRegisterRelative")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsSupportInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsSupportInsuranceInServiceScreen")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsSupportVNeID")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SecretKey")
                        .HasColumnType("text");

                    b.Property<string>("Serial")
                        .HasColumnType("text");

                    b.Property<string>("UltraViewId")
                        .HasColumnType("text");

                    b.Property<string>("UltraViewPassword")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId")
                        .HasDatabaseName("IX_Kiosk_HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Kiosk");

                    b.ToTable("Kiosks");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.KioskMetaData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("KioskId")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_KioskMetaData");

                    b.HasIndex("KioskId");

                    b.HasIndex("GroupType", "Code");

                    b.ToTable("KioskMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.KioskReleaseHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DownloadUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("PasswordExtract")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_KioskReleaseHistory_Id");

                    b.ToTable("KioskReleaseHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.KioskStatusLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int?>("AdvertisingAppStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("ApiAppStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("KioskId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LogDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("MediPayAppStatus")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("NetworkPing")
                        .HasColumnType("text");

                    b.Property<int?>("UltraViewAppStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Uptime")
                        .HasColumnType("text");

                    b.Property<string>("UsedCpuPercent")
                        .HasColumnType("text");

                    b.Property<string>("UsedRamPercent")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_KioskStatusLog");

                    b.HasIndex("KioskId")
                        .HasDatabaseName("IX_KioskStatusLog_KioskId");

                    b.ToTable("KioskStatusLogs");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Membership", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric");

                    b.Property<string>("CardName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DiscountUnit")
                        .HasColumnType("text");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("FromAge")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MemberCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("MinimumBalance")
                        .HasColumnType("numeric");

                    b.Property<int?>("ToAge")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("UsageCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ContactPhone")
                        .HasDatabaseName("IX_Membership_ContactPhone");

                    b.HasIndex("CustomerId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Membership");

                    b.HasIndex("MemberCode")
                        .HasDatabaseName("IX_Membership_MemberCode");

                    b.ToTable("Memberships");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Nation", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Nation");

                    b.ToTable("Nations");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Nationality", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("OtherName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Nationality");

                    b.ToTable("Nationalities");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ObjectPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("boolean");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("boolean");

                    b.Property<bool>("CanRead")
                        .HasColumnType("boolean");

                    b.Property<bool>("CanUpdate")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Criteria")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("ObjectName")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_ObjectPermission");

                    b.HasIndex("UserId")
                        .HasDatabaseName("PK_ObjectPermission_UserId");

                    b.ToTable("ObjectPermissions");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Otp", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Current")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Success")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Used")
                        .HasColumnType("boolean");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Otp");

                    b.HasIndex("Phone")
                        .HasDatabaseName("PK_Otp_Phone");

                    b.ToTable("Otps");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.PackageService", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DiscountUnit")
                        .HasColumnType("text");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("numeric");

                    b.Property<int?>("ExpirationDays")
                        .HasColumnType("integer");

                    b.Property<int?>("FromAge")
                        .HasColumnType("integer");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<string>("Image")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MembershipPrefix")
                        .HasColumnType("text");

                    b.Property<decimal>("MinimumBalance")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("numeric");

                    b.Property<int>("PackageType")
                        .HasColumnType("integer");

                    b.Property<decimal>("PromotionPrice")
                        .HasColumnType("numeric");

                    b.Property<int>("SortIndex")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("ToAge")
                        .HasColumnType("integer");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("UsageCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_PackageService");

                    b.ToTable("PackageServices");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.PatientBooking", b =>
                {
                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<decimal>("AdvancePayment")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("AppointmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AppointmentTimeZone")
                        .HasColumnType("text");

                    b.Property<double?>("BloodOxygen")
                        .HasColumnType("double precision");

                    b.Property<string>("BloodPressure")
                        .HasColumnType("text");

                    b.Property<string>("CareerId")
                        .HasColumnType("text");

                    b.Property<string>("ClinicCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("CustomerKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerRelationKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DistrictId")
                        .HasColumnType("text");

                    b.Property<string>("EducationLevel")
                        .HasColumnType("text");

                    b.Property<string>("ExameTypeId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("HealthServiceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("HealthcareServiceTierId")
                        .HasColumnType("text");

                    b.Property<string>("HealthcareServiceTypeId")
                        .HasColumnType("text");

                    b.Property<int?>("HeartRate")
                        .HasColumnType("integer");

                    b.Property<double?>("Height")
                        .HasColumnType("double precision");

                    b.Property<string>("HospitalId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOnARVTreatment")
                        .HasColumnType("boolean");

                    b.Property<string>("MaPhong")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MedicalTreatmentCategoryHisId")
                        .HasColumnType("text");

                    b.Property<string>("MedicalTreatmentCategoryId")
                        .HasColumnType("text");

                    b.Property<string>("PatientCode")
                        .HasColumnType("text");

                    b.Property<string>("PatientId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceId")
                        .HasColumnType("text");

                    b.Property<int?>("PulseRate")
                        .HasColumnType("integer");

                    b.Property<string>("ReasonForVisit")
                        .HasColumnType("text");

                    b.Property<string>("RefRegisterNumber")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RegisterAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RespiratoryRate")
                        .HasColumnType("integer");

                    b.Property<string>("ServiceCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SocialCareerId")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Street")
                        .HasColumnType("text");

                    b.Property<string>("SubClinicId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double?>("Temperature")
                        .HasColumnType("double precision");

                    b.Property<string>("TransferReferralDate")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDiagnosisInfo")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDiseaseCode")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDiseaseCodeAndName")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDocumentNumber")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralReason")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralType")
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralUnit")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("UuTien")
                        .HasColumnType("integer");

                    b.Property<string>("WardId")
                        .HasColumnType("text");

                    b.Property<double?>("Weight")
                        .HasColumnType("double precision");

                    b.Property<string>("WorkAddress")
                        .HasColumnType("text");

                    b.Property<string>("WorkPlace")
                        .HasColumnType("text");

                    b.HasKey("Number");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Number")
                        .HasDatabaseName("PK_PatientBooking");

                    b.ToTable("PatientBookings");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Payment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<string>("InvoiceInfoRef")
                        .HasColumnType("text");

                    b.Property<string>("IpnMessage")
                        .HasColumnType("text");

                    b.Property<string>("IpnStatus")
                        .HasColumnType("text");

                    b.Property<string>("IpnUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHisGenQr")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("QrCode")
                        .HasColumnType("text");

                    b.Property<string>("QrTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("ReceiptNumber")
                        .HasColumnType("text");

                    b.Property<string>("RefDesc")
                        .HasColumnType("text");

                    b.Property<string>("RefDescReq")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("RefNo")
                        .HasColumnType("text");

                    b.Property<string>("RefTran")
                        .HasColumnType("text");

                    b.Property<string>("RequestToPartner")
                        .HasColumnType("text");

                    b.Property<string>("ResponseFromPartner")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Payment");

                    b.HasIndex("ReceiptNumber");

                    b.HasIndex("RefNo");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Province", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Level")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OtherName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Province");

                    b.ToTable("Provinces");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Receipt", b =>
                {
                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<object>("AdditionalData")
                        .HasColumnType("jsonb");

                    b.Property<string>("CashierRef")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ClinicNameRef")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CreatedFrom")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("DeviceId")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("PatientCodeRef")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PatientDateOfBirthRef")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("PatientGenderRef")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("PatientNameRef")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("QrCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("ReceiptDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RefMessage")
                        .HasColumnType("text");

                    b.Property<string>("RefNo")
                        .HasColumnType("text");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("text");

                    b.Property<string>("ServiceNameRef")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Number");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HospitalId");

                    b.HasIndex("Number")
                        .HasDatabaseName("PK_Receipt");

                    b.HasIndex("RefNo");

                    b.HasIndex("RegisterNumber");

                    b.ToTable("Receipts");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByIp")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("GeneratedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("JwtId")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Register", b =>
                {
                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<string>("Clinic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroup")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicGroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("CustomerRelationId")
                        .HasColumnType("text");

                    b.Property<string>("DeviceId")
                        .HasColumnType("text");

                    b.Property<string>("ExameType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExameTypeId")
                        .HasColumnType("text");

                    b.Property<string>("ExaminationLocation")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("ExpectedAppointmentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("HealthInsurance")
                        .HasColumnType("integer");

                    b.Property<string>("HealthServiceId")
                        .HasColumnType("text");

                    b.Property<string>("HealthcareServiceTierId")
                        .HasColumnType("text");

                    b.Property<string>("HealthcareServiceTypeId")
                        .HasColumnType("text");

                    b.Property<string>("HisMessageWhenCreateForm")
                        .HasColumnType("text");

                    b.Property<string>("HospitalId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHisCreateFormSuccess")
                        .HasColumnType("boolean");

                    b.Property<string>("LinkCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("MedicalTreatmentCategoryHisId")
                        .HasColumnType("text");

                    b.Property<string>("MedicalTreatmentCategoryId")
                        .HasColumnType("text");

                    b.Property<string>("MedicalTreatmentCategoryName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MembershipId")
                        .HasColumnType("text");

                    b.Property<string>("PatientBookingNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PatientCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<string>("QueueNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("QueueNumberPriority")
                        .HasColumnType("boolean");

                    b.Property<string>("RateOfInsurance")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReasonForVisit")
                        .HasColumnType("text");

                    b.Property<string>("RefDocNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RefNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RefRegisterNumber")
                        .HasColumnType("text");

                    b.Property<string>("ReferralLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("RegisterAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RegisterTime")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegisterType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceAmount")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceTotalAmount")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceUnit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SubClinic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SubClinicId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDiseaseCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralDocumentNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransferReferralUnit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceOfCurrency")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Number");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HospitalId");

                    b.HasIndex("MembershipId");

                    b.HasIndex("Number")
                        .HasDatabaseName("PK_Register");

                    b.HasIndex("RefNo");

                    b.ToTable("Registers");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.RegisterDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Clinic")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClinicId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerAddress")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerSex")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("numeric");

                    b.Property<string>("DiscountUnit")
                        .HasColumnType("text");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("numeric");

                    b.Property<string>("ExameType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExameTypeId")
                        .HasColumnType("text");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("numeric");

                    b.Property<string>("HealthServiceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IdentityNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("NationalityId")
                        .HasColumnType("text");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<int?>("QueueNumber")
                        .HasColumnType("integer");

                    b.Property<string>("RefNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("text");

                    b.Property<string>("ServiceCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Status")
                        .HasColumnType("integer");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("numeric");

                    b.Property<decimal>("UnitPriceOfCurrency")
                        .HasColumnType("numeric");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_RegisterDetail_Id");

                    b.HasIndex("RegisterNumber");

                    b.ToTable("RegisterDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Relationship", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ReferId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Relationship");

                    b.ToTable("Relationships");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.HasIndex("UserId");

                    b.ToTable("Roles", (string)null);
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ServiceClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ClientDesription")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("SecretKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ServiceClients");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.SystemMetaData", b =>
                {
                    b.Property<string>("GroupType")
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("GroupTypeDesc")
                        .HasColumnType("text");

                    b.Property<bool?>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("GroupType", "Code");

                    b.HasIndex("GroupType", "Code")
                        .HasDatabaseName("PK_SystemMetaData");

                    b.ToTable("SystemMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("Avatar")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerId")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<bool?>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsFirstLogin")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IseKYC")
                        .HasColumnType("boolean");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .IsUnique();

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("PhoneNumber")
                        .HasDatabaseName("IX_User_PhoneNumber");

                    b.HasIndex("UserName")
                        .HasDatabaseName("IX_User_UserName");

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Ward", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DistrictId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Level")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OtherName")
                        .HasColumnType("text");

                    b.Property<string>("ProvinceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("DistrictId")
                        .HasDatabaseName("IX_Ward_DistirctId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_Ward");

                    b.HasIndex("ProvinceId")
                        .HasDatabaseName("IX_Ward_ProvinceId");

                    b.ToTable("Wards");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.WarehouseEquipment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentCatalogId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EquipmentRevokeId")
                        .HasColumnType("text");

                    b.Property<string>("EquipmentWarrantyId")
                        .HasColumnType("text");

                    b.Property<string>("ExportedEquipmentId")
                        .HasColumnType("text");

                    b.Property<string>("ImportedEquipmentId")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("Serial")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("EquipmentCatalogId");

                    b.HasIndex("EquipmentRevokeId");

                    b.HasIndex("EquipmentWarrantyId");

                    b.HasIndex("ExportedEquipmentId");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_WarehouseEquipment");

                    b.HasIndex("ImportedEquipmentId");

                    b.ToTable("WarehouseEquipments");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.WarehousePartner", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("Id")
                        .HasDatabaseName("PK_WarehousePartner");

                    b.ToTable("WarehousePartners");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingKioskCampaign", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.AdvertisingCampaign", "Campaign")
                        .WithMany("KioskParticipations")
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.Kiosk", "Kiosk")
                        .WithMany("CampaignParticipations")
                        .HasForeignKey("KioskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");

                    b.Navigation("Kiosk");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingPartnerCampaign", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.AdvertisingCampaign", "Campaign")
                        .WithMany("PartnerParticipations")
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.AdvertisingPartner", "Partner")
                        .WithMany("CampaignParticipations")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Clinic", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerHospital", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany("CustomerHospitals")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerRelationShip", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.Relationship", "Relationship")
                        .WithMany()
                        .HasForeignKey("RelationshipId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Relationship");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.CustomerReview", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.DialerQueue", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.District", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Province", "Province")
                        .WithMany("Districts")
                        .HasForeignKey("ProvinceId");

                    b.Navigation("Province");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentCatalog", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentModel", "EquipmentModel")
                        .WithMany()
                        .HasForeignKey("EquipmentModelId");

                    b.HasOne("MediTrack.Domain.Domain.EquipmentVendor", "EquipmentVendor")
                        .WithMany()
                        .HasForeignKey("EquipmentVendorId");

                    b.Navigation("EquipmentModel");

                    b.Navigation("EquipmentVendor");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevoke", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.WarehousePartner", "WarehousePartner")
                        .WithMany()
                        .HasForeignKey("WarehousePartnerId");

                    b.Navigation("Hospital");

                    b.Navigation("WarehousePartner");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevokeDetail", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentCatalog", "EquipmentCatalog")
                        .WithMany()
                        .HasForeignKey("EquipmentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.EquipmentRevoke", null)
                        .WithMany("EquipmentRevokeDetails")
                        .HasForeignKey("EquipmentRevokeId");

                    b.Navigation("EquipmentCatalog");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevokeHistory", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentRevoke", null)
                        .WithMany("EquipmentRevokeHistories")
                        .HasForeignKey("EquipmentRevokeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentWarranty", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentModel", "EquipmentModel")
                        .WithMany()
                        .HasForeignKey("EquipmentModelId");

                    b.Navigation("EquipmentModel");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipment", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.WarehousePartner", "WarehousePartner")
                        .WithMany()
                        .HasForeignKey("WarehousePartnerId");

                    b.Navigation("Hospital");

                    b.Navigation("WarehousePartner");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipmentDetail", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentCatalog", "EquipmentCatalog")
                        .WithMany()
                        .HasForeignKey("EquipmentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.ExportedEquipment", null)
                        .WithMany("ExportedEquipmentDetails")
                        .HasForeignKey("ExportedEquipmentId");

                    b.Navigation("EquipmentCatalog");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipmentHistory", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.ExportedEquipment", null)
                        .WithMany("ExportedEquipmentHistories")
                        .HasForeignKey("ExportedEquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ForgotPasswordSession", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HealthPackageDetail", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.HealthService", "HealthService")
                        .WithMany()
                        .HasForeignKey("HealthServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.PackageService", "PackageService")
                        .WithMany("HealthPackageDetails")
                        .HasForeignKey("PackageServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthService");

                    b.Navigation("PackageService");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HealthService", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Clinic", "Clinic")
                        .WithMany()
                        .HasForeignKey("ClinicId");

                    b.Navigation("Clinic");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HisMetaData", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.His", null)
                        .WithMany("HisMetaDatas")
                        .HasForeignKey("HisId");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Hospital", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.BankBranch", "BankBranch")
                        .WithMany("Hospitals")
                        .HasForeignKey("BankBranchCode");

                    b.HasOne("MediTrack.Domain.Domain.His", "His")
                        .WithMany("Hospitals")
                        .HasForeignKey("HisId");

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Parent")
                        .WithMany()
                        .HasForeignKey("ParentId");

                    b.Navigation("BankBranch");

                    b.Navigation("His");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalMetaData", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", null)
                        .WithMany("HospitalMetaDatas")
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalNotification", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalPatientBooking", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalPatientBookingConfig", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.HospitalUser", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.User", "User")
                        .WithMany("HospitalUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hospital");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipment", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentVendor", "EquipmentVendor")
                        .WithMany()
                        .HasForeignKey("EquipmentVendorId");

                    b.HasOne("MediTrack.Domain.Domain.WarehousePartner", "WarehousePartner")
                        .WithMany()
                        .HasForeignKey("WarehousePartnerId");

                    b.HasOne("MediTrack.Domain.Domain.WarehousePartner", "WarehousePartnerImport")
                        .WithMany()
                        .HasForeignKey("WarehousePartnerImportId");

                    b.Navigation("EquipmentVendor");

                    b.Navigation("WarehousePartner");

                    b.Navigation("WarehousePartnerImport");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipmentDetail", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentCatalog", "EquipmentCatalog")
                        .WithMany()
                        .HasForeignKey("EquipmentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.ImportedEquipment", null)
                        .WithMany("ImportedEquipmentDetails")
                        .HasForeignKey("ImportedEquipmentId");

                    b.Navigation("EquipmentCatalog");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipmentHistory", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.ImportedEquipment", null)
                        .WithMany("ImportedEquipmentHistories")
                        .HasForeignKey("ImportedEquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.KioskMetaData", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.Kiosk", "Kiosk")
                        .WithMany()
                        .HasForeignKey("KioskId");

                    b.Navigation("Hospital");

                    b.Navigation("Kiosk");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Membership", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.PatientBooking", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Hospital");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Payment", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.Receipt", "Receipt")
                        .WithMany("Payment")
                        .HasForeignKey("ReceiptNumber");

                    b.Navigation("Hospital");

                    b.Navigation("Receipt");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Receipt", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.Register", "Register")
                        .WithMany()
                        .HasForeignKey("RegisterNumber");

                    b.Navigation("Customer");

                    b.Navigation("Hospital");

                    b.Navigation("Register");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Register", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId");

                    b.HasOne("MediTrack.Domain.Domain.Hospital", "Hospital")
                        .WithMany()
                        .HasForeignKey("HospitalId");

                    b.HasOne("MediTrack.Domain.Domain.Membership", "Membership")
                        .WithMany()
                        .HasForeignKey("MembershipId");

                    b.Navigation("Customer");

                    b.Navigation("Hospital");

                    b.Navigation("Membership");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Role", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.User", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.User", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Customer", null)
                        .WithOne("User")
                        .HasForeignKey("MediTrack.Domain.Domain.User", "CustomerId");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Ward", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.District", "District")
                        .WithMany("Wards")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.Province", "Province")
                        .WithMany()
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("District");

                    b.Navigation("Province");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.WarehouseEquipment", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.EquipmentCatalog", "EquipmentCatalog")
                        .WithMany()
                        .HasForeignKey("EquipmentCatalogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.EquipmentRevoke", "EquipmentRevoke")
                        .WithMany()
                        .HasForeignKey("EquipmentRevokeId");

                    b.HasOne("MediTrack.Domain.Domain.EquipmentWarranty", "EquipmentWarranty")
                        .WithMany()
                        .HasForeignKey("EquipmentWarrantyId");

                    b.HasOne("MediTrack.Domain.Domain.ExportedEquipment", "ExportedEquipment")
                        .WithMany()
                        .HasForeignKey("ExportedEquipmentId");

                    b.HasOne("MediTrack.Domain.Domain.ImportedEquipment", "ImportedEquipment")
                        .WithMany()
                        .HasForeignKey("ImportedEquipmentId");

                    b.Navigation("EquipmentCatalog");

                    b.Navigation("EquipmentRevoke");

                    b.Navigation("EquipmentWarranty");

                    b.Navigation("ExportedEquipment");

                    b.Navigation("ImportedEquipment");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MediTrack.Domain.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("MediTrack.Domain.Domain.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingCampaign", b =>
                {
                    b.Navigation("KioskParticipations");

                    b.Navigation("PartnerParticipations");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.AdvertisingPartner", b =>
                {
                    b.Navigation("CampaignParticipations");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.BankBranch", b =>
                {
                    b.Navigation("Hospitals");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Customer", b =>
                {
                    b.Navigation("CustomerHospitals");

                    b.Navigation("User");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.District", b =>
                {
                    b.Navigation("Wards");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.EquipmentRevoke", b =>
                {
                    b.Navigation("EquipmentRevokeDetails");

                    b.Navigation("EquipmentRevokeHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ExportedEquipment", b =>
                {
                    b.Navigation("ExportedEquipmentDetails");

                    b.Navigation("ExportedEquipmentHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.His", b =>
                {
                    b.Navigation("HisMetaDatas");

                    b.Navigation("Hospitals");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Hospital", b =>
                {
                    b.Navigation("HospitalMetaDatas");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.ImportedEquipment", b =>
                {
                    b.Navigation("ImportedEquipmentDetails");

                    b.Navigation("ImportedEquipmentHistories");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Kiosk", b =>
                {
                    b.Navigation("CampaignParticipations");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.PackageService", b =>
                {
                    b.Navigation("HealthPackageDetails");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Province", b =>
                {
                    b.Navigation("Districts");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.Receipt", b =>
                {
                    b.Navigation("Payment");
                });

            modelBuilder.Entity("MediTrack.Domain.Domain.User", b =>
                {
                    b.Navigation("HospitalUsers");

                    b.Navigation("Roles");
                });
#pragma warning restore 612, 618
        }
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddHospitalPatientBookingConfigs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HospitalPatientBookingConfigs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DayOfWeek = table.Column<int>(type: "integer", nullable: true),
                    MorningTimeZone = table.Column<string>(type: "text", nullable: true),
                    AfternoonTimeZone = table.Column<string>(type: "text", nullable: true),
                    MaxBooking = table.Column<int>(type: "integer", nullable: true),
                    HospitalId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HospitalPatientBookingConfigs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HospitalPatientBookingConfigs_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "HospitalPatientBookings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DayOfWeek = table.Column<int>(type: "integer", nullable: true),
                    MaxBooking = table.Column<int>(type: "integer", nullable: true),
                    CurrentBooking = table.Column<int>(type: "integer", nullable: true),
                    DateBooking = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    HospitalId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HospitalPatientBookings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HospitalPatientBookings_Hospitals_HospitalId",
                        column: x => x.HospitalId,
                        principalTable: "Hospitals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HospitalPatientBookingConfigs_HospitalId",
                table: "HospitalPatientBookingConfigs",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "PK_HospitalPatientBookingConfig",
                table: "HospitalPatientBookingConfigs",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_HospitalPatientBookings_DateBooking",
                table: "HospitalPatientBookings",
                column: "DateBooking");

            migrationBuilder.CreateIndex(
                name: "IX_HospitalPatientBookings_HospitalId",
                table: "HospitalPatientBookings",
                column: "HospitalId");

            migrationBuilder.CreateIndex(
                name: "PK_HospitalPatientBooking",
                table: "HospitalPatientBookings",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HospitalPatientBookingConfigs");

            migrationBuilder.DropTable(
                name: "HospitalPatientBookings");
        }
    }
}

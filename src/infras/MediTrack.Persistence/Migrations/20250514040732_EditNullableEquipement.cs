﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditNullableEquipement : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ImportedEquipmentDetails_EquipmentCatalogs_EquipmentCatalog~",
                table: "ImportedEquipmentDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_WarehouseEquipments_EquipmentCatalogs_EquipmentCatalogId",
                table: "WarehouseEquipments");

            migrationBuilder.AlterColumn<string>(
                name: "Serial",
                table: "WarehouseEquipments",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "EquipmentCatalogId",
                table: "WarehouseEquipments",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "EquipmentCatalogId",
                table: "ImportedEquipmentDetails",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddForeignKey(
                name: "FK_ImportedEquipmentDetails_EquipmentCatalogs_EquipmentCatalog~",
                table: "ImportedEquipmentDetails",
                column: "EquipmentCatalogId",
                principalTable: "EquipmentCatalogs",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_WarehouseEquipments_EquipmentCatalogs_EquipmentCatalogId",
                table: "WarehouseEquipments",
                column: "EquipmentCatalogId",
                principalTable: "EquipmentCatalogs",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ImportedEquipmentDetails_EquipmentCatalogs_EquipmentCatalog~",
                table: "ImportedEquipmentDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_WarehouseEquipments_EquipmentCatalogs_EquipmentCatalogId",
                table: "WarehouseEquipments");

            migrationBuilder.AlterColumn<string>(
                name: "Serial",
                table: "WarehouseEquipments",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EquipmentCatalogId",
                table: "WarehouseEquipments",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EquipmentCatalogId",
                table: "ImportedEquipmentDetails",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ImportedEquipmentDetails_EquipmentCatalogs_EquipmentCatalog~",
                table: "ImportedEquipmentDetails",
                column: "EquipmentCatalogId",
                principalTable: "EquipmentCatalogs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_WarehouseEquipments_EquipmentCatalogs_EquipmentCatalogId",
                table: "WarehouseEquipments",
                column: "EquipmentCatalogId",
                principalTable: "EquipmentCatalogs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}

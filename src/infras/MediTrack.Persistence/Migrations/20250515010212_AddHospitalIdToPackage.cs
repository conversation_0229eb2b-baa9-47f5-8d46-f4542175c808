﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddHospitalIdToPackage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "HospitalId",
                table: "PackageServices",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PackageServices_HospitalId",
                table: "PackageServices",
                column: "HospitalId");

            migrationBuilder.AddForeignKey(
                name: "FK_PackageServices_Hospitals_HospitalId",
                table: "PackageServices",
                column: "HospitalId",
                principalTable: "Hospitals",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PackageServices_Hospitals_HospitalId",
                table: "PackageServices");

            migrationBuilder.DropIndex(
                name: "IX_PackageServices_HospitalId",
                table: "PackageServices");

            migrationBuilder.DropColumn(
                name: "HospitalId",
                table: "PackageServices");
        }
    }
}

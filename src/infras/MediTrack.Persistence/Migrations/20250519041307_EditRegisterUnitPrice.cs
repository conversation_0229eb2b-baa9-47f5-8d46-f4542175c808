﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditRegisterUnitPrice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ServiceAmount",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "ServiceTotalAmount",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "ServiceUnit",
                table: "Registers");

            migrationBuilder.RenameColumn(
                name: "UnitPrice",
                table: "Registers",
                newName: "SubTotalAmount");

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                table: "Registers",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountAmount",
                table: "Registers",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "DiscountUnit",
                table: "Registers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountValue",
                table: "Registers",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "SubTotalAmount",
                table: "RegisterDetails",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                table: "RegisterDetails",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            //update all register details
            migrationBuilder.Sql("UPDATE \"RegisterDetails\" SET \"SubTotalAmount\" = \"UnitPrice\" * \"Quantity\", \"TotalAmount\" = \"SubTotalAmount\" - \"DiscountAmount\"");
            migrationBuilder.Sql("UPDATE \"Registers\" SET \"TotalAmount\" = \"SubTotalAmount\"");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DiscountAmount",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "DiscountUnit",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "DiscountValue",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "Registers");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "RegisterDetails");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                table: "RegisterDetails");

            migrationBuilder.RenameColumn(
                name: "SubTotalAmount",
                table: "Registers",
                newName: "UnitPrice");

            migrationBuilder.AddColumn<string>(
                name: "ServiceAmount",
                table: "Registers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ServiceTotalAmount",
                table: "Registers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ServiceUnit",
                table: "Registers",
                type: "text",
                nullable: false,
                defaultValue: "");
        }
    }
}

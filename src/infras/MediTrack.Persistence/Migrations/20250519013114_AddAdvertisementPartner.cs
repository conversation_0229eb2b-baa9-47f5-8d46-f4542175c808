﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddAdvertisementPartner : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MembershipSuffix",
                table: "PackageServices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "SortIndex",
                table: "ExameTypes",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "SortIndex",
                table: "Clinics",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MembershipSuffix",
                table: "PackageServices");

            migrationBuilder.DropColumn(
                name: "SortIndex",
                table: "ExameTypes");

            migrationBuilder.DropColumn(
                name: "SortIndex",
                table: "Clinics");
        }
    }
}

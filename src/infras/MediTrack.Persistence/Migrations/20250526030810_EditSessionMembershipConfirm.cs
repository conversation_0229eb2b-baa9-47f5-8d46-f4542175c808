﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class EditSessionMembershipConfirm : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Customers_CustomerId",
                table: "SessionMembershipConfirms");

            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Hospitals_HospitalId",
                table: "SessionMembershipConfirms");

            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Memberships_MembershipId",
                table: "SessionMembershipConfirms");

            migrationBuilder.AlterColumn<string>(
                name: "MembershipId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "CustomerId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Customers_CustomerId",
                table: "SessionMembershipConfirms",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Hospitals_HospitalId",
                table: "SessionMembershipConfirms",
                column: "HospitalId",
                principalTable: "Hospitals",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Memberships_MembershipId",
                table: "SessionMembershipConfirms",
                column: "MembershipId",
                principalTable: "Memberships",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Customers_CustomerId",
                table: "SessionMembershipConfirms");

            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Hospitals_HospitalId",
                table: "SessionMembershipConfirms");

            migrationBuilder.DropForeignKey(
                name: "FK_SessionMembershipConfirms_Memberships_MembershipId",
                table: "SessionMembershipConfirms");

            migrationBuilder.AlterColumn<string>(
                name: "MembershipId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "HospitalId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CustomerId",
                table: "SessionMembershipConfirms",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Customers_CustomerId",
                table: "SessionMembershipConfirms",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Hospitals_HospitalId",
                table: "SessionMembershipConfirms",
                column: "HospitalId",
                principalTable: "Hospitals",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_SessionMembershipConfirms_Memberships_MembershipId",
                table: "SessionMembershipConfirms",
                column: "MembershipId",
                principalTable: "Memberships",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}

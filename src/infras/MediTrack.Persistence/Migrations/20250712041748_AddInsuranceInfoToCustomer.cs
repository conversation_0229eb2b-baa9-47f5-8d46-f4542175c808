﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddInsuranceInfoToCustomer : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "HealthInsuranceExpiredDate",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthInsuranceFromDate",
                table: "Customers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HealthInsurancePlaceId",
                table: "Customers",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HealthInsuranceExpiredDate",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "HealthInsuranceFromDate",
                table: "Customers");

            migrationBuilder.DropColumn(
                name: "HealthInsurancePlaceId",
                table: "Customers");
        }
    }
}

﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddReceiptType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ReceiptType",
                table: "Receipts",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReceiptType",
                table: "Receipts");
        }
    }
}

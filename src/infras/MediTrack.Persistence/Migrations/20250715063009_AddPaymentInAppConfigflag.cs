﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentInAppConfigflag : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsInsurancePaymentInAppAllowed",
                table: "Hospitals",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsInsurancePopupHiddenOnCorrectReferral",
                table: "Hospitals",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsServicePaymentInAppAllowed",
                table: "Hospitals",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsInsurancePaymentInAppAllowed",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "IsInsurancePopupHiddenOnCorrectReferral",
                table: "Hospitals");

            migrationBuilder.DropColumn(
                name: "IsServicePaymentInAppAllowed",
                table: "Hospitals");
        }
    }
}

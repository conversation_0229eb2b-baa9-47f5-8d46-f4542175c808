﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace MediTrack.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddAdvertisingPartnerMetaData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AdvertisingPartners",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Address",
                table: "AdvertisingPartners",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<DateTime>(
                name: "ContractDate",
                table: "AdvertisingPartners",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "ContractNo",
                table: "AdvertisingPartners",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PartnerCode",
                table: "AdvertisingPartners",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "RepresentativeName",
                table: "AdvertisingPartners",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Zone",
                table: "AdvertisingPartners",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "AdvertisingPartnerMetaDatas",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    GroupType = table.Column<string>(type: "text", nullable: true),
                    GroupTypeDesc = table.Column<string>(type: "text", nullable: true),
                    Code = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Value = table.Column<string>(type: "text", nullable: true),
                    AdvertisingPartnerId = table.Column<string>(type: "text", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdvertisingPartnerMetaDatas", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdvertisingPartnerMetaData_AdvertisingPartnerId",
                table: "AdvertisingPartnerMetaDatas",
                column: "AdvertisingPartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_AdvertisingPartnerMetaData_GroupType_Code",
                table: "AdvertisingPartnerMetaDatas",
                columns: new[] { "GroupType", "Code" });

            migrationBuilder.CreateIndex(
                name: "PK_AdvertisingPartnerMetaData",
                table: "AdvertisingPartnerMetaDatas",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdvertisingPartnerMetaDatas");

            migrationBuilder.DropColumn(
                name: "ContractDate",
                table: "AdvertisingPartners");

            migrationBuilder.DropColumn(
                name: "ContractNo",
                table: "AdvertisingPartners");

            migrationBuilder.DropColumn(
                name: "PartnerCode",
                table: "AdvertisingPartners");

            migrationBuilder.DropColumn(
                name: "RepresentativeName",
                table: "AdvertisingPartners");

            migrationBuilder.DropColumn(
                name: "Zone",
                table: "AdvertisingPartners");

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AdvertisingPartners",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Address",
                table: "AdvertisingPartners",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}

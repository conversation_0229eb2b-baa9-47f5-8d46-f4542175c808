﻿using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediTrack.Application.Services;
using MongoDB.Driver;

namespace MediTrack.Persistence.Services
{
    public class MongoService : IMongoService
    {
        private readonly IMongoDatabase _database;

        public MongoService(string connectionString, string databaseName)
        {
            var client = new MongoClient(connectionString);
            _database = client.GetDatabase(databaseName);
        }

        public IMongoCollection<VNEIDIdentityInfo> VNEIDIdentityInfos =>
            _database.GetCollection<VNEIDIdentityInfo>("VNEIDIdentityInfos");
    }
}
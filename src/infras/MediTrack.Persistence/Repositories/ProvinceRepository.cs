﻿using System.Text.Json;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Persistence.Repositories
{
    public class ProvinceRepository(
        ICachedService cachedService,
        IDatabaseService databaseService) : IProvinceRepository
    {
        public async Task<List<ProvinceDto>> GetProvincedAsync(CancellationToken cancellationToken = default)
        {
            var provinces = await cachedService.GetAsync<List<ProvinceDto>>($"Provinces", cancellationToken);
            if (provinces is null || provinces.Count == 0)
            {
                provinces = await databaseService.Provinces
                    .AsNoTracking()
                    .Select(x => new ProvinceDto
                    {
                        Id = x.Id,
                        Name = x.Name.Trim(),
                        OtherName = x.Name.Replace(x.Level ?? string.Empty, "").Trim(),
                        Level = x.Level,
                        NewId = x.NewId,
                        NewName = x.NewName,
                        NewOtherName = x.NewName.Replace(x.NewLevel ?? string.Empty, "").Trim(),
                        NewLevel = x.NewLevel
                    })
                    .ToListAsync(cancellationToken);

                await cachedService.SetAsync($"Provinces", provinces, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            return provinces;
        }
    }
}

﻿using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Persistence.Repositories
{
    public class WardRepository(ICachedService cachedService,
        IDatabaseService databaseService) : IWardRepository
    {
        public async Task<List<WardDto>> GetWardByParentIdAsync(string? parentId, bool isTwoLevel, CancellationToken cancellationToken = default)
        {
            var wards = await cachedService.GetAsync<List<WardDto>>($"Wards", cancellationToken);

            if (wards is null || wards.Count == 0)
            {
                //if new, get data from SystemConfig
                wards = await databaseService.Wards
                    .AsNoTracking()
                    .Select(x => new WardDto()
                    {
                        Id = x.Id,
                        Name = x.Name.Trim(),
                        OtherName = x.Name.Replace(x.Level ?? string.Empty, "").Trim(),
                        Level = x.Level,
                        DistrictId = x.DistrictId,
                        ProvinceId = x.ProvinceId,
                        NewId = x.NewId,
                        NewName = x.NewName,
                        NewOtherName = x.NewName.Replace(x.NewLevel ?? string.Empty, "").Trim(),
                        NewLevel = x.NewLevel,
                        NewProvinceId = x.NewProvinceId
                    })
                    .ToListAsync(cancellationToken);

                await cachedService.SetAsync($"Wards", wards, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            if(string.IsNullOrEmpty(parentId))
            {
                return wards;
            }

            return [.. wards.Where(x => parentId == (isTwoLevel ? x.NewProvinceId : x.DistrictId))];
        }

        public async Task<WardDto?> GetWardByIdAsync(string id, bool isTwoLevel, CancellationToken cancellationToken = default)
        {
            var wards = await cachedService.GetAsync<List<WardDto>>($"Wards", cancellationToken);

            if (wards is null || wards.Count == 0)
            {
                //if new, get data from SystemConfig
                wards = await databaseService.Wards
                    .AsNoTracking()
                    .Select(x => new WardDto()
                    {
                        Id = x.Id,
                        Name = x.Name.Trim(),
                        OtherName = x.Name.Replace(x.Level ?? string.Empty, "").Trim(),
                        Level = x.Level,
                        DistrictId = x.DistrictId,
                        ProvinceId = x.ProvinceId,
                        NewId = x.NewId,
                        NewName = x.NewName,
                        NewOtherName = x.NewName.Replace(x.NewLevel ?? string.Empty, "").Trim(),
                        NewLevel = x.NewLevel,
                        NewProvinceId = x.NewProvinceId
                    })
                    .ToListAsync(cancellationToken);

                await cachedService.SetAsync($"Wards", wards, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            return wards.FirstOrDefault(x => isTwoLevel ? x.NewId == id : x.Id == id);
        }
    }
}

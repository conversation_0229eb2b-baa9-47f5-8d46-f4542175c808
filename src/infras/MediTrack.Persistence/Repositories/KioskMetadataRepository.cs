using MediTrack.Application.Features.KioskMetadataLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Persistence.Repositories
{
    public class KioskMetadataRepository(ICachedService cachedService, IDatabaseService databaseService) : IKioskMetadataRepository
    {
        public async Task<List<KioskMetadataDto>> GetKioskMetadatasByKeyAsync(string? hospitalId, string? kioskId, string? groupType = null, string? code = null, CancellationToken cancellationToken = default)
        {
            var kioskMetadatas = await cachedService.GetAsync<List<KioskMetadataDto>>("KioskMetadatas" + kioskId, cancellationToken);
            if (kioskMetadatas is null || kioskMetadatas.Count == 0)
            {
                kioskMetadatas = await databaseService.KioskMetaDatas
                    .Where(x => x.HospitalId == hospitalId && x.KioskId == kioskId)
                    .AsNoTracking()
                    .Select(x => new KioskMetadataDto()
                    {
                        HospitalId = x.HospitalId,
                        KioskId = x.KioskId,
                        GroupType = x.GroupType,
                        GroupTypeDesc = x.GroupTypeDesc,
                        Code = x.Code,
                        Title = x.Title,
                        Value = x.Value
                    })
                    .ToListAsync(cancellationToken);
                await cachedService.SetAsync("KioskMetadatas" + kioskId, kioskMetadatas, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            return kioskMetadatas?.Where(x => (groupType == null || x.GroupType == groupType)
                && (code == null || x.Code == code)).ToList() ?? [];
        }

        public async Task<List<KioskMetadataDto>> GetKioskMetadatasByKioskHospitalAsync(string? hospitalId, string? kioskId, CancellationToken cancellationToken = default)
        {
            var kioskMetadatas = await cachedService.GetAsync<List<KioskMetadataDto>>("KioskMetadatas" + kioskId, cancellationToken);
            if (kioskMetadatas is null || kioskMetadatas.Count == 0)
            {
                kioskMetadatas = await databaseService.KioskMetaDatas
                    .Where(x => x.HospitalId == hospitalId && x.KioskId == kioskId)
                    .AsNoTracking()
                    .Select(x => new KioskMetadataDto()
                    {
                        HospitalId = x.HospitalId,
                        KioskId = x.KioskId,
                        GroupType = x.GroupType,
                        GroupTypeDesc = x.GroupTypeDesc,
                        Code = x.Code,
                        Title = x.Title,
                        Value = x.Value
                    })
                    .ToListAsync(cancellationToken);
                await cachedService.SetAsync("KioskMetadatas" + kioskId, kioskMetadatas, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            return kioskMetadatas ?? [];
        }
    }
}
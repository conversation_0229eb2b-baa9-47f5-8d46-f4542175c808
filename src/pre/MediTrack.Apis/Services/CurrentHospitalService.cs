﻿using MediTrack.Application.Exceptions;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using Microsoft.Extensions.Primitives;
using Serilog;

namespace MediTrack.Apis.Services
{
    public class CurrentHospitalService : ICurrentHospitalService
    {
        private const string DeviceHeader = "x-device-id";
        private const string TraceIdHeader = "x-trace-id";

        public CurrentHospitalService(IHttpContextAccessor httpContextAccessor,
            IHisServiceHelper hisServiceHelpers,
            ICachedService cachedService,
            IProvinceRepository provinceRepository,
            IDistrictRepository districtRepository,
            IWardRepository wardRepository,
            IHospitalMetadataRepository hospitalMetadataRepository,
            IDatabaseService databaseService,
            IHttpClientFactory httpClientFactory)
        {
            HisService = default!;
            CurrentHospital = default!;
            LogPrefix = string.Empty;
            KioskId = string.Empty;
            string? hospitalId = null;

            if (IsMobileAppAgent(httpContextAccessor))
            {
                hospitalId = httpContextAccessor.HttpContext?
                    .Request.Headers["x-hospital-id"].ToString() ?? string.Empty;
                KioskId = httpContextAccessor.HttpContext?
                    .Request.Headers[DeviceHeader].ToString() ?? string.Empty;
            }
            else
            {
                KioskId = httpContextAccessor.HttpContext?
                    .Request.Headers["x-kiosk-id"].ToString() ?? string.Empty;

                if (!string.IsNullOrEmpty(KioskId))
                {
                    var kiosk = hisServiceHelpers.GetKiosk(KioskId, databaseService).Result;
                    hospitalId = kiosk?.HospitalId ?? string.Empty;

                    if (kiosk?.IsMaintenanceMode == true)
                    {
                        throw new MaintenanceException();
                    }
                }
            }

            if(!string.IsNullOrEmpty(hospitalId))
            {
                LogPrefix = $"{hospitalId}|{KioskId}|{httpContextAccessor.HttpContext?.Request.Headers[TraceIdHeader].ToString() ?? string.Empty}:";

                // TODO: Thông tin config đang lấy theo hospitalId, cần lấy theo kioskId nếu có
                CurrentHospital = hisServiceHelpers.GetHospitalServiceById(hospitalId, databaseService);

                if (CurrentHospital.IsMaintenanceMode)
                {
                    throw new MaintenanceException();
                }

                HisService = hisServiceHelpers.CreateHisService(CurrentHospital, httpClientFactory, databaseService, cachedService, LogPrefix,
                    hospitalMetadataRepository, provinceRepository, districtRepository, wardRepository);
            }
        }

        public bool IsMobileAppAgent(IHttpContextAccessor httpContextAccessor)
        {
            if (httpContextAccessor.HttpContext?.Request.Headers.TryGetValue(key: DeviceHeader,
                out StringValues deviceHeaders) == true)
            {
                return deviceHeaders.Any(v => string.Equals(v, "mobile", StringComparison.OrdinalIgnoreCase)) ||
                    deviceHeaders.Any(v => string.Equals(v, "admin", StringComparison.OrdinalIgnoreCase));
            }

            return false;
        }

        public CurrentHospitalDto CurrentHospital { get; set; }

        /// <summary>
        /// Object HIS của kiosk hiện tại
        /// </summary>
        public IHisService HisService { get; set; }

        public string LogPrefix { get; set; }
        public string KioskId { get; set; }
    }
}

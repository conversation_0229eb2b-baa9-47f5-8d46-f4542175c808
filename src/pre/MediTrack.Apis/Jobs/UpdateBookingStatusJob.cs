using MediTrack.Application.Schedules;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Apis.Jobs
{
    public class UpdateBookingStatusJob(IDatabaseService databaseService, CancellationToken cancellationToken = default) : IUpdateBookingStatusJob
    {
        public async Task ExecuteAsync()
        {
            Log.Information("UpdateStatusJob started at {Time}", DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyy-MM-dd HH:mm:ss"));
            try
            {
                var bookings = await databaseService.PatientBookings
                    .Where(x => x.Status == PatientBookingStatus.PENDING
                    && x.AppointmentDate.HasValue
                    && x.AppointmentDate.Value.AddHours(7).Date <= DateTimeHelper.GetCurrentLocalDateTime().Date)
                    .ToListAsync();
                Log.Information("UpdateStatusJob found {Count} bookings to update, List: {@list}", bookings.Count, bookings);

                if (bookings.Count == 0)
                {
                    Log.Information("UpdateStatusJob no bookings to update");
                    return;
                }

                bookings.ForEach(x => x.Status = PatientBookingStatus.EXPIRED);
                databaseService.PatientBookings.UpdateRange(bookings);

                var result = await databaseService.SaveChangesAsync(cancellationToken);
                if (result > 0)
                {
                    Log.Information("UpdateStatusJob updated {Count} bookings successfully", result);
                }
                else
                {
                    Log.Warning("UpdateStatusJob failed to update bookings");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred while executing UpdateBookingStatusJob: {Message}", ex.Message);
            }

        }
    }
}
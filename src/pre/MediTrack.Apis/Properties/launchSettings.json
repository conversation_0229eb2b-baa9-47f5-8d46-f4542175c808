{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "PATH_BASE": "aaa/reseller", "INIT_DATABASE": "True", "DATABASE": "Server=**************;User Id=MasterUsr;Password=***************;Port=5432;Database=MediTrack.Local;Pooling=true"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5007"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "PATH_BASE": "meditrack", "INIT_DATABASE": "True", "DATABASE": "Server=**************;User Id=MasterUsr;Password=***************;Port=5432;Database=MediTrack.Local;Pooling=true", "HDBANK_Url": "https://openbanking-uat.hdbank.com.vn", "HDBANK_UrlUploadFileHost": "https://openbanking-uat.hdbank.com.vn/eks", "HDBANK_UrlUploadFilePath": "/dms/v1/file/create", "HDBANK_UrlCheckBankAccount": "/eks/hdss/journey/medipay/type", "HDBANK_UrlConfirmOtp": "/eks/hdss/journey/medipay/ntb/otp-verification", "HDBANK_UrlSendOtp": "/eks/hdss/journey/medipay/ntb/otp-request", "HDBANK_UrlCheckStatusOnBoarding": "/eks/hdss/journey/medipay/status", "HDBANK_UrlUpdateCustomerInfo": "/eks/hdss/journey/medipay/information", "HDBANK_PassPfx": "EPjrpe751XMLCATi", "HDBANK_SecretKey": "hdss", "HDBANK_PartnerId": "MEDIPAY", "HDBANK_ApiKey": "UH5l362Ky2bvhRNuAfua84iujwszBCs7u1C1y2u0", "HDBANK_ChannelId": "channeltes", "HDBANK_AppVersion": "2", "HDBANK_PartnerUserId": "partnerUserId", "HDBANK_UploadCategoryCode": "customer#medipay#ekyc#image", "HDBANK_SecretKeyUploadFile": "medipay", "PAYMENT_PaymentUrl": "https://api-dev.gotrust.vn/payment/api/v1/payments/qrCode", "PAYMENT_IpnUrl": "https://google.com", "PAYMENT_SecretKey": "31quHvzJhA4kVJJNn7a5EPXsL6q9sXPsqm", "MediBank_Url": "https://openbanking.hdbank.com.vn/eks/medibank", "MediBank_CheckAccountUrl": "/api/hdbank/check-account", "MediBank_CheckStatusOnBoardingUrl": "/api/hdbank/check-status-onboarding", "MediBank_ConfirmOtpUrl": "/api/hdbank/otp-verification", "MediBank_SendOtpUrl": "/api/hdbank/otp-request", "MediBank_UpdateAccountUrl": "/api/hdbank/account", "MediBank_UploadFileUrl": "/api/hdbank/upload-face-base64", "MediBank_UploadIdentityUrl": "/api/hdbank/upload-identity-base64", "MediBank_GenQrTransactionUrl": "/api/hdbank/gen-qr", "MediBank_CreateTransferUrl": "/api/hdbank/create-transfer", "MediBank_GetAvailableBalanceUrl": "/api/hdbank/get-available-balance", "MediBank_CreateLoginQrUrl": "/api/hdbank/create-login-qr", "MediBank_CheckLoginQrStatusUrl": "/api/hdbank/get-access-token-login", "MediBank_GetAllAccountUrl": "/api/hdbank/get-all-account", "MediBank_GetStatusAccountLinkUrl": "/api/hdbank/get-status-account-link", "MediBank_GetCareesUrl": "/api/hdbank/get-carees", "MediBank_PassPfx": "EPjrpe751XMLCATi", "MediBank_UsePem": "TRUE", "MediBank_Cert": "certificate_prod.pfx", "S3_AccessKey": "MVT04V942I425XYUZ3B3", "S3_SecretKey": "VJf2Is1G4e7L8Vm3FhHdueLDTWXu9waisdw1VsGh", "S3_ServiceUrl": "https://s3.cloud.cmctelecom.vn", "Identity_VneidUrlGetLinkSSO": "https://api-dev.gotrust.vn/identity/v1/vneid/sso", "Identity_VneidUrlGetSSOUser": "https://api-dev.gotrust.vn/identity/v1/vneid/sso/user", "Identity_VerifyLeeonEIDUrl": "https://api-dev.gotrust.vn/identity/v1/identity/leeon/verify/eID", "Identity_VerifyLeeonLivenessUrl": "https://api-dev.gotrust.vn/identity/v1/identity/leeon/verify/liveness", "Identity_VerifyLeeonFacematchingUrl": "https://api-dev.gotrust.vn/identity/v1/identity/leeon/verify/face-matching", "Identity_FaceSearchLeeonAddFaceUrl": "https://api-dev.gotrust.vn/identity/v1/identity/leeon/facesearch/add-face", "Identity_FaceSearchLeeonFindFaceUrl": "https://api-dev.gotrust.vn/identity/v1/identity/leeon/facesearch/find-face", "AgentGateWay_Host": "https://localhost:7013", "MONGO_DATABASE_URI": "**********************************************", "MONGO_DATABASE_VNEID": "Medi_VNEID", "MONGO_DATABASE_HANGFIRE": "Medi_Hangfires", "TELEGRAM_SERILOG_API": "**********************************************", "TELEGRAM_SERILOG_CHAT_ID": "-*************", "TELEGRAM_API": "**********************************************", "TELEGRAM_CHAT_ID": "-*************", "Matching": "83.0", "Chatbot_Url": "https://staging-api.ivita.vn/v1/integration/webview/login", "UpdateAuthen_Config": "{\"Host\":\"**************\",\"Username\":\"mediPayClient\",\"Password\":\"Client@123\",\"Port\":\"8822\"}", "MediBank_ApiKey": "EULHZ3EhkSbjpiNFuGyB1O6fWRkd6UU5lBEN908f", "CAService_Url": "https://api-dev.gotrust.vn/ca-service", "CAService_EMRUrl": "https://api-dev.gotrust.vn/emr-management", "CAService_Username": "<EMAIL>", "CAService_Password": "Pa$$w0rd"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7053;http://localhost:5007"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:63659", "sslPort": 44322}}}
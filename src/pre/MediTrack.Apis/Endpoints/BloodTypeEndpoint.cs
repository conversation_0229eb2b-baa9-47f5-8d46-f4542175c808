using MediatR;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Swashbuckle.AspNetCore.Annotations;
using MediTrack.Apis.Extensions;

namespace MediTrack.Apis.Endpoints
{
    public class BloodTypeEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/blood-types")
                .WithTags("BloodTypes")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapGet(GetBloodTypes);
        }

        [SwaggerOperation(Summary = "Api lấy ra danh sách nhóm máu")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<GetBloodTypeDto>>>> GetBloodTypes(IMediator mediator)
        {
            var req = new GetBloodType();
            var res = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<IEnumerable<GetBloodTypeDto>>(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}
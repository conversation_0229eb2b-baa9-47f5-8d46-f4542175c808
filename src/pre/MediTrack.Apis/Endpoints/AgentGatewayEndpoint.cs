using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Commands;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediTrack.Application.Features.AgentGatewayLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class VNEIDEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/agent-gateway")
                .WithTags("AgentGateway")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapPost(SaveIdentityAsync, "identity")
                .MapGet(GetIdentityAsync, "{session}/identity")
                .MapPost(InitTransactionAsync, "init-transaction")
                .MapPost(GetTransactionAsync, "transaction")
                .MapPost(RequestUserSharedAsync, "user-shared")
                .MapGet(GetUserShareInfoAsync, "share-info/{txnId}")
                .MapPost(RevokedUserInfoAsync, "revoked-user");

        }

        [SwaggerOperation(
            Summary = "Lưu thông tin định danh",
            Description = "Lưu trữ thông tin định danh bao gồm hình ảnh khuôn mặt và chữ ký")]
        //[HospitalClient]
        public async Task<Ok<BaseResponseModel<VNEIDIdentityInfo>>> SaveIdentityAsync([FromBody] SaveIdentity request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
            Summary = "Lấy thông tin định danh",
            Description = "Truy xuất thông tin định danh theo mã phiên (session ID)")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<GetIdentityRequest>>> GetIdentityAsync(string session, ISender mediator)
        {
            var res = await mediator.Send(new GetIdentityBySession { Session = session });
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
            Summary = "Khởi tạo giao dịch",
            Description = "Khởi tạo giao dịch xác thực với CCCD")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<InitTransactionResponseDto>>> InitTransactionAsync([FromBody] InitTransaction request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
            Summary = "Lấy thông tin giao dịch",
            Description = "Lấy thông tin giao dịch xác thực kèm hình ảnh khuôn mặt")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<UserShareInfoDataResponseDto>>> GetTransactionAsync([FromBody] GetTransaction request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
            Summary = "Yêu cầu chia sẻ thông tin người dùng",
            Description = "Xử lý yêu cầu chia sẻ thông tin người dùng từ hệ thống xác thực")]
        [AgentGWClient]
        public async Task<Ok<BaseResponseModel<UserSharedResponseDto>>> RequestUserSharedAsync( [FromBody] RequestUserShared request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
            Summary = "Lấy thông tin chia sẻ của người dùng",
            Description = "Truy xuất thông tin chia sẻ của người dùng theo mã giao dịch (Transaction ID)")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<UserShareInfoResponseDto>>> GetUserShareInfoAsync(string txnId, ISender mediator)
        {
            var res = await mediator.Send(new GetUserShareInfoByTxnId { TxnId = txnId });
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }



        [SwaggerOperation(
            Summary = "Yêu cầu thu hồi thông tin người dùng",
            Description = "Yêu cầu thu hồi thông tin người dùng")]
        [AgentGWClient]
        public async Task<Ok<BaseResponseModel<object>>> RevokedUserInfoAsync([FromBody] RevokedUserInfo request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data, res.ErrorType);
            return TypedResults.Ok(resConvert);
        }
    }
}

using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ICDLogic.Dtos;
using MediTrack.Application.Features.ICDLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class ICDEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/icds")
               .WithTags("Icd")
               .WithOpenApi()
               .RequireRateLimiting("fixed")
               .MapGet(GetICDList)
               .MapGet("/lower-level", GetLowerLevelDiagnosesList);
        }

        [SwaggerOperation(Summary = "Api lấy danh sách mã bệnh theo phân loại ICD-10")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<DiseaseCode>>>> GetICDList(IMediator mediator)
        {
            var res = await mediator.Send(new GetICDList());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy danh sách chuẩn đoán tuyến dưới")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<DiseaseCode>>>> GetLowerLevelDiagnosesList(IMediator mediator)
        {
            var res = await mediator.Send(new GetLowerLevelDiagnoses());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

    }
}
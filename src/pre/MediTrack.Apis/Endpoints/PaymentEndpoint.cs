﻿using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PaymentLogic.Commands;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class PaymentEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/payments")
               .WithTags("Payment")
               .WithOpenApi()
               .RequireRateLimiting("fixed")
               .MapGet(GetPaymentStatus, "{number}/status")
               .MapGet(GetPaymentDocument, "{number}/payment-document")
               .MapGet(GetPaymentDocumentJson, "{number}/payment-document-json")
               .MapPost(UpdatePaymentStatus, "ipn")
               .MapPost(UpdateParaclinicalPaymentStatus, "paraclinical-ipn")
               .MapPost(UpdateMembershipPaymentStatus, "membership-ipn")
               .MapPost(UpdateDonationPaymentStatus, "donation-ipn")
               .MapPost(CreateQrPayment, "qr")
               .MapPost(CreateParaclinicalQrPayment, "paraclinical-qr")
               .MapPost(CreateMembershipQrPayment, "membership-qr")
               .MapPost(CreateDonationQrPayment, "donation-qr")
               .MapPost(CreateQrPaymentTransfer, "qr-transfer");
        }

        [SwaggerOperation(Summary = "Api tạo yêu cầu thanh toán từ phiếu thu bằng QR",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<QrPaymentDto>>> CreateQrPayment(
            [FromBody] CreateQrPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo yêu cầu thanh toán từ phiếu thu bằng QR (cận lâm sàng)",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<QrPaymentDto>>> CreateParaclinicalQrPayment(
            [FromBody] CreateParaclinicalQrPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo yêu cầu thanh toán nap tiền thẻ thành viên bằng QR",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<QrPaymentDto>>> CreateMembershipQrPayment(
            [FromBody] CreateMembershipQrPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo yêu cầu thanh toán nap tiền thẻ thành viên bằng QR",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<QrPaymentDto>>> CreateDonationQrPayment(
            [FromBody] CreateDonationQrPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api truy vấn trạng thái của giao dịch thanh toán",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<PaymentStatusDto>>> GetPaymentStatus(
            string number, ISender mediator)
        {
            var req = new GetPaymentStatus() { Number = number };
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api cập nhật trạng thái thanh toán",
            Description = "Không có mô tả")]
        public async Task<Ok<BaseResponseModel<object>>> UpdatePaymentStatus(
            [FromBody] UpdateStatusOfPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<object>(result.Success, result.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api cập nhật trạng thái thanh toán (cận lâm sàng)",
            Description = "Không có mô tả")]
        public async Task<Ok<BaseResponseModel<object>>> UpdateParaclinicalPaymentStatus(
            [FromBody] UpdateStatusOfParaclinicalPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<object>(result.Success, result.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api cập nhật trạng thái thanh toán thẻ thành viên",
            Description = "Không có mô tả")]
        public async Task<Ok<BaseResponseModel<object>>> UpdateMembershipPaymentStatus(
            [FromBody] UpdateStatusOfMembershipPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<object>(result.Success, result.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api cập nhật trạng thái thanh toán thẻ thành viên",
            Description = "Không có mô tả")]
        public async Task<Ok<BaseResponseModel<object>>> UpdateDonationPaymentStatus(
            [FromBody] UpdateStatusOfDonationPayment req, ISender mediator)
        {
            var result = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<object>(result.Success, result.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy biên lai",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<PaymentDocumentDto>>> GetPaymentDocument(
            string number, ISender mediator)
        {
            var req = new GetPaymentDocument() { Number = number };
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy chuỗi json biên lai",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<string>>> GetPaymentDocumentJson(
            string number, ISender mediator)
        {
            var req = new GetPaymentJson() { Number = number };
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert(result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api tạo yêu cầu thanh toán từ phiếu thu bằng QR với luồng KH đã tạo/đã có tài khoản HDBank",
            Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<QrPaymentDto>>> CreateQrPaymentTransfer(
            [FromBody] CreateQrPaymentTransfer req, ISender mediator)
        {
            var result = await mediator.Send(req);

            var resConvert = ResponseConverter.Convert<QrPaymentDto>(
                result.Success, result.Messages, result.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}

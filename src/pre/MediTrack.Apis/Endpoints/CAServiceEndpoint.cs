using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CAServiceLogic.Commands;
using MediTrack.Application.Features.CAServiceLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class CAServiceEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/ca-service")
                .WithTags("CAService")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapPost(CreateCustomerAsync, "customers")
                .MapPost(CreateSignRequestAsync, "sign-requests")
                .MapPost(SignPdfAsync, "sign-pdf")
                .MapPost(FindCustomerByFaceAsync, "customers/find-by-face")
                .MapPost(GetCustomerPinAsync, "customers/pin")
                .MapGet(GetSignedPdfAsync, "signed-pdf/{id}");
        }

        [SwaggerOperation(
        Summary = "Create Customer in CA Service",
        Description = "Creates a new customer in the CA service with identity and face data")]
        public async Task<Ok<BaseResponseModel<object>>> CreateCustomerAsync([FromBody] CreateCustomerCA request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Create PDF Sign Request",
        Description = "Creates a new PDF signing request for a customer")]
        public async Task<Ok<BaseResponseModel<object>>> CreateSignRequestAsync([FromBody] CreateSignRequestCA request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Sign PDF Document",
        Description = "Signs a PDF document with digital signature")]
        public async Task<Ok<BaseResponseModel<object>>> SignPdfAsync([FromBody] SignPdfCA request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Find Customer by Face Recognition",
        Description = "Finds customers using facial recognition technology")]
        public async Task<Ok<BaseResponseModel<object>>> FindCustomerByFaceAsync([FromBody] FindCustomerByFaceCA request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Get Customer PIN",
        Description = "Retrieves customer PIN using face verification")]
        public async Task<Ok<BaseResponseModel<object>>> GetCustomerPinAsync([FromBody] GetCustomerPinCA request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Get Signed PDF Document",
        Description = "Retrieves a signed PDF document by ID")]
        public async Task<Ok<BaseResponseModel<object>>> GetSignedPdfAsync(Guid id, ISender mediator)
        {
            var res = await mediator.Send(new GetSignedPdfCA() { Id = id });
            var resConvert = ResponseConverter.Convert<object>(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}

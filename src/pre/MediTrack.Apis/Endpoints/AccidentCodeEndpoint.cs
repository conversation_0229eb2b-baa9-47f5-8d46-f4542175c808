using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class AccidentCodeEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/accident-codes")
                .WithTags("AccidentCodes")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapGet(GetAccidentCodes);
        }

        [SwaggerOperation(Summary = "Api lấy ra danh sách mã tai nạn")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<GetAccidentCodeDto>>>> GetAccidentCodes(IMediator mediator)
        {
            var req = new GetAccidentCode();
            var res = await mediator.Send(req);
            var resConvert = ResponseConverter.Convert<IEnumerable<GetAccidentCodeDto>>(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}
using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HealthPackageLogic.Queries;
using MediTrack.Domain.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class HealthPackageEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/health-packages")
                .WithTags("Health Packages")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapGet(GetHealthPackages, "all")
                .MapGet(GetHealthPackageTypes, "types")
                .MapGet(GetTechnicalServices, "technical-services")
                .MapGet(GetFilter, "filter");
        }

        [SwaggerOperation(
        Summary = "Api lấy ra danh sách loại gói kh<PERSON>m chữa bệnh",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<HealthPackageType>>>> GetHealthPackageTypes(
            IMediator mediator)
        {
            var res = await mediator.Send(new GetHealthPackageTypes());
            var resConvert = ResponseConverter.Convert<IEnumerable<HealthPackageType>>(
                res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Api lấy ra danh sách gói khám chữa bệnh",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<HealthPackage>>>> GetHealthPackages([FromQuery] string? healthPackageTypeId,
            IMediator mediator)
        {
            var res = await mediator.Send(new GetHealthPackages() { HealthPackageTypeId = healthPackageTypeId});
            var resConvert = ResponseConverter.Convert<IEnumerable<HealthPackage>>(
                res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Api lấy ra filter gói khám chữa bệnh",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<string>>>> GetFilter( [FromQuery] string code,
            IMediator mediator)
        {
            var res = await mediator.Send(new GetHealthPackageFilter() { code = code });
            var resConvert = ResponseConverter.Convert<IEnumerable<string>>(
                res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Api lấy ra đơn giá gói khám chữa bệnh",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<IEnumerable<TechnicalService>>>> GetTechnicalServices(
            IMediator mediator)
        {
            var res = await mediator.Send(new GetTechnicalServices());
            var resConvert = ResponseConverter.Convert<IEnumerable<TechnicalService>>(
                res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}
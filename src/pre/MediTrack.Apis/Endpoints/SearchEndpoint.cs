using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Queries;
using Microsoft.AspNetCore.Http.HttpResults;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class SearchEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/search")
                .WithTags("Search")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapGet(SearchHealthServices, "health-services");
        }
        [SwaggerOperation(Summary = "Api tìm kiếm dịch vụ y tế",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<HealthServiceSearchDto>>>> SearchHealthServices(ISender mediator)
    {
        var res = await mediator.Send(new SearchHealthService());

        var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
        return TypedResults.Ok(resConvert);
    }
    }

    
}
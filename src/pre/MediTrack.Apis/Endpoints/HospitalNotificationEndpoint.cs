

using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.NotificationLogic.Queries;
using MediTrack.Domain.Domain;
using Microsoft.AspNetCore.Http.HttpResults;
using Swashbuckle.AspNetCore.Annotations;

namespace MediTrack.Apis.Endpoints
{
    public class HospitalNotificationEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/hospital-notifications")
                .WithTags("HospitalNotifications")
                .WithOpenApi()
                .RequireRateLimiting("fixed")
                .MapGet(GetNotification);
        }

        [SwaggerOperation(Summary = "Api lấy list thông báo cho kiosk")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<Dictionary<int, string>>>> GetNotification(IMediator mediator)
        {
            var res = await mediator.Send(new GetNotifications());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}
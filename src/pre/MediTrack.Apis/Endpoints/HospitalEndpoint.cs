using System.Text.Json;
using MediatR;
using MediTrack.Apis.Extensions;
using MediTrack.Application.Attributes;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HospitalLogic.Commands;
using MediTrack.Application.Features.HospitalLogic.Dtos;
using MediTrack.Application.Features.HospitalLogic.Queries;
using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;
using MediTrack.Application.Features.HospitalMetadataLogic.Queries;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;


namespace MediTrack.Apis.Endpoints
{
    public class HospitalEndpoint : BaseEndpoint
    {
        public override void Map(WebApplication app)
        {
            app.MapGroup("/api/hospitals")
               .WithTags("Hospital")
               .WithOpenApi()
               .RequireRateLimiting("fixed")
               .MapGet(GetHospitalAsync, "{id}")
               .MapGet(GetAdvanceMoney, "{id}/advance-money")
               .MapPost(ClearHospitalCache, "clear-cache")
               .MapPost(ConfigHospitalByKey, "config-key")
               .MapPost(SetHospitalId, "set-hospital-id")
               .MapGet(GetTransferReferralHospitals, "transfer-referral-hospitals")
               .MapGet(GetReasons, "reasons")
               .MapGet(GetExameCategory, "exame-categories")
               .MapGet(GetQueuePriorityData, "queue-priority-data")
               .MapGet(GetHospitalMap, "hospital-map");
        }

        [Microsoft.AspNetCore.Authorization.Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "OPERATOR")]
        [SwaggerOperation(Summary = "Cập nhật HospitalId cho kiosk được phép", Description = "Yêu cầu HospitalId và KioskId trong body. Chỉ cho phép các KioskId đã được định nghĩa trong danh sách cho phép.")]
        public async Task<Ok<BaseResponseModel<object>>> SetHospitalId([FromBody] SetHospitalId request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(res.Success, res.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [Microsoft.AspNetCore.Authorization.Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "OPERATOR")]
        [SwaggerOperation(Summary = "Cập nhật giá trị cho nhiều field cấu hình bệnh viện", Description = "Yêu cầu HospitalId và Dictionary<string, string> Configs. Validate tất cả configs trước khi update, nếu có lỗi sẽ trả về tất cả lỗi và không update config nào cả.")]
        public async Task<Ok<BaseResponseModel<object>>> ConfigHospitalByKey([FromBody] ConfigHospitalByKey request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(res.Success, res.Messages, null);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(
        Summary = "Api chi tiết bệnh viện",
        Description = "Không có mô tả")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<HospitalDto>>> GetHospitalAsync(string id, ISender mediator)
        {
            var res = await mediator.Send(new GetHospital { Id = id });
            var resConvert = ResponseConverter.Convert(
               res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy thông tin tiền tạm ứng")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<string[]>>> GetAdvanceMoney(string id, ISender mediator)
        {
            var res = await mediator.Send(new GetAdvanceMonney { HospitalId = id });
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api xóa cache hospital")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<object>>> ClearHospitalCache([FromBody] ClearHospitalCache request, ISender mediator)
        {
            var res = await mediator.Send(request);
            var resConvert = ResponseConverter.Convert<object>(res.Success, res.Messages, null);
            return TypedResults.Ok(resConvert);
        }
        [SwaggerOperation(Summary = "Api lấy lý do vào khám")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<HospitalMetadataReason>>>> GetReasons(ISender mediator)
        {
            var res = await mediator.Send(new GetHospitalMetadataRequest { Key = "recommended_reasons" });

            var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

            var data = res.Data is JsonElement jsonElement
                ? jsonElement.Deserialize<List<HospitalMetadataReason>>(options) ?? []
                : [];

            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy các loại khám theo bệnh viện")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<HospitalMetadataExameCategory>>>> GetExameCategory(ISender mediator)
        {
            var res = await mediator.Send(new GetExameCategories());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy dữ liệu ưu tiên STT")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<HospitalQueuePriorityData>>> GetQueuePriorityData(ISender mediator)
        {
            var res = await mediator.Send(new GetQueuePriorityDataRequest());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy danh sách bệnh viện chuyển tuyến")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<List<TransferReferralHospital>>>> GetTransferReferralHospitals(IMediator mediator)
        {
            var res = await mediator.Send(new GetTransferReferralHospitals());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }

        [SwaggerOperation(Summary = "Api lấy bản đồ bệnh viện")]
        [HospitalClient]
        public async Task<Ok<BaseResponseModel<string>>> GetHospitalMap(ISender mediator)
        {
            var res = await mediator.Send(new GetHospitalMapRequest());
            var resConvert = ResponseConverter.Convert(res.Success, res.Messages, res.Data);
            return TypedResults.Ok(resConvert);
        }
    }
}

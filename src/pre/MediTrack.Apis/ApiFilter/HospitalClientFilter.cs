using MediTrack.Application.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace MediTrack.Apis.ApiFilter
{
    public class HospitalClientFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation,
            OperationFilterContext context)
        {
            if (context.ApiDescription.ActionDescriptor.EndpointMetadata
                .Any(em => em is HospitalClientAttribute))
            {
                operation.Parameters ??= [];

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-kiosk-id") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-kiosk-id",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Id máy Kiosk"
                    });
                }

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-hospital-id") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-hospital-id",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Id bệnh viện"
                    });
                }

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-device-id") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-device-id",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Id loại thiếu bị truy cập: `mobile`/`kiosk`/`admin`"
                    });
                }

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-client-secret") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-client-secret",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Chữ ký được tạo mỗi khi thực hiện request"
                    });
                }

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-request-time") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-request-time",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Thời gian thực hiện request này theo epoch time"
                    });
                }

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-trace-id") is null)
                {
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-trace-id",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Id dùng để trace của request"
                    });
                }
            }
        }
    }
}

﻿using MediTrack.Application.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace MediTrack.Apis.ApiFilter
{
    public class AgentGWClientFilter  : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (context.ApiDescription.ActionDescriptor.EndpointMetadata
               .Any(em => em is AgentGWClientAttribute))
            {
                operation.Parameters ??= [];

                if (operation.Parameters
                    .FirstOrDefault(x => x.Name == "x-api-key") is null)
                {
                  
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "x-api-key",
                        In = ParameterLocation.Header,
                        Required = false,
                        Description = "Key agent GW integration"
                    });
                }
                
            }    
        }
    }
}

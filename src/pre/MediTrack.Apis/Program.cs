using MediTrack.Apis;
using MediTrack.Apis.ApiFilter;
using MediTrack.Apis.Config;
using MediTrack.Apis.Middlewares;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Persistence;
using MediTrack.Persistence.Init;
using MediTrack.Persistence.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Formatting.Json;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.RateLimiting;
using MediTrack.Application;
using MediTrack.Integrated;
using MediTrack.Ultils.Models;
using Newtonsoft.Json;
using System.Globalization;
using Hangfire;
using MediTrack.Application.Schedules;
using Hangfire.Dashboard;


var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddHttpClient();
builder.Services.AddMemoryCache();
builder.Services.Configure<JsonSerializerSettings>(options =>
{
    options.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
});


builder.Services.AddLogging();
builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
    options.SerializerOptions.WriteIndented = true;
});
builder.Services.AddSwaggerGen(opt =>
{
    opt.EnableAnnotations();
    opt.SwaggerDoc("v1", new OpenApiInfo { Title = "MediTrack Api", Version = "v1" });
    opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "Please enter token",
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        BearerFormat = "JWT",
        Scheme = "bearer"
    });
    opt.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type=ReferenceType.SecurityScheme,
                    Id="Bearer"
                }
            },
            Array.Empty<string>()
        },
    });

    opt.OperationFilter<RestrictClientFilter>();
    opt.OperationFilter<RequireSignHashClientFilter>();
    opt.OperationFilter<HospitalClientFilter>();
    opt.OperationFilter<AgentGWClientFilter>();
});

builder.Configuration.AddEnvironmentVariables()
    .Build();

builder.Services.AddIdentity<User, Role>()
    .AddEntityFrameworkStores<DatabaseService>()
    .AddDefaultTokenProviders()
    .AddSignInManager();
builder.Services.Configure<IdentityOptions>(options =>
{
    options.Password.RequireDigit = false;
    options.Password.RequiredLength = 1;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireLowercase = false;
});

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = false,
            ValidateAudience = false,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Token:Issuer"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Token:SigningKey"] ?? string.Empty))
        };
    });
builder.Services.AddAuthorization();

builder.Services.AddRateLimiter(o =>
{
    o.AddPolicy("fixed", c =>
    {
        return RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: c.Connection.RemoteIpAddress?.ToString(),
            factory: _ => new FixedWindowRateLimiterOptions
            {
                PermitLimit = 100,
                Window = TimeSpan.FromSeconds(60),
                QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                QueueLimit = 10
            });
    });
    o.AddPolicy("sliding", c =>
    {
        return RateLimitPartition.GetSlidingWindowLimiter(
            partitionKey: c.Connection.RemoteIpAddress?.ToString(),
            factory: _ => new SlidingWindowRateLimiterOptions
            {
                PermitLimit = 100,
                Window = TimeSpan.FromSeconds(60),
                SegmentsPerWindow = 4,
                QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                QueueLimit = 10
            });
    });
    o.OnRejected = (context, _) =>
    {
        var res = new BaseResponseModel<object>
        {
            Code = ErrorConstant.TOO_MANY_REQUEST,
            Message = "Calm down, don't call too many requests!!!"
        };

        context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
        context.HttpContext.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(res), cancellationToken: _);

        return new ValueTask();
    };
});

builder.Services.AddScoped<List<LogStep>>();
builder.Services.AddPaymentConfig(builder.Configuration);
builder.Services.AddEnvironmentConfig(builder.Configuration);

builder.Services.AddApplicationServices(builder.Configuration);
builder.Services.AddInfraPersistenceServices(builder.Configuration);
builder.Services.AddIntegratedServices(builder.Configuration);
builder.Services.AddPresentationServices();
builder.Services.AddHangfireServices(builder.Configuration);

builder.Host.UseSerilog((context, config) =>
{
    config.WriteTo.Console().MinimumLevel.Information();
    config.WriteTo.File(path: AppDomain.CurrentDomain.BaseDirectory + "/logs/app-log-.txt",
        rollingInterval: RollingInterval.Day,
        rollOnFileSizeLimit: true,
        fileSizeLimitBytes: 100000000, // 100MB
        formatter: new JsonFormatter()).MinimumLevel.Information();
});

builder.Services
    .Configure<TokenConfig>(builder.Configuration
    .GetSection(TokenConfig.ConfigName));
builder.Services
    .Configure<MailConfig>(builder.Configuration
    .GetSection(MailConfig.ConfigName));
builder.Services
    .Configure<AppConfig>(builder.Configuration
    .GetSection(AppConfig.ConfigName));
builder.Services
    .Configure<ConnectionStringConfig>(builder.Configuration
    .GetSection(ConnectionStringConfig.ConfigName));

builder.Services.AddCors(options =>
{
    options.AddPolicy(name: "CorsPolicy",
                      policy =>
                      {
                          policy.AllowAnyOrigin();
                          policy.AllowAnyHeader();
                          policy.AllowAnyMethod();
                      });
});

var app = builder.Build();
app.UseRateLimiter();

var pathBase = Environment
    .GetEnvironmentVariable("PATH_BASE");
if (!string.IsNullOrEmpty(pathBase))
{
    app.UsePathBase($"/{pathBase}");
}

var isSeed = Environment
    .GetEnvironmentVariable("INIT_DATABASE")?.ToUpper() == "TRUE";
if (isSeed)
{
    await app.InitialiseDatabaseAsync();
}

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("CorsPolicy");

// Tạm thời dừng logstep do dung lượng log của api xác thực quá lớn
// app.UseMiddleware<LoggingMiddleware>();

app.UseMiddleware<ValidateSignatureMiddleware>();
app.UseMiddleware<ValidateClientMiddleware>();

app.UseAuthentication();
app.UseAuthorization();
app.MapEndpoints();

app.UseHangfireDashboard("/medipay/jobs", new DashboardOptions
{
    IsReadOnlyFunc = dashboardContext =>
    {
        var context = dashboardContext.GetHttpContext();
        return !context.User.IsInRole("Admin");
    }
});

using var scope = app.Services.CreateScope();
var jobManager = scope.ServiceProvider.GetRequiredService<IRecurringJobManager>();
jobManager.AddOrUpdate<IUpdateBookingStatusJob>(
    "UpdateBookingStatusJob",
    x => x.ExecuteAsync(),
    "30 17 * * *",
    new RecurringJobOptions
    {
        TimeZone = TimeZoneInfo.Utc,
    });

var cultureInfo = new CultureInfo("vi-VN");
CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

app.Run();

﻿using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;

namespace MediTrack.Domain.Helpers
{
    public static class CustomerHelper
    {
        public static (string firstName, string lastName) GetVietnameseNameParts(this string fullName)
        {
            if (string.IsNullOrWhiteSpace(fullName))
            {
                throw new ArgumentException("Tên không được để trống.", nameof(fullName));
            }

            // Split the full name into parts by space
            string[] nameParts = fullName.Trim().Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            // If there's only one part, consider it as the last name
            if (nameParts.Length == 1)
            {
                return (string.Empty, nameParts[0]);
            }

            // The last part is always the last name
            string lastName = nameParts[^1];

            // The remaining parts are the first name
            string[] firstNameParts = new string[nameParts.Length - 1];
            Array.Copy(nameParts, 0, firstNameParts, 0, nameParts.Length - 1);
            string firstName = string.Join(" ", firstNameParts);

            return (firstName, lastName);
        }

        public static string GetAge(this DateTime? dateOfBirth)
        {
            if (dateOfBirth == null)
            {
                return string.Empty;
            }

            DateTime now = DateTimeHelper.GetCurrentLocalDateTime();
            int age = now.Year - dateOfBirth.Value.Year;
            if (now.Month < dateOfBirth.Value.Month || (now.Month == dateOfBirth.Value.Month && now.Day < dateOfBirth.Value.Day))
            {
                age--;
            }

            return age.ToString();
        }
        
        #region support
        public static string GetFullName(this Customer customer)
        {
            return $"{customer.FirstName} {customer.LastName}".Trim();
        }

        public static string GetMidAndLastName(this Customer customer)
        {
            var names = customer.FirstName?.Split(' ');
            if (names == null || names.Length == 0)
            {
                return $"{customer.LastName}";
            }
            return $"{names.Last()} {customer.LastName}";
        }
        #endregion
    }
}

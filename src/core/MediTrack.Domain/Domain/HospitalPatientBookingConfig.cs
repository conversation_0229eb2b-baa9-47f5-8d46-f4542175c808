using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin config đặt khám tại bệnh viện
    /// </summary>
    public class HospitalPatientBookingConfig : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public int? DayOfWeek { get; set; }
        public string? MorningTimeZone { get; set; }
        public string? AfternoonTimeZone { get; set; }
        public int? MaxBooking { get; set; }

        public string? ClinicId { get; set; }
        public string? ClinicName { get; set; }

        public string? HealthServiceId { get; set; }
        public string? HealthServiceName { get; set; }

        public virtual Hospital? Hospital { get; set; }
        public string HospitalId { get; set; } = string.Empty;
    }
}

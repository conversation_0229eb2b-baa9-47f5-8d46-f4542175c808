﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin khách hàng, chứa kèm mã bệnh nhân
    /// </summary>
    public class Customer : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string? Image { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string? IdentityNo { get; set; } = string.Empty;
        public string? IdentityIssuePlace { get; set; }
        public DateTime? IdentityIssueDate { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        /// <summary>
        /// Thường trú
        /// </summary>
        public string? PlaceOfResidence { get; set; }
        public string? PlaceOfOrigin { get; set; }
        public string? Sex { get; set; }
        public string? NationalityId { get; set; }
        // public string? PatientCode { get; set; }

        public string? Address { get; set; }

        /// <summary>
        /// Địa chỉ theo tỉnh huyện, xã
        /// </summary>
        public string? ProvinceId { get; set; }
        public string? DistrictId { get; set; }
        public string? WardId { get; set; }
        public string? Nation { get; set; }
        /// <summary>
        /// Số nhà, tên đường, ấp,...
        /// </summary>
        public string? Street { get; set; }

        /// <summary>
        /// Bảo hiểm y tế và nơi đăng ký khám chữa bệnh
        /// </summary>
        public string? HealthInsuranceNo { get; set; }
        public string? HealthInsurancePlace { get; set; }
        public string? HealthInsurancePlaceId { get; set; }
        public string? HealthInsuranceFromDate { get; set; }
        public string? HealthInsuranceExpiredDate { get; set; }

        /// <summary>
        /// Nghề nghiệp
        /// </summary>
        public string? CareerId { get; set; }

        /// <summary>
        /// Trình độ văn hóa
        /// </summary>
        public string? EducationLevel { get; set; }

        /// <summary>
        /// Nơi làm việc
        /// </summary>
        public string? WorkPlace { get; set; }

        /// <summary>
        /// Địa chỉ nơi làm việc
        /// </summary>
        public string? WorkAddress { get; set; }

        /// <summary>
        /// Chức vụ
        /// </summary>
        public string? PositionId { get; set; }

        /// <summary>
        /// S0: Chưa có tài khoản medipay
        /// S01: Có tài khoản medipay nhưng chưa xác thực căn cước công dân (Luồng người thân)
        /// S1: Đã có tài khoản medipay chưa có HDBank
        /// S2: Đã có tài khoản medipay và HDBank
        /// </summary>
        public string? AccountStatus { get; set; }

        public string? BankTranId { get; set; } = string.Empty;
        public string? Phone { get; set; }

        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public string? HospitalId { get; set; } = string.Empty;

        /// <summary>
        /// Mã xác thực eID
        /// </summary>
        public string? EIDTransactionId { get; set; } = string.Empty;

        /// <summary>
        /// Mã xác thực Face Matching
        /// </summary>
        public string? FaceMatchingTransactionId { get; set; } = string.Empty;

        /// <summary>
        /// Mã xác thực Liveness
        /// </summary>
        public string? LivenessTransactionId { get; set; } = string.Empty;

        /// <summary>
        /// 0: CCCD, 1: VNeID, other values reserved for future sources.
        /// </summary>
        public int RegistrationType { get; set; } = 0;

        public bool IsSyncedTwoLevelAddress { get; set; } = false;
        public virtual User? User { get; set; }
        public virtual List<CustomerHospital>? CustomerHospitals { get; set; } = [];
    }
}

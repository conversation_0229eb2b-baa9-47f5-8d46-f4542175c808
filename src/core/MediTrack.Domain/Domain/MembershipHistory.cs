using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin lịch sử thao tác của thẻ thành viên khách hàng
    /// </summary>
    public class MembershipHistory : BaseAudit
    {
        /// <summary>
        /// Đ<PERSON>nh danh duy nhất của lịch sử
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Định danh duy nhất của thành viên
        /// </summary>
        public string MembershipId { get; set; } = string.Empty;

        /// <summary>
        /// Mã thành viên
        /// </summary>
        public string MembershipCode { get; set; } = string.Empty;

        /// <summary>
        /// Số dư trước thay đổi
        /// </summary>
        public decimal? OldBalance { get; set; }
        /// <summary>
        /// Số dư sau thay đổi
        /// </summary>
        public decimal? NewBalance { get; set; }

        /// <summary>
        /// S<PERSON> lượt dùng trước thay đổi
        /// </summary>
        public int? OldUsageCount { get; set; }
        /// <summary>
        /// Số lượt dùng sau thay đổi
        /// </summary>
        public int? NewUsageCount { get; set; }
        /// <summary>
        /// Ghi chú
        /// </summary>
        public string? Description { get; set; }
        /// <summary>
        /// USED: sử dụng
        /// TOP_UP: nạp tiền
        /// </summary>
        public string? TransactionType { get; set; }  

        public string? RegisterNumber { get; set; }
        public string? ReceiptNumber { get; set; }
        public string? RefNo { get; set; }     
        public string CustomerId { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        
        public virtual Register? Register { get; set; }
        public virtual Receipt? Receipt { get; set; } 
        public virtual Customer Customer { get; set; } = default!;
        public virtual Hospital Hospital { get; set; } = default!;
    } 
}
﻿using MediTrack.Domain.Bases;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Domain.Domain
{
    public class UserShareInfo : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Id của session share thông tin của vneid 
        /// </summary>
        public string? TxnId { get; set; } 
        public string? IndentityNo { get; set; }

        /// <summary>
        /// 0 - đang đợi chia sẻ 
        /// 1 - đồng ý chia sẻ 
        /// 2 - không đồng ý chia sẻ
        /// 3- thu hồi chia sẻ
        /// </summary>
        public string? result { get; set; }
        public DateTime? ConsentGivenAt { get; set; }
        public DateTime? ConsentRevokedAt { get; set; }

    }
}
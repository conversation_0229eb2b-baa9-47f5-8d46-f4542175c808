﻿
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON><PERSON>ch sử quyên góp
    /// </summary>
    public class DonationHistory : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string CampaignId { get; set; } = string.Empty;
        public virtual DonationCampaign DonationCampaign { get; set; } = null!;
        public string FullName { get; set; } = string.Empty;
        public string? Message { get; set; }
        public string? UserId { get; set; }
        public virtual User User { get; set; } = null!;
        public decimal Amount { get; set; }
        public string ReceiptNumber { get; set; } = string.Empty;
        public virtual Receipt Receipt { get; set; } = null!;
        public string PaymentStatus { get; set; } = string.Empty;
        public bool IsAnonymous { get; set; }
    }
}
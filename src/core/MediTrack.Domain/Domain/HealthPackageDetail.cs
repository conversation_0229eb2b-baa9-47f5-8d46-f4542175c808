using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Bảng liên kết giữa gói dịch vụ và dịch vụ y tế (quan hệ nhiều-nhiều)
    /// </summary>
    public class HealthPackageDetail : BaseAudit
    {
        /// <summary>
        /// Đ<PERSON>nh danh duy nhất
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// ID của gói dịch vụ
        /// </summary>
        public string PackageServiceId { get; set; } = string.Empty;

        /// <summary>
        /// ID của dịch vụ y tế
        /// </summary>
        public string HealthServiceId { get; set; } = string.Empty;

        /// <summary>
        /// Số lượt dùng
        /// </summary>
        public int? UsageCount { get; set; }

        /// <summary>
        /// Khuyến mãi khi mua dịch vụ
        /// </summary>
        public decimal DiscountValue { get; set; } = 0;

        /// <summary>
        /// Đ<PERSON>n vị của <PERSON>ến mãi khi mua dịch vụ (%, số tiền,...)
        /// </summary>
        public string? DiscountUnit { get; set; }


        /// <summary>
        /// Tham chiếu đến gói dịch vụ
        /// </summary>
        public virtual PackageService PackageService { get; set; } = null!;

        /// <summary>
        /// Tham chiếu đến dịch vụ y tế
        /// </summary>
        public virtual HealthService HealthService { get; set; } = null!;
    }
}

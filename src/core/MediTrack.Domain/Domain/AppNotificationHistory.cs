using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class AppNotificationHistory : BaseAudit
    {
        public string Id { get; set; } = default!;
        public string UserId { get; set; } = default!;
        /// <summary>
        /// "reminder", "transaction", "promotion", "system"
        /// </summary>
        public string Type { get; set; } = default!;
        /// <summary>
        /// "victoria_request"
        /// </summary>
        public string SubType { get; set; } = default!;

        public string Title { get; set; } = default!;
        public string? Message { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<string, string>? Metas { get; set; } 

        public DateTime SentAt { get; set; }
        public DateTime? ReadAt { get; set; }
    }
}

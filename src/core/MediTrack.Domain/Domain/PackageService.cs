using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin gói dịch vụ
    /// </summary>
    public class PackageService : BaseAudit
    {
        /// <summary>
        /// Đ<PERSON>nh danh duy nhất của gói dịch vụ
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Hình ảnh (nếu có)
        /// </summary>
        public string? Image { get; set; }

        /// <summary>
        /// Tên gói
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Đơn vị (vd: Gói)
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// Giá gốc
        /// </summary>
        public decimal OriginalPrice { get; set; } = 0;

        /// <summary>
        /// Giá khuyến mãi
        /// </summary>
        public decimal PromotionPrice { get; set; } = 0;

        /// <summary>
        /// Trạng thái gói (vd: đ<PERSON>, đ<PERSON> du<PERSON>, từ chối)
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Loại gói (0: Gói quản lý theo số lần dùng; 1: Gói quản lý theo số dư)
        /// </summary>
        public int PackageType { get; set; } = 0;

        /// <summary>
        /// Số lượt dùng
        /// </summary>
        public int? UsageCount { get; set; }

        /// <summary>
        /// Mô tả gói
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Giới tính (Nam/Nữ/Tất cả)
        /// </summary>
        public string? Gender { get; set; }

        /// <summary>
        /// Độ tuổi tối thiểu
        /// </summary>
        public int? FromAge { get; set; }

        /// <summary>
        /// Độ tuổi tối đa 
        /// </summary>
        public int? ToAge { get; set; }

        /// <summary>
        /// Hạn dùng (số ngày)
        /// </summary>
        public int? ExpirationDays { get; set; }

        /// <summary>
        /// Tiền tố của mã thành viên
        /// </summary>
        public string? MembershipPrefix { get; set; }

        /// <summary>
        /// Hậu tố của mã thành viên
        /// </summary>
        public string? MembershipSuffix { get; set; }

        /// <summary>
        /// Khuyến mãi khi mua dịch vụ
        /// </summary>
        public decimal DiscountValue { get; set; } = 0;

        /// <summary>
        /// Đơn vị của Khuyến mãi khi mua dịch vụ (%, số tiền,...)
        /// </summary>
        public string? DiscountUnit { get; set; }

        /// <summary>
        /// Số dư
        /// </summary>
        public decimal Balance { get; set; } = 0;

        /// <summary>
        /// Số dư duy trì tối thiểu
        /// </summary>
        public decimal MinimumBalance { get; set; } = 0;

        /// <summary>
        /// Thứ tự hiển thị
        /// </summary>
        public int SortIndex { get; set; } = 0;

        /// <summary>
        /// Danh sách dịch vụ y tế trong gói
        /// </summary>
        public virtual List<HealthPackageDetail> HealthPackageDetails { get; set; } = [];
        public List<string>? Tags { get; set; } = [];
        public string? HospitalId { get; set; }
        public virtual Hospital? Hospital { get; set; }
    }
}

using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class HospitalNotification: BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;
        /// <summary>
        /// Json Config của HIS, dùng để call api HIS
        /// </summary>
        [Column(TypeName = "jsonb")]
        public List<NotificationConfig>? NotiConfig { get; set; }
        public string Value { get; set; } = string.Empty;

        public virtual Hospital? Hospital { get; set; }
    }

    public class NotificationConfig
    {
        public int DayOfWeek { get; set; }
    }
}
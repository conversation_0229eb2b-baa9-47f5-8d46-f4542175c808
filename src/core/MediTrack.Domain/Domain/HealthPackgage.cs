using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MediTrack.Domain.Domain
{
    public class HealthPackage
    {
        /// <summary>
        /// Id gói khám sức khỏe
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Mã gói khám sức khỏe
        /// </summary>
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// Tên gói khám sức khỏe
        /// </summary>
        public string? Name { get; set; } = string.Empty;
        /// <summary>
        /// Id loại gói khám sức khỏe
        /// </summary>
        public string? HealthPackageTypeId { get; set; } = string.Empty;
        /// <summary>
        /// Đơn giá gói khám sức khỏe (VNĐ)
        /// </summary>
        public decimal UnitPrice { get; set; }
        /// <summary>
        /// Đơn giá gói khám sức khỏe (VNĐ) hiển thị
        /// </summary>
        public string? UnitPriceDisplay { get; set; }
        /// <summary>
        /// <PERSON><PERSON> tả gói khám sức khỏe
        /// </summary>
        public string? Description { get; set; } = string.Empty;
        /// <summary>
        /// Sort index
        /// </summary>
        public int? SortIndex { get; set; }
        /// <summary>
        /// Độ tuổi khám tối thiểu)
        /// </summary>
        public string? AgeRange { get; set; }
        /// <summary>
        /// Đối tượng sử dụng (1: Nam, 2: Nữ, 3: Cả 2, ...)
        /// </summary>
        public string? UserType { get; set; }
        /// <summary>
        /// Hạn sử dụng gói khám sức khỏe
        /// / </summary>
        public string? ExpirationDate { get; set; }
        /// <summary>
        /// Số lượt sử dụng tối đa
        /// </summary>
        public string? UserCount { get; set; }

        /// <summary>
        /// Danh sách dịch vụ khám, chữa bệnh theo gói khám sức khỏe
        /// </summary>
        public List<HealthPackageService> HealthPackageServices { get; set; } = new List<HealthPackageService>();


    }
}
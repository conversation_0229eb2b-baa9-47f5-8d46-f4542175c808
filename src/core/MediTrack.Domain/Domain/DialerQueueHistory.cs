﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// L<PERSON>ch sử số thứ tự khám bệnh
    /// </summary>
    public class DialerQueueHistory : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// CCCD
        /// </summary>
        public string? IdentityNo { get; set; }
        /// <summary>
        /// <PERSON><PERSON>y lấy số thứ tự
        /// </summary>
        public DateTime QueueDate { get; set; } = DateTime.UtcNow;
        /// <summary>
        /// <PERSON><PERSON> thứ tự
        /// </summary>
        public int QueueNumber { get; set; }
        public bool UuTien { get; set; }
        public string HealthServiceId { get; set; } = string.Empty;
        public string DialerQueueId { get; set; } = string.Empty;
        /// <summary>
        /// Mã bệnh viện
        /// </summary>
        public string HospitalId { get; set; } = string.Empty;

        public virtual DialerQueue? DialerQueue { get; set; }
        public virtual Hospital? Hospital { get; set; }
    }
}

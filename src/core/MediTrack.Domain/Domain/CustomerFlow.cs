﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin luồng khách hàng, ch<PERSON><PERSON> kèm mã bệnh nhân
    /// Dùng để quản lý luồng khách hàng từ lúc đăng ký đến khi có tài khoản ngân hàng
    /// </summary>
    public class CustomerFlow : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string? Status { get; set; }
        public string? Logs { get; set; }
    }
}

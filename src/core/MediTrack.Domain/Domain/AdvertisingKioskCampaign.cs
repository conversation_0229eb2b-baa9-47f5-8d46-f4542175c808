using System;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Bảng liên kết giữa Kiosk và chiến dịch quảng cáo (quan hệ nhiều-nhiều)
    /// </summary>
    public class AdvertisingKioskCampaign : BaseAudit
    {
        /// <summary>
        /// Định danh duy nhất cho bản ghi kiosk trong chiến dịch
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// ID của Kiosk
        /// </summary>
        public string KioskId { get; set; } = string.Empty;

        /// <summary>
        /// Tham chiếu đến <PERSON>k
        /// </summary>
        public virtual Kiosk Kiosk { get; set; } = null!;

        /// <summary>
        /// ID của chiến dịch quảng cáo
        /// </summary>
        public string CampaignId { get; set; } = string.Empty;

        /// <summary>
        /// Tham chiếu đến chiến dịch quảng cáo
        /// </summary>
        public virtual AdvertisingCampaign Campaign { get; set; } = null!;
    }
}
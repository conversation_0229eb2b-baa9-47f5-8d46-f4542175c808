﻿
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON><PERSON>n dịch quyên góp
    /// </summary>
    public class DonationCampaign : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalDonators { get; set; }
        public string Status { get; set; } = string.Empty; // e.g., "Active", "Completed", "Cancelled"
        public decimal TargetAmount { get; set; }
        public decimal CurrentAmount { get; set; }
        public List<string> ImageUrls { get; set; } = [];
        public string DonationCompaignTypeId { get; set; } = string.Empty;
        public string? HospitalId { get; set; }
        public virtual Hospital? Hospital { get; set; }
        public virtual DonationCampaignType? DonationCampaignType { get; set; }
        public virtual ICollection<DonationHistory> DonationHistories { get; set; } = [];
        public virtual ICollection<DonationFollower> DonationFollowers { get; set; } = [];
    }
}

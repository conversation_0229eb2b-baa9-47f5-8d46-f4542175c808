using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin số lượt đặt khám của bệnh viện
    /// </summary>
    public class HospitalPatientBooking : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public int? DayOfWeek { get; set; }
        public int? MaxBooking { get; set; }        
        public int? CurrentBooking { get; set; } 
        
        public DateTime? DateBooking { get; set; }

        public string? ClinicId { get; set; }
        public string? ClinicName { get; set; }

        public string? HealthServiceId { get; set; }
        public string? HealthServiceName { get; set; }

        public virtual Hospital? Hospital { get; set; }
        public string HospitalId { get; set; } = string.Empty;
    }
}

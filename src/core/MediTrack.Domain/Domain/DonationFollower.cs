﻿
namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON> góp
    /// </summary>
    public class DonationFollower
    {
        public string DonationCampaignId { get; set; } = string.Empty;
        public virtual DonationCampaign DonationCampaign { get; set; } = null!;
        public string UserId { get; set; } = string.Empty;
        public virtual User User { get; set; } = null!;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}

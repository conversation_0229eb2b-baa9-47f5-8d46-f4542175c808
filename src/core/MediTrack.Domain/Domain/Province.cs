﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class Province : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
        public string NewId { get; set; } = string.Empty;
        public string NewName { get; set; } = string.Empty;
        public string? NewLevel { get; set; } = string.Empty;
        public virtual List<District>? Districts { get; set; } = [];
    }
}

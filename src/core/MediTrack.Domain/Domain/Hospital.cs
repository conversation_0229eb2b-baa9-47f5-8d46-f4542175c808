using MediTrack.Domain.Bases;
using System.ComponentModel.DataAnnotations.Schema;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// <PERSON>h s<PERSON>ch bệnh viện
    /// </summary>
    public class Hospital : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// MerchantId của bệnh viện, chỉ dùng để gen QR từ his
        /// </summary>
        public string MerchantId { get; set; } = string.Empty;

        /// <summary>
        /// SecretKey của bệnh viện, chỉ dùng để gen QR từ his
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// Url của bệnh viện, chỉ dùng để gen QR từ his
        /// </summary>
        public string IpnUrl { get; set; } = string.Empty;
        public List<string>? Images { get; set; } = [];

        public string AccountHolderName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;

        public string BankTransferId { get; set; } = string.Empty;
        /// <summary>
        /// Vùng của ngân hàng
        /// </summary>
        public string BankZone { get; set; } = string.Empty;
        /// <summary>
        /// Khu vực của ngân hàng
        /// </summary>
        public string BankArea { get; set; } = string.Empty;
        public string? BankBranchCode { get; set; }
        public BankBranch? BankBranch { get; set; }

        public string BankTranPrefix { get; set; } = string.Empty;
        public string HisInsuranceKey { get; set; } = string.Empty;
        public string HisUrl { get; set; } = string.Empty;

        public string ProvinceId { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string WardId { get; set; } = string.Empty;

        public string Address { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;

        public string ContactPhone { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;

        public float? Latitude { get; set; }
        public float? Longitude { get; set; }

        public string? ParentId { get; set; }

        public Hospital? Parent { get; set; }

        /// <summary>
        /// Version Client của HIS
        /// </summary>
        public string? HisVersion { get; set; }

        /// <summary>
        /// Xác định bệnh viện genQR (true) hoặc Medi genQR (false)
        /// </summary>
        public bool IsGenQR { get; set; } = false;

        /// <summary>
        /// Cờ xác định bỏ qua màn lấy danh sách dịch vụ của HIS hay không (luồng Dịch vụ)
        /// </summary>
        public bool IsSkipGetServices { get; set; } = false;

        /// <summary>
        /// Cờ xác định bỏ qua màn lấy danh sách dịch vụ của HIS hay không (luồng bảo hiểm)
        /// </summary>
        public bool IsSkipGetInsuranceServices { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có đi luồng tạm ứng hay không (luồng Dịch vụ)
        /// </summary>
        public bool IsAdvancePayment { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có đi luồng tạm ứng hay không (luồng Bảo hiểm)
        /// </summary>
        public bool IsInsuranceAdvancePayment { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có tạo phiếu tiếp nhận hay không (cho phép ref no trả về rỗng hay không)
        /// </summary>
        public bool IsHisIgnoreCreateRegister { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có chức năng in số thứ tự hay không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsGenQueueNumberDefault { get; set; } = false;

        /// <summary>
        /// His gen STT hay Medi gen STT
        /// </summary>
        public bool IsGenQueueNumberByHis { get; set; } = false;

        /// <summary>
        /// Cờ xác định có cần xác thực trước khi lấy số thứ tự không
        /// </summary>
        public bool IsNeedAuthenticateBeforeGetQueueNumber { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có chức năng in phiếu thanh toán không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsPaymentDocumentDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có chức năng in phiếu tiếp nhận không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsRegisterDocumentDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có hỗ trợ khám bảo hiểm không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsSupportInsuranceDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định HIS có hỗ trợ in lại phiếu đăng kí sau thanh toán không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsRePrintRegisterDocumentDefault { get; set; } = false;

        /// <summary>
        /// Có luồng tái khám theo lịch sử không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsReExaminationDefault { get; set; } = false;

        /// <summary>
        /// Luồng tái khám theo chỉ định của bác sĩ (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsReExaminationByDoctorDefault { get; set; } = false;

        /// <summary>
        /// Có đăng kí khám cho bản thân không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsRegisterSelfDefault { get; set; } = true;

        /// <summary>
        /// Có đăng kí khám cho người thân không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsRegisterRelativeDefault { get; set; } = false;

        /// <summary>
        /// Có đăng kí khám luồng đăng kí ở app, check in ở kiosk không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsAppRegisterCheckInKioskDefault { get; set; } = false;

        /// <summary>
        /// Có hỗ trợ đăng kí khám bằng VNEID không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsSupportVNeIDDefault { get; set; } = false;

        /// <summary>
        /// Có hỗ trợ khám cận lâm sàn không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsParaclinicalExaminationDefault { get; set; } = false;

        /// <summary>
        /// Khi cờ = true => vào màn dịch vụ mới check bảo hiểm, chứ không check từ màn examp-type nữa, cờ là false thì như luồng hiện tại
        /// </summary>
        public bool IsSupportInsuranceInServiceScreenDefault { get; set; } = false;

        /// <summary>
        /// Cờ ẩn hiện toggle button bảo hiểm y tế ở màn dịch vụ
        /// </summary>
        public bool IsHideInsuranceToggleButton { get; set; } = false;

        /// <summary>
        /// Cờ ẩn hiện toggle lấy số thứ tự ưu tiên ở màn dịch vụ
        /// </summary>
        public bool IsHidePriorityQueueToggleButton { get; set; } = false;

        /// <summary>
        /// Khi cờ = true => tự động check bảo hiểm của bệnh nhân và chọn phân loại bảo hiểm nếu có
        /// </summary>
        public bool IsAutoCheckAndSelectInsuranceType { get; set; } = false;

        /// <summary>
        /// Có bỏ qua thanh toán hay không (trường hợp giá khác 0, dùng cho bảo hiểm)
        /// </summary>
        public bool IsIgnoreInsurancePayment { get; set; }

        /// <summary>
        /// Có show trình độ văn hóa không
        /// </summary>
        public bool IsShowEducationLevel { get; set; }

        /// <summary>
        /// có hiện nơi làm việc không
        /// </summary>
        public bool IsShowWorkPlace { get; set; }
        /// <summary>
        /// có hiện địa chỉ làm việc không
        /// </summary>
        public bool IsShowWorkAddress { get; set; }

        /// <summary>
        /// Json Config của Medi, 1: face_matching, 2: Near - far liveness, 3: Left - middle - right liveness (default)
        /// </summary>
        public int LivenessType { get; set; } = 3;

        /// <summary>
        /// Phiên bản hiện tại của app kiosk bệnh viện
        /// </summary>
        public string KioskVersion { get; set; } = string.Empty;

        /// <summary>
        /// Cờ bỏ qua nhập số điện thoại khi đăng kí
        /// </summary>
        public bool IsSkipInputPhoneNumber { get; set; }

        /// <summary>
        /// Cờ ẩn màn hình nhập số điện thoại (không phải skip màn hình)
        /// </summary>
        public bool IsHideInputPhoneNumber { get; set; } = false;

        /// <summary>
        /// Cờ ẩn tuyến bhyt khi check bảo hiểm
        /// </summary>
        public bool IsHideReferralInsurance { get; set; }

        /// <summary>
        /// Có nhập thông tin chuyển tuyến không
        /// </summary>
        public bool IsInputTransferReferralInfo { get; set; }

        /// <summary>
        /// Có input lý do khám bệnh không
        /// </summary>
        public bool IsInputReasonForVisit { get; set; }

        /// <summary>
        /// Tình trạng triển khai của bệnh viện
        /// </summary>
        public string? DeploymentStatus { get; set; }

        /// <summary>
        /// Phương thức triển khai của bệnh viện
        /// </summary>
        public string? DeploymentMethod { get; set; }

        /// <summary>
        /// Cờ xác định có đang bảo trì hay không
        /// </summary>
        public bool IsMaintenanceMode { get; set; } = false;

        /// <summary>
        /// Cờ xác định có đang active hay không
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Cờ xác định có hiển thị nghề nghiệp (4750) hay không
        /// </summary>
        public bool IsShowCareer { get; set; }

        /// <summary>
        /// Cờ xác định có hiển thị nghề nghiệp phía bệnh viện hay không
        /// </summary>
        public bool IsShowSocialCareer { get; set; }

        /// <summary>
        /// Cờ xác định có hiển thị hình thức chuyển tuyến hay không
        /// </summary>
        public bool IsShowTransferReferralType { get; set; }

        /// <summary>
        /// Cờ xác định có hiển thị Lý do chuyển tuyến hay không
        /// </summary>
        public bool IsShowTransferReferralReason { get; set; }

        /// <summary>
        /// Cờ xác định có hiển thị ngày chuyển tuyến hay không
        /// </summary>
        public bool IsShowTransferReferralDate { get; set; }

        /// <summary>
        /// Cờ xác định có hiển thị thông tin chuẩn đoán tuyến dưới hay không
        /// </summary>
        public bool IsShowTransferReferralDiagnosisInfo { get; set; }

        /// <summary>
        /// Cờ xác định có tra cứu thông tin bệnh nhân bằng mã thẻ BHYT hay không
        /// </summary>
        public bool IsSearchPatientByInsuranceNo { get; set; }

        /// <summary>
        /// Cờ xác định có cho lựa chọn là người mới hay không
        /// </summary>
        public bool IsAllowSelectNewPatient { get; set; }

        /// <summary>
        /// Có gen QR khi tạo phiếu đăng kí tới quầy không
        /// </summary>
        public bool IsGenQRWhenCreateRegister { get; set; }

        /// <summary>
        /// Có hiển thị nhập thông tin xuất hóa đơn điện tử hay không
        /// </summary>
        public bool IsShowInputEInvoiceInfo { get; set; }

        /// <summary>
        /// Có ẩn thay đổi địa chỉ tạm trú hay không
        /// </summary>
        public bool IsHideChangeAddressField { get; set; }

        /// <summary>
        /// Có luôn luôn sử dụng đơn giá thu thêm làm giá thanh toán bảo hiểm hay không
        /// </summary>
        public bool IsUseExtraFeeAsInsurancePrice { get; set; }

        /// <summary>
        /// Có tự động chọn đối tượng 130 hay không (EHIS)
        /// </summary>
        public bool IsAutoSelectObject130 { get; set; }

        /// <summary>
        /// Có show cờ điều trị arv hay không
        /// </summary>
        public bool IsShowArvTreatment { get; set; }

        /// <summary>
        /// Thời gian logout tự động của kiosk (giây)
        /// </summary>
        public int AutoLogoutTimeSeconds { get; set; } = 10;

        /// <summary>
        /// Thông báo của bệnh viện
        /// </summary>
        public string Announcement { get; set; } = string.Empty;

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Huyết áp.
        /// </summary>
        public bool IsShowBloodPressure { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Nhịp tim.
        /// </summary>
        public bool IsShowHeartRate { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Tần số hô hấp.
        /// </summary>
        public bool IsShowRespiratoryRate { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Nồng độ oxy trong máu.
        /// </summary>
        public bool IsShowBloodOxygen { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Chiều cao.
        /// </summary>
        public bool IsShowHeight { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Cân nặng.
        /// </summary>
        public bool IsShowWeight { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Mạch.
        /// </summary>
        public bool IsShowPulseRate { get; set; }

        /// <summary>
        /// Cờ hiển thị hoặc ẩn thông tin Nhiệt độ.
        /// </summary>
        public bool IsShowTemperature { get; set; }

        /// <summary>
        /// Cờ chặn khám bệnh cho bệnh nhân BHYT nếu bệnh nhân đã khám tại bệnh viện khác trong ngày
        /// </summary>
        public bool IsBlockForDuplicatedVisitInsurance { get; set; }

        /// <summary>
        /// Có cho phép nhập tự do số tiền tạm ứng hay không
        /// </summary>
        public bool IsAllowInputAdvancePaymentAmount { get; set; }

        /// <summary>
        /// Số tiền tạm ứng tối thiểu
        /// </summary>
        public decimal AdvancePaymentMinAmount { get; set; } = 0;

        /// <summary>
        /// Số tiền tạm ứng tối đa
        /// </summary>
        public decimal? AdvancePaymentMaxAmount { get; set; }

        /// <summary>
        /// Có cho phép thử lại lần hai băng mã số bệnh nhân không
        /// </summary>
        public bool IsAllowCustomerRetryByPatientCode { get; set; }
        /// <summary>
        /// Có cho phép thử lại lần hai băng mã số BHYT không
        /// </summary>
        public bool IsAllowCustomerRetryByInsuranceCode { get; set; }

        /// <summary>
        /// Có cho phép đẩy số thứ tự qua HIS không
        /// </summary>
        public bool IsAllowPushQueueInfoToHis { get; set; }

        /// <summary>
        /// Json Config của HIS, dùng để call api HIS
        /// </summary>
        [Column(TypeName = "jsonb")]
        public Dictionary<string, string>? HisConfig { get; set; }

        /// <summary>
        /// His version của bệnh viện
        /// </summary>
        public string? HisId { get; set; }
        public virtual His? His { get; set; }

        /// <summary>
        /// Tỉ lệ match face của bệnh viện
        /// </summary>
        public double FaceMatchingRateAccepted { get; set; } = 82.5;

        /// <summary>
        /// Cờ chặn khám BHYT vào cuối tuần
        /// </summary>
        public bool IsBlockInsuranceOnWeekend { get; set; }

        /// <summary>
        /// Cờ cho phép thanh toán luồng bảo lãnh viện phí hay không
        /// </summary>
        public bool IsAllowPaymentGuarantee { get; set; }

        /// <summary>
        /// Cờ cho phép thanh toán luồng check in kiosk hay không
        /// </summary>
        public bool IsAllowPaymentCheckIn { get; set; }

        /// <summary>
        /// Cờ xác định bệnh viện có chuyển luồng mới hay chưa
        /// </summary>
        public bool IsNewFlow { get; set; }

        /// <summary>
        /// Cờ xác định bệnh viện bỏ qua check BHYT hay không (luồng bảo hiểm)
        /// </summary>
        public bool IsIgnoreCheckInsurance { get; set; } = false;

        /// <summary>
        /// Cờ ẩn thanh toán QRCode (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsHideQrCodePaymentDefault { get; set; } = false;

        /// <summary>
        /// Cờ chặn khám cho bệnh nhân dưới 16 tuổi
        /// </summary>
        public bool IsBlockUnder16YearsOld { get; set; } = false;

        /// <summary>
        /// Cờ xác định có chức năng đăng kí khám sức khỏe không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsHealthCheckRegisterDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép chọn nhiều dịch vụ khi đăng kí khám hay không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsAllowSelectMultipleServicesDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này phép in phiếu thủ công hay không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsAllowPrintManualDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép cho xem bản đồ hay không (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsAllowShowHospitalMapDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện có show chênh lệch giá bảo hiểm so với giá dịch vụ hay không
        /// </summary>
        public bool IsShowDifferenceInsuranceAndServicePrice { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện có cho bật cấu hình thời gian khám hay không
        /// </summary>

        public bool IsActiveConfigExaminationTime { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này phép bỏ qua tạm ứng
        /// </summary>
        public bool IsAllowBypassAdvancePayment { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này đã chuyển sang luồng hành chính 2 cấp
        /// </summary>
        public bool IsTwoLevelAddress { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này có cho phép tra cứu dịch vụ y tế hay không
        /// </summary>
        public bool IsAllowSearchHealthServiceDefault { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này hiện danh sách mã tai nạn hay không
        /// </summary>
        public bool IsShowAccidentCode { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này hiện danh sách nhóm máu hay không
        /// </summary>
        public bool IsShowBloodType { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này ẩn thông tin bảo hiểm khi thông tuyến
        /// </summary>
        public bool IsInsurancePopupHiddenOnCorrectReferral { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này cho phép thanh toán bảo hiểm
        /// </summary>
        public bool IsInsurancePaymentInAppAllowed { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép thanh toán dịch vụ tại nhà trong luồng check-in
        /// </summary>
        public bool IsServicePaymentInAppAllowed { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này có hiển thị chẩn đoán đầu vào hay không
        /// </summary>
        public bool IsInputDiagnosisVisible { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này có cho bệnh nhân lấy STT 2 lần trong ngày
        /// </summary>
        public bool IsAllowOnlyOneQueuePerDay { get; set; } = false;

        /// <summary>
        /// Json Config của Hospital trả cho FE
        /// </summary>
        [Column(TypeName = "jsonb")]
        public Dictionary<string, bool>? RequireInputConfig { get; set; }

        public bool IsShowChatBot { get; set; } = false;

        public virtual List<HospitalMetaData>? HospitalMetaDatas { get; set; }
    }
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Phiếu thu
    /// </summary>
    public class Receipt : BaseAudit
    {
        /// <summary>
        /// Số phiếu thu tại Meditrack
        /// </summary>
        public string Number { get; set; } = string.Empty;

        public string? CustomerId { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime ReceiptDate { get; set; }

        /// <summary>
        /// Số phiếu tiếp nhận
        /// </summary>
        public string? RegisterNumber { get; set; }

        /// <summary>
        /// Mã thành viên nếu là luồng nạp tiền vào
        /// </summary>
        public string? MembershipId { get; set; }

        /// <summary>
        /// Mã chiến dịch quyên góp nếu là luồng donation
        /// </summary>
        public string? DonationCampaignId { get; set; }

        /// <summary>
        /// Mã tham chiếu đến phiếu thu HIS
        /// </summary>
        public string? RefNo { get; set; }

        /// <summary>
        /// Qr Code và tình trạng thanh toán
        /// </summary>
        public string? QrCode { get; set; }

        /// <summary>
        /// NEW: Tạo mới
        /// PAID: Đã thanh toán
        /// FAIL: Thanh toán thất bại
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// Message tra cứu phản hồi từ HIS
        /// </summary>
        public string? RefMessage { get; set; }

        public string? HospitalId { get; set; }

        public string? DeviceId { get; set; } = string.Empty;

        [Column(TypeName = "jsonb")]
        public object? AdditionalData { get; set; }

        /// <summary>
        /// Mã bệnh nhân phía BV để tra cứu
        /// </summary>
        public string PatientCodeRef { get; set; } = string.Empty;

        /// <summary>
        /// Tên bệnh nhân phía BV để tra cứu
        /// </summary>
        public string PatientNameRef { get; set; } = string.Empty;

        /// <summary>
        /// Ngày sinh bệnh nhân phía BV để tra cứu
        /// </summary>
        public string PatientDateOfBirthRef { get; set; } = string.Empty;

        /// <summary>
        /// Giới tính bệnh nhân phía BV để tra cứu
        /// </summary>
        public string PatientGenderRef { get; set; } = string.Empty;

        /// <summary>
        /// Tên bệnh viện phía BV để tra cứu
        /// </summary>
        public string ClinicNameRef { get; set; } = string.Empty;

        /// <summary>
        /// Tên thu ngân phía BV để tra cứu
        /// </summary>
        public string CashierRef { get; set; } = string.Empty;

        /// <summary>
        /// Tên dịch vụ phía BV để tra cứu
        /// </summary>
        public string ServiceNameRef { get; set; } = string.Empty;

        /// <summary>
        /// Phiếu thu này được tạo từ đâu, có thể là KIOSK, PARTNER,...
        /// </summary>
        public string CreatedFrom { get; set; } = "KIOSK";

        /// <summary>
        /// Loại phiếu thu, có thể là nạp tiền vào ví, thanh toán dịch vụ, quyên góp,...
        /// </summary>
        public string ReceiptType { get; set; } = "DANG_KY";

        public virtual Register? Register { get; set; }
        public virtual Customer? Customer { get; set; }
        public virtual Hospital? Hospital { get; set; }
        public virtual List<Payment>? Payment { get; set; }
    }
}
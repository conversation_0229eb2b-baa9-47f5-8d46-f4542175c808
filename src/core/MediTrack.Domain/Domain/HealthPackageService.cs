using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MediTrack.Domain.Domain
{
    public class HealthPackageService
    {
        /// <summary>
        /// Id dịch vụ khám sức khỏe
        /// </summary>
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Mã dịch vụ khám sức khỏe
        /// </summary>
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// Tên dịch vụ khám sức khỏe
        /// </summary>
        public string? Name { get; set; } = string.Empty;
        /// <summary>
        /// Mô tả dịch vụ khám sức khỏe
        /// </summary>
        public string? Description { get; set; } = string.Empty;
        /// <summary>
        /// Đơn giá dịch vụ khám sức khỏe (VNĐ)
        /// </summary>
        public decimal UnitPrice { get; set; }
        /// <summary>
        /// Giá hiển thị dịch vụ khám sức khỏe (giá hiển thị với giá thực tế là khác nhau)
        /// </summary>
        public string? UnitPriceDisplay { get; set; } = string.Empty;
        /// <summary>
        /// Id gói khám sức khỏe
        /// </summary>
        public string? HealthPackageId { get; set; } = string.Empty;
        /// <summary>
        /// Mã gói khám sức khỏe
        /// </summary>
        public string? HealthPackageCode { get; set; } = string.Empty;
        /// <summary>
        /// Tên gói khám sức khỏe
        /// </summary>
        public string? HealthPackageName { get; set; } = string.Empty;
        /// <summary>
        /// Id loại gói khám sức khỏe
        /// </summary>
        public string? ExamePackageTypeId { get; set; } = string.Empty;
        /// <summary>
        /// Thứ tự hiển thị
        /// </summary>
        public int? SortIndex { get; set; }
    }
}
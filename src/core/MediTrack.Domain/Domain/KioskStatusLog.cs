﻿using System.ComponentModel.DataAnnotations.Schema;
using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class KioskStatusLog : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int? ApiAppStatus { get; set; } = 0; 
        public int? MediPayAppStatus { get; set; } = 0;
        public int? AdvertisingAppStatus { get; set; } = 0;
        public int? UltraViewAppStatus { get; set; } = 0;
        public string? NetworkPing { get; set; } = string.Empty;
        public string? UsedRamPercent { get; set; } = string.Empty;
        public string? UsedCpuPercent { get; set; } = string.Empty;
        public string? Uptime { get; set; } = string.Empty;
        public string? KioskId { get; set; } = string.Empty;
        public DateTime? LogDate { get; set; }
    }
}

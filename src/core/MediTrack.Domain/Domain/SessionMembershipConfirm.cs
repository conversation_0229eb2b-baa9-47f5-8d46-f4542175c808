using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class SessionMembershipConfirm : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// "WAIT" | "ACCEPT" | "REJECT"
        /// </summary>
        public string Status { get; set; } = "WAIT";
        public string? MembershipId { get; set; }
        public string? CustomerId { get; set; }
        public string? HospitalId { get; set; }
        public DateTime? ConfirmedAt { get; set; }
        public DateTime? ExpiredAt { get; set; }
        public virtual Membership? Membership { get; set; }
        public virtual Customer? Customer { get; set; }
        public virtual Hospital? Hospital { get; set; }
    }
}
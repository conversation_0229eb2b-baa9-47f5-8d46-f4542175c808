using MediTrack.Domain.Bases;
using MediTrack.Domain.Enums;

namespace MediTrack.Domain.Domain
{
    /// <summary>
    /// Thông tin thành viên
    /// </summary>
    public class Membership : BaseAudit
    {
        /// <summary>
        /// Định danh duy nhất của thành viên
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Mã thành viên
        /// </summary>
        public string MemberCode { get; set; } = string.Empty;

        /// <summary>
        /// Tên thẻ
        /// </summary>
        public string CardName { get; set; } = string.Empty;

        /// <summary>
        /// Số điện thoại liên lạc
        /// </summary>
        public string ContactPhone { get; set; } = string.Empty;

        /// <summary>
        /// Số lượt dùng
        /// </summary>
        public int? UsageCount { get; set; }

        /// <summary>
        /// Đ<PERSON> tuổi tối thiểu
        /// </summary>
        public int? FromAge { get; set; }

        /// <summary>
        /// Đ<PERSON> tuổi tối đa 
        /// </summary>
        public int? ToAge { get; set; }

        /// <summary>
        /// Loại gói (0: Gói quản lý theo số lần dùng; 1: Gói quản lý theo số dư)
        /// </summary>
        public MembershipTypeEnum Type { get; set; } = MembershipTypeEnum.ByUsageCount;

        /// <summary>
        /// Khuyến mãi khi mua dịch vụ
        /// </summary>
        public decimal DiscountValue { get; set; } = 0;

        /// <summary>
        /// Đơn vị của Khuyến mãi khi mua dịch vụ (%, số tiền,...)
        /// </summary>
        public string? DiscountUnit { get; set; }

        /// <summary>
        /// Ngày hết hạn
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// Số dư duy trì tối thiểu
        /// </summary>
        public decimal MinimumBalance { get; set; } = 0;

        /// <summary>
        /// Số dư
        /// </summary>
        public decimal Balance { get; set; } = 0;

        public string CustomerId { get; set; } = string.Empty;
        public string HospitalId { get; set; } = string.Empty;

        public virtual Customer Customer { get; set; } = default!;
        public virtual Hospital Hospital { get; set; } = default!;

        public virtual PackageService? PackageService { get; set; }
        public string? PackageServiceId { get; set; }

        public virtual List<MembershipHistory>? MembershipHistories { get; set; }
    } 
}
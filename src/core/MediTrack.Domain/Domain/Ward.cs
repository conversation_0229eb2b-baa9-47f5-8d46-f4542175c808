﻿using MediTrack.Domain.Bases;

namespace MediTrack.Domain.Domain
{
    public class Ward : BaseAudit
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;

        /// <summary>
        /// 
        /// </summary>
        public string DistrictId { get; set; } = string.Empty;
        public string ProvinceId { get; set; } = string.Empty;
        public virtual District? District { get; set; }
        public virtual Province? Province { get; set; }
        public string NewId { get; set; } = string.Empty;
        public string NewName { get; set; } = string.Empty;
        public string? NewLevel { get; set; } = string.Empty;
        public string NewProvinceId { get; set; } = string.Empty;
    }
}

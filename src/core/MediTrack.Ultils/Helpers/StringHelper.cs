﻿using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace MediTrack.Ultils.Helpers
{
    public static partial class StringHelper
    {
        /// <summary>
        /// remove tp,thành phố,h.,quận,huyện,thị xã,ph<PERSON><PERSON>ng,t.,x.,xã,tx.,thị trấn,p.,.,-,_ from beginning of string
        /// </summary>
        /// <param name="address"></param>
        /// <returns></returns>
        public static string RemoveAddressPrefix(string address)
        {
            var prefix = new List<string> { "tỉnh", "tp.", "thành phố", "h.", "quận", "huyện", "thị xã", "phường", "t.", "x.", "xã", "tx.", "thị trấn", "p.", ".", "-", "_" };
            foreach (var item in prefix)
            {
                if (address.StartsWith(item, StringComparison.OrdinalIgnoreCase))
                {
                    address = address[item.Length..].Trim();
                }
            }

            return address;
        }

        public static string NormalizeNumberInAddress(string address)
        {
            if (string.IsNullOrEmpty(address))
                return address;

            return System.Text.RegularExpressions.Regex.Replace(
                address,
                @"(?<=\D)0([1-9])(?=\D|$)",
                "$1"
            );
        }

        public static string FindMatchedString(this List<string> strings, string address)
        {
            string? foundStr = strings.Find(x => address.Equals(x, StringComparison.OrdinalIgnoreCase));
            if (foundStr != null)
                return foundStr;

            return "";
        }

        public static string FindClosestString(this List<string> stringList, string inputString)
        {
            string closestString = string.Empty;
            int minDistance = int.MaxValue;

            foreach (string s in stringList)
            {
                int distance = Fastenshtein.Levenshtein.Distance(inputString, s);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestString = s;
                }
            }

            return closestString;
        }

        public static string ProcessHTMLFile(string filePath, object data)
        {
            // Đọc nội dung file HTML vào chuỗi
            string htmlContent = File.ReadAllText(filePath);

            // Thực hiện thay thế
            htmlContent = ReplaceProperties(htmlContent, data);

            // Đảm bảo không còn tham chiếu đến object sau khi thực hiện xong
            // Nếu dữ liệu là đối tượng, có thể không cần làm gì thêm vì .NET sẽ quản lý vòng đời

            return htmlContent;
        }

        #region Support Function
        private static string ReplaceProperties(string htmlContent, object data)
        {
            // Sử dụng Regex để tìm các mẫu [{property}]
            Regex regex = MyRegex();

            // Tìm tất cả các mẫu trong chuỗi HTML
            MatchCollection matches = regex.Matches(htmlContent);

            foreach (Match match in matches)
            {
                // Lấy tên thuộc tính từ mẫu
                string propertyName = match.Groups[1].Value;

                // Tìm giá trị của thuộc tính trong object
                string propertyValue = GetPropertyValue(data, propertyName);

                // Thay thế mẫu [{property}] bằng giá trị tìm được
                htmlContent = htmlContent.Replace(match.Value, propertyValue);
            }

            return htmlContent;
        }

        private static string GetPropertyValue(object data, string propertyName)
        {
            // Kiểm tra xem object có tồn tại thuộc tính này không
            PropertyInfo? propertyInfo = data.GetType().GetProperty(propertyName);
            if (propertyInfo != null)
            {
                // Lấy giá trị của thuộc tính
                object? value = propertyInfo.GetValue(data);

                // Chuyển đổi giá trị thành chuỗi và trả về
                return value?.ToString() ?? string.Empty;
            }

            return string.Empty; // Trường hợp không tìm thấy thuộc tính
        }

        [GeneratedRegex(@"\[\{(.+?)\}\]")]
        private static partial Regex MyRegex();

        /// <summary>
        /// Remove vietnamese character
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveVietnameseCharacter(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            // Normalize the input to FormD (Decomposed)
            string normalizedString = str.Normalize(NormalizationForm.FormD);

            // Use a regex to remove all non-spacing mark characters
            Regex regex = new Regex(@"\p{IsCombiningDiacriticalMarks}+");
            string withoutDiacritics = regex.Replace(normalizedString, string.Empty);

            //replace space Đ to D
            withoutDiacritics = withoutDiacritics.Replace("Đ", "D").Replace("đ", "d");

            // Normalize back to FormC (Composed)
            return withoutDiacritics.Normalize(NormalizationForm.FormC);
        }


        /// <summary>
        /// Convert number to Vietnamese text
        /// </summary>
        static readonly string[] Units = { "", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín" };
        static readonly string[] Tens = { "", "mười", "hai mươi", "ba mươi", "bốn mươi", "năm mươi", "sáu mươi", "bảy mươi", "tám mươi", "chín mươi" };
        static readonly string[] SmallScales = { "", "nghìn", "triệu", "tỷ" };

        private static string ConvertHundreds(int num)
        {
            if (num == 0) return "";
            int hundred = num / 100;
            int remainder = num % 100;
            string result = "";

            if (hundred > 0)
                result += Units[hundred] + " trăm ";
            else if (hundred == 0 && remainder > 0)
                result += "không trăm ";

            if (remainder > 0)
            {
                if (remainder < 10)
                    result += "lẻ " + Units[remainder];
                else if (remainder < 20)
                    result += "mười " + (remainder == 10 ? "" : Units[remainder % 10]);
                else
                    result += Tens[remainder / 10] + (remainder % 10 > 0 ? " " + Units[remainder % 10] : "");

                if (remainder % 10 == 5 && !result.EndsWith("lẻ năm"))
                    result = result.Substring(0, result.Length - 3) + "lăm";
            }

            return result.Trim();
        }

        public static string ConvertNumberToVietnameseText(decimal number)
        {
            if (number == 0) return "Không";

            string result = "";
            int scaleIndex = 0;
            List<string> scales = new List<string>(SmallScales);

            while (number > 0)
            {
                int chunk = (int)(number % 1000);
                number /= 1000;

                if (scales.Count <= scaleIndex)
                    scales.Add(scales[scaleIndex - 3]);

                if (chunk > 0)
                {
                    string chunkText = ConvertHundreds(chunk);
                    string suffix = scaleIndex > 0 ? scales[scaleIndex] : "";

                    if (scaleIndex > 0 && !string.IsNullOrWhiteSpace(chunkText))
                        result = chunkText + " " + suffix + " " + result;
                    else
                        result = chunkText + " " + result;
                }

                scaleIndex++;
            }

            if (result.StartsWith("không trăm "))
                result = result.Substring(11);

            if (result.StartsWith("lẻ "))
                result = result.Substring(3);

            return char.ToUpper(result[0]) + result.Substring(1).Trim();
        }
    }
    #endregion
}

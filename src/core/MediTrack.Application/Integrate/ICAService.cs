

using MediTrack.Domain.Enums;
using CAService.Lib.Models;
using CAService.Lib.Requests;

namespace MediTrack.Application.Integrate
{
    public interface ICAService
    {
        Task<(bool, string, ErrorTypeEnum, LoginData)> LoginAsync(LoginRequest request);
        Task<(bool, string, ErrorTypeEnum, CreateCustomerData)> CreateCustomerAsync(CreateCustomerRequest request);
        Task<(bool, string, ErrorTypeEnum, FaceRecognitionData)> GetCustomersByFaceAsync(FaceRecognitionRequest request);
        Task<(bool, string, ErrorTypeEnum, CustomerPinData)> GetCustomerPinAsync(CustomerPinRequest request);
        Task<(bool, string, ErrorTypeEnum, SignedPdfData)> GetSignedPdfAsync(Guid id);
        Task<(bool, string, ErrorTypeEnum, SignPdfRequestData)> CreateSignPdfRequestAsync(SignPdfRequestRequest request);
        Task<(bool, string, ErrorTypeEnum, SignPdfData)> SignPdfAsync(SignPdfRequest request);
    }
}

﻿using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Integrate
{
    public interface IHisService
    {
        /// <summary>
        /// Lấy danh mục <PERSON>ến khám bệnh
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTierDto>)> HealthcareServiceTiers();

        /// <summary>
        /// Lấy loại hình khám chữa bệnh
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<GetHealthcareServiceTypeDto>)> HealthcareServiceTypes();

        /// <summary>
        /// Lấy danh mục đối tượng khám chữa bệnh
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<GetMedicalTreatmentCategoryDto>)> GetMedicalTreatmentCategories();

        /// <summary>
        /// Lấy thông tin loại khám (Bảo hiểm, dịch vụ....)
        /// </summary>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, List<ExameType>)> GetExameTypes();

        /// <summary>
        /// Lấy thông tin các chuyên khoa/Phòng khám
        /// </summary>
        /// <param name="exameTypeId"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, List<Clinic>)> GetClinics(string? exameTypeId = null, string? kioskId = null);

        /// <summary>
        /// Lấy danh sách nghề nghiệp của bệnh nhân từ HIS
        /// </summary>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, List<GetSocialCareerDto>)> GetSocialCareers();

        /// <summary>
        /// Lấy thông tin dịch vụ khám (phòng khám), chữa bệnh theo khoa
        /// </summary>
        /// <param name="exameTypeId">Mã loại khám</param>
        /// <param name="clinicId">Id khoa</param>
        /// <param name="subClinicId">Trường hợp clinic có danh sách clinic con</param>
        /// <param name="code">Mã khoa</param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, List<HealthService>)> GetHealthServices(GetHealthServicesDto request);

        /// <summary>
        /// Lấy mã bệnh nhân từ HIS (check tồn tại)
        /// </summary>
        /// <param name="customer"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, HisCustomerDto hisCustomerDto)> GetCustomerHis(Customer customer, string? patientCode = null, string requestType = "CCCD");

        /// <summary>
        /// Tạo phiếu đăng kí khám chữa bệnh
        /// </summary>
        /// <param name="customer">Thông tin bệnh nhân</param>
        /// <param name="customerHospital">Thông tin bệnh nhân của bệnh viện</param>
        /// <param name="service">Thông tin dịch vụ khám</param>
        /// <param name="isInsurance">Khám bảo hiểm hay không</param>
        /// <param name="insurance">Thông tin bảo hiểm</param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, string, RegisterFormResponseDto)> CreateRegisterForm(RegisterFormRequestDto request);

        /// <summary>
        /// Tạo phiếu đăng kí với nhiều dịch vụ
        /// </summary>
        /// <param name="request">Thông tin yêu cầu với danh sách dịch vụ</param>
        /// <returns>Kết quả tạo phiếu đăng ký</returns>
        Task<(bool, string, ErrorTypeEnum, string, RegisterFormsResponseDto)> CreateRegisterFormWithMultiService(RegisterFormsRequestDto request);
        /// <summary>
        /// Tạo receipt về HIS (Phú nhuận)
        /// </summary>
        /// <param name="form"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, string, string)> CreateReceipt(Register form);
        /// <summary>
        /// Tạo số thứ tự mới cho bệnh nhân (sang HIS)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, AutoDialerQueueAddNewResponse)> AutoDialerQueueAddNew(AutoDialerQueueAddNewRequest request);
        /// <summary>
        /// Lấy thông tin số thứ tự hiện tại của bệnh nhân
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, object)> AutoDialerQueueCall(AutoDialerQueueCallRequest request);
        /// <summary>
        /// Lấy thông tin số thứ tự hiện tại theo phòng khám
        /// </summary>
        /// <param name="maPhong"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, List<AllCurrentNumberResponse>)> GetAllCurrentNumber(string maPhong);
        /// <summary>
        /// Check trạng thái thanh toán của phiếu thu
        /// </summary>
        /// <param name="refNo">refno từ his</param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, PaymentStatusDto)> CheckPaymentStatus(string refNo);
        /// <summary>
        /// Lấy thông tin bảo hiểm y tế của bệnh nhân
        /// </summary>
        /// <param name="customer"></param>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, CustomerHealthInsurance)> GetCustomerHealthInsurance(Customer customer, bool isCheckByInsuranceNo = false);
        /// <summary>
        /// Lấy thông tin tiền tạm ứng của bệnh nhân
        /// </summary>
        /// <returns></returns>
        Task<(bool, string, ErrorTypeEnum, string[]?)> GetAdvanceMoney();
        /// <summary>
        /// Cập nhật trạng thái thanh toán của phiếu thu (sang Bệnh Viện)
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, UpdatePaymentStatusResponse)> UpdatePaymentStatus(UpdatePaymentStatusRequest request);

        /// <summary>
        /// Get healthService default
        /// </summary>
        /// <param name="hospitalId"></param>
        /// <param name="code"></param>
        /// <param name="databaseService"></param>
        /// <returns></returns>
        Task<List<HealthService>> GetDefaultHealthServices(GetDefaultHealthServiceRequest request);
        /// <summary>
        /// Cập nhật trạng thái thanh toán của phiếu thu (sang Bệnh Viện luồng cận lâm sàng)
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, UpdateParaclinicalPaymentStatusResponse)> UpdateParaclinicalPaymentStatus(UpdateParaclinicalPaymentStatusRequest request);
        /// <summary>
        /// Lấy danh sách chỉ định cận lâm sàng
        /// </summary>       
        Task<(bool, string, ErrorTypeEnum, IndicationSearchResponse)> GetParaclinicalIndications(IndicationSearchRequest request);
        /// <summary>
        /// Đẩy thông tin xuất hóa đơn sang bệnh viện
        /// </summary>       
        Task<(bool, string, ErrorTypeEnum, PushReceiptInfoResponseDto)> PushReceiptInfo(PushReceiptInfoRequestDto request);
        /// <summary>
        /// Gen QR thanh toán (luồng v4)
        /// </summary>          
        Task<(bool, string, ErrorTypeEnum, QrPaymentDto)> GenQRPayment(string refNo);
        /// <summary>
        /// Lấy danh sách loại khám
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<HealthPackageType>)> GetHealthPackageTypes();
        /// <summary>
        /// Lấy danh sách gói khám
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<HealthPackage>)> GetHealthPackages(string? packageTypeId = null);
        /// <summary>
        /// Lấy danh sách dịch vụ kỹ thuật
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<TechnicalService>)> GetHospitalTechnicalServices();
        
        /// <summary>
        /// Lấy danh mục mã tai nạn từ HIS
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<GetAccidentCodeDto>)> GetAccidentCodes();
        
        /// <summary>
        /// Lấy danh mục nhóm máu từ HIS
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<GetBloodTypeDto>)> GetBloodTypes();
        
        /// <summary>
        /// Tạo phiếu đăng kí goi khám sức khỏe
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, RegisterFormPackageResponseDto)> CreateRegisterFormforHealthPackage(RegisterFormPackageRequestDto request);

        /// <summary>
        /// Tạo phiếu đăng kí goi khám sức khỏe
        /// </summary>
        Task<(bool, string, ErrorTypeEnum, List<HealthServiceSearchDto>)> GetSearchListHealthServices();

    }
}

﻿using AgentGateway.Lib.Config;
using CAService.Lib.Config;
using FluentValidation;
using IdentityClient.Lib.Config;
using MediatR;
using MediBankClient.Lib.Config;
using MediTrack.Application.Behaviours;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.Reflection;

namespace MediTrack.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(UnhandledExceptionBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(AuthorizationBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));
                cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(PerformanceBehaviour<,>));
            });

            services.AddMediBankConfig(configuration);
            services.AddIdentityConfig(configuration);
            services.AddAgentGatewayConfig(configuration);
            services.AddCAServiceConfig(configuration);
            return services;
        }

        public static IServiceCollection AddMediBankConfig(this IServiceCollection services,
            IConfiguration configuration)
        {
            string Url = configuration.GetSection("MediBank_Url").Value ?? string.Empty;
            string CheckAccountUrl = configuration.GetSection("MediBank_CheckAccountUrl").Value ?? string.Empty;
            string CheckStatusOnBoardingUrl = configuration.GetSection("MediBank_CheckStatusOnBoardingUrl").Value ?? string.Empty;
            string ConfirmOtpUrl = configuration.GetSection("MediBank_ConfirmOtpUrl").Value ?? string.Empty;
            string SendOtpUrl = configuration.GetSection("MediBank_SendOtpUrl").Value ?? string.Empty;
            string UpdateAccountUrl = configuration.GetSection("MediBank_UpdateAccountUrl").Value ?? string.Empty;
            string UploadFileUrl = configuration.GetSection("MediBank_UploadFileUrl").Value ?? string.Empty;
            string UploadIdentityUrl = configuration.GetSection("MediBank_UploadIdentityUrl").Value ?? string.Empty;
            string GenQrTransactionUrl = configuration.GetSection("MediBank_GenQrTransactionUrl").Value ?? string.Empty;
            string CreateTransferUrl = configuration.GetSection("MediBank_CreateTransferUrl").Value ?? string.Empty;
            string GetAvailableBalanceUrl = configuration.GetSection("MediBank_GetAvailableBalanceUrl").Value ?? string.Empty;
            string CreateLoginQrUrl = configuration.GetSection("MediBank_CreateLoginQrUrl").Value ?? string.Empty;
            string CheckLoginQrStatusUrl = configuration.GetSection("MediBank_CheckLoginQrStatusUrl").Value ?? string.Empty;
            string GetAllAccountUrl = configuration.GetSection("MediBank_GetAllAccountUrl").Value ?? string.Empty;
            string GetStatusAccountLinkUrl = configuration.GetSection("MediBank_GetStatusAccountLinkUrl").Value ?? string.Empty;
            string GetCareesUrl = configuration.GetSection("MediBank_GetCareesUrl").Value ?? string.Empty;
            string IsUsePem = configuration.GetSection("MediBank_UsePem").Value ?? string.Empty;
            string PassPfx = configuration.GetSection("MediBank_PassPfx").Value ?? string.Empty;
            string Cert = configuration.GetSection("MediBank_Cert").Value ?? string.Empty;
            string Pem = configuration.GetSection("MediBank_Pem").Value ?? "my_client.csr.pem";
            string ApiKey = configuration.GetSection("MediBank_ApiKey").Value ?? string.Empty;
            string SecretKeySDKCallbackAccount = configuration.GetSection("MediBank_SecretKeySDKCallbackAccount").Value ?? string.Empty;
            var defaultConfig = new MediBankConfigModel()
            {
                Host = Url,
                CheckAccountUrl = Url + CheckAccountUrl,
                CheckStatusOnBoardingUrl = Url + CheckStatusOnBoardingUrl,
                ConfirmOtpUrl = Url + ConfirmOtpUrl,
                SendOtpUrl = Url + SendOtpUrl,
                UpdateAccountUrl = Url + UpdateAccountUrl,
                UploadFileUrl = Url + UploadFileUrl,
                UploadIdentityUrl = Url + UploadIdentityUrl,
                GenQrTransactionUrl = Url + GenQrTransactionUrl,
                CreateTransferUrl = Url + CreateTransferUrl,
                GetAvailableBalanceUrl = Url + GetAvailableBalanceUrl,
                CreateLoginQrUrl = Url + CreateLoginQrUrl,
                CheckLoginQrStatusUrl = Url + CheckLoginQrStatusUrl,
                GetAllAccountUrl = Url + GetAllAccountUrl,
                GetStatusAccountLinkUrl = Url + GetStatusAccountLinkUrl,
                GetCareesUrl = Url + GetCareesUrl,
                IsUsePem = IsUsePem.ToUpper() == "TRUE",
                PfxFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "cert", Cert),
                PassPfx = PassPfx,
                PemFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "pem", Pem),
                ApiKey = ApiKey,
                SecretKeySDKCallbackAccount = SecretKeySDKCallbackAccount

            };

            services.AddSingleton(Options.Create(defaultConfig));
            return services;
        }

        public static IServiceCollection AddIdentityConfig(this IServiceCollection services, IConfiguration configuration)
        {
            var defaultConfig = new IdentityConfigModel()
            {
                VneidUrlGetLinkSSO = configuration.GetSection("Identity_VneidUrlGetLinkSSO").Value ?? string.Empty,
                VneidUrlGetSSOUser = configuration.GetSection("Identity_VneidUrlGetSSOUser").Value ?? string.Empty,
                VerifyLeeonEIDUrl = configuration.GetSection("Identity_VerifyLeeonEIDUrl").Value ?? string.Empty,
                VerifyLeeonLivenessUrl = configuration.GetSection("Identity_VerifyLeeonLivenessUrl").Value ?? string.Empty,
                VerifyLeeonFacematchingUrl = configuration.GetSection("Identity_VerifyLeeonFacematchingUrl").Value ?? string.Empty,
                FaceSearchLeeonAddFaceUrl = configuration.GetSection("Identity_FaceSearchLeeonAddFaceUrl").Value ?? string.Empty,
                FaceSearchLeeonFindFaceUrl = configuration.GetSection("Identity_FaceSearchLeeonFindFaceUrl").Value ?? string.Empty,
                DeviceType = configuration.GetSection("DeviceType").Value ?? string.Empty,
                Project = configuration.GetSection("Project").Value ?? string.Empty,
                Matching = configuration.GetValue<double>("Matching")
                ,// Convert.ToDouble(configuration.GetSection("Matching").Value ?? "0"),
            };
            services.AddSingleton(Options.Create(defaultConfig));
            return services;
        }
        public static IServiceCollection AddAgentGatewayConfig(this IServiceCollection services, IConfiguration configuration)
        {
            var defaultConfig = new AgentGatewayConfigModel()
            {
                Host = configuration.GetSection("AgentGateWay_Host").Value ?? string.Empty,
            };
            services.AddSingleton(Options.Create(defaultConfig));
            return services;
        }

        public static IServiceCollection AddCAServiceConfig(this IServiceCollection services, IConfiguration configuration)
        {
            var defaultConfig = new CAServiceConfigModel()
            {
                Url = configuration.GetSection("CAService_Url").Value ?? string.Empty,
                EMRUrl = configuration.GetSection("CAService_EMRUrl").Value ?? string.Empty,
                Username = configuration.GetSection("CAService_Username").Value ?? string.Empty,
                Password = configuration.GetSection("CAService_Password").Value ?? string.Empty,
            };
            services.AddSingleton(Options.Create(defaultConfig));
            return services;
        }
    }
}
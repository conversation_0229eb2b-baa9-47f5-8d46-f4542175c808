﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Helpers\obj\**" />
    <EmbeddedResource Remove="Helpers\obj\**" />
    <None Remove="Helpers\obj\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="C:\Users\<USER>\.nuget\packages\hiqpdf.free\15.0.8\contentFiles\any\any\HiQPdf.dep" />
    <None Remove="C:\Users\<USER>\.nuget\packages\hiqpdf.free\15.0.8\contentFiles\any\any\HiQPdf.dep" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="3.7.310.5" />
    <PackageReference Include="FluentValidation" Version="11.9.2" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="MediatR" Version="12.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="ZXing.Net" Version="0.15.0" />
    <PackageReference Include="MongoDB.Driver" Version="2.28.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\lib\HDBankClient.Lib\HDBankClient.Lib.csproj" />
    <ProjectReference Include="..\..\lib\HisClientV2.Lib\HisClientV2.Lib.csproj" />
    <ProjectReference Include="..\..\lib\HisClientV3.Lib\HisClientV3.Lib.csproj" />
    <ProjectReference Include="..\..\lib\HisClientV5.Lib\HisClientV5.Lib.csproj" />
    <ProjectReference Include="..\..\lib\IdentityClient.Lib\IdentityClient.Lib.csproj" />
    <ProjectReference Include="..\..\lib\MediBankClient.Lib\MediBankClient.Lib.csproj" />
    <ProjectReference Include="..\..\lib\PaymentClient.Lib\PaymentClient.Lib.csproj" />
    <ProjectReference Include="..\..\lib\CAService.Lib\CAService.Lib.csproj" />
    <ProjectReference Include="..\..\lib\AgentGateway.Lib\AgentGateway.Lib.csproj" />
    <ProjectReference Include="..\..\lib\HisClient.Lib\HisClient.Lib.csproj" />
    <ProjectReference Include="..\MediTrack.Domain\MediTrack.Domain.csproj" />
    <ProjectReference Include="..\MediTrack.Ultils\MediTrack.Ultils.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Features\HealthServiceLogic\Queries\" />
    <Folder Include="Features\NationalityLogic\Validators\" />
  </ItemGroup>

</Project>

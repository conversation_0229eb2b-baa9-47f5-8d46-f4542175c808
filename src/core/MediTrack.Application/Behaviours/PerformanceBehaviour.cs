﻿using MediatR;
using MediTrack.Application.Services;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MediTrack.Application.Behaviours
{
    public class PerformanceBehaviour<TRequest, TResponse>(
        ILogger<TRequest> logger,
        ICurrentUserService user)
       : IPipelineBehavior<TRequest, TResponse> where TRequest : notnull
    {
        private readonly Stopwatch timer = new();

        public async Task<TResponse> Handle(TRequest request,
            RequestHandlerDelegate<TResponse> next,
            CancellationToken cancellationToken)
        {
            timer.Start();

            var response = await next();

            timer.Stop();

            var elapsedMilliseconds = timer.ElapsedMilliseconds;

            if (elapsedMilliseconds > 1000)
            {
                var requestName = typeof(TRequest).Name;
                var userId = user.UserId ?? string.Empty;
                var userAgent = string.Empty;

                if (!string.IsNullOrEmpty(userId))
                {
                    userAgent = user.UserAgent;

                }

                logger.LogWarning("In Process Long Running Request: {Name} ({ElapsedMilliseconds} milliseconds) {@UserId} {@UserName}",
                    requestName, elapsedMilliseconds, userId, userAgent);
            }

            return response;
        }
    }
}

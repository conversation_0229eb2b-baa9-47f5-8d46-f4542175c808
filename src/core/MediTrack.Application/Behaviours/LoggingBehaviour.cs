﻿using MediatR.Pipeline;
using MediTrack.Application.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Behaviours
{
    public class LoggingBehaviour<TRequest>(ILogger<TRequest> logger,
        ICurrentUserService user,
        IIdentityService identityService) : IRequestPreProcessor<TRequest> where TRequest : notnull
    {
        private readonly ILogger<TRequest> logger = logger;
        private readonly ICurrentUserService user = user;
        private readonly IIdentityService identityService = identityService;

        public async Task Process(TRequest request, CancellationToken cancellationToken)
        {
            var requestName = typeof(TRequest).Name;
            var userId = user.UserId ?? string.Empty;
            string? userName = string.Empty;

            if (!string.IsNullOrEmpty(userId))
            {
                userName = await identityService.GetUserNameAsync(userId);
            }

            logger.LogInformation("In Process Request: {Name} {@UserId} {@UserName} {@Request}",
                requestName, userId, userName, request);
        }
    }
}

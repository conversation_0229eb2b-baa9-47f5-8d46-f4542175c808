using MediTrack.Application.Features.KioskMetadataLogic.Dtos;

namespace MediTrack.Application.Repositories
{
    public interface IKioskMetadataRepository
    {
        Task<List<KioskMetadataDto>> GetKioskMetadatasByKeyAsync(
            string? hospitalId,
            string? kioskId,
            string? groupType = null,
            string? code = null,
            CancellationToken cancellationToken = default);

        Task<List<KioskMetadataDto>> GetKioskMetadatasByKioskHospitalAsync(string? hospitalId, string? kioskId, CancellationToken cancellationToken = default);
    }
}
﻿using AgentGateway.Lib.Requests;
using AgentGateway.Lib.Responses;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Services
{
    public interface IAgentGatewayService
    {
        Task<(bool, string, ErrorTypeEnum, InitTransactionResponseDto)> InitTransaction(InitTransactionRequest request);
        Task<(bool, string, ErrorTypeEnum, GetTransactionResponse)> GetTransaction(GetTransactionRequest request);
        Task<(bool, string, ErrorTypeEnum, UserSharedResponseDto)> RequestUserShared(UserSharedRequest request);
    }
}

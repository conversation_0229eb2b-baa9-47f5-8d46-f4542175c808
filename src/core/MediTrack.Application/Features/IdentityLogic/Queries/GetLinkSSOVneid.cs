﻿using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.IdentityLogic.Queries
{
    public class GetLinkSSOVneid : IRequest<IdentityClient.Lib.Base.BaseResponseModel<GetUrlSSOVneidResponse>>
    {
    }

    public class GetLinkSSOVneidHandler(IIdentityIntegrateService identityService) : IRequestHandler<GetLinkSSOVneid, IdentityClient.Lib.Base.BaseResponseModel<GetUrlSSOVneidResponse>>
    {
        private readonly IIdentityIntegrateService identityService = identityService;

        public async Task<IdentityClient.Lib.Base.BaseResponseModel<GetUrlSSOVneidResponse>> Handle(GetLinkSSOVneid request, CancellationToken cancellationToken)
        {
            var response = await identityService.VneidGetUrlSSO();

            return response;
        }
    }
}
﻿using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.IdentityLogic.Queries
{
    public class GetUserVneid : IRequest<BaseResponseModel<GetUserByTokenSSOVneidResponse>>
    {
        public string? sessionState { get; set; }
        public string? code { get; set; }
        public string? encryptKey { get; set; }

        public string? id { get; set; }
    }

    public class GetUserVneidHandler(IIdentityIntegrateService identityService) : IRequestHandler<GetUserVneid, BaseResponseModel<GetUserByTokenSSOVneidResponse>>
    {
        private readonly IIdentityIntegrateService identityService = identityService;

        public async Task<BaseResponseModel<GetUserByTokenSSOVneidResponse>> Handle(GetUserVneid request, CancellationToken cancellationToken)
        {
            var response = await identityService.VneidGetUserSSO(request: new GetUserByTokenSSOVneidRequest
            {
                code = request.code,
                encryptKey = request.encryptKey,
                id = request.id,
                sessionState = request.sessionState,
            });

            return response;
        }
    }
}
﻿using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediTrack.Application.Integrate;
using Microsoft.AspNetCore.Http;

namespace MediTrack.Application.Features.IdentityLogic.Commands
{
    public class VerifyLiveness : IRequest<BaseResponseModel<LivenessVerificationResponse>>
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt bên trái dạng Base64
        /// </summary>
        public string? PortraitLeft { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt chính diện dạng Base64
        /// </summary>
        public string? PortraitMid { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt bên phải dạng Base64
        /// </summary>
        /// </summary>
        public string? PortraitRight { get; set; }
    }

    public class VerifyLivenessHandler(IIdentityIntegrateService identityIntegrateService, IHttpContextAccessor httpContextAccessor) : IRequestHandler<VerifyLiveness, BaseResponseModel<LivenessVerificationResponse>>
    {
        public Task<BaseResponseModel<LivenessVerificationResponse>> Handle(VerifyLiveness request, CancellationToken cancellationToken)
        {
            return identityIntegrateService.VerifyLiveness(new LeeonLivenessVerificationRequest
            {
                PortraitLeft = request.PortraitLeft,
                PortraitMid = request.PortraitMid,
                PortraitRight = request.PortraitRight,
                OnBoardId = httpContextAccessor.HttpContext?.Request.Headers["x-trace-id"].ToString() ?? string.Empty
            });
        }
    }
}
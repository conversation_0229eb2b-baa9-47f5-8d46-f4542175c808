﻿using System.Globalization;
using IdentityClient.Lib.Base;
using IdentityClient.Lib.Config;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediTrack.Application.Integrate;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace MediTrack.Application.Features.IdentityLogic.Commands
{
    public class FaceMatching : IRequest<BaseResponseModel<LeeonFaceMatchingVerificationResponse>>
    {
        /// <summary>
        /// Ảnh trong NFC dạng base64
        /// </summary>
        public string? Img1 { get; set; }

        /// <summary>
        /// Ảnh chụp khuôn mặt chính diện dạng Base64
        /// </summary>
        public string? Img2 { get; set; }
    }

    public class FaceMatchingHandler(IOptions<IdentityConfigModel> configuration,
    IIdentityIntegrateService identityIntegrateService,
        IHttpContextAccessor httpContextAccessor,
        ICurrentHospitalService current) : IRequestHandler<FaceMatching, BaseResponseModel<LeeonFaceMatchingVerificationResponse>>
    {
        private readonly IdentityConfigModel identityConfig = configuration.Value ?? throw new NullReferenceException(nameof(IdentityConfigModel));

        public async Task<BaseResponseModel<LeeonFaceMatchingVerificationResponse>> Handle(FaceMatching request, CancellationToken cancellationToken)
        {
            var faceMatchingRes = await identityIntegrateService.FaceMatching(new LeeonFaceMatchingVerificationRequest
            {
                Img1 = request.Img1,
                Img2 = request.Img2,
                OnBoardId = httpContextAccessor.HttpContext?.Request.Headers["x-trace-id"].ToString() ?? string.Empty
            });

            if (faceMatchingRes.Code == "00" && faceMatchingRes.Data?.IsMatching == true)
            {
                double maching = Convert.ToDouble(faceMatchingRes.Data.Matching, CultureInfo.InvariantCulture);
                double matchingConfig = identityConfig.Matching;
                double matchingByBankConfig = current.CurrentHospital.FaceMatchingRateAccepted ?? 100;
                faceMatchingRes.Data.IsMatching = maching >= matchingConfig;
                faceMatchingRes.Data.IsMatchingByBank = maching >= matchingByBankConfig;
            }

            return faceMatchingRes;
        }
    }
}
﻿using FluentValidation;
using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediTrack.Application.Integrate;
using Microsoft.AspNetCore.Http;

namespace MediTrack.Application.Features.IdentityLogic.Commands
{
    public class FindFace : IRequest<BaseResponseModel<FindFaceResponse>>
    {
        /// <summary>
        /// Ảnh chụp khuôn mặt dạng Base64
        /// </summary>
        public string? Img { get; set; }
    }

    public class FindFaceValidator : AbstractValidator<FindFace>
    {
        public FindFaceValidator()
        {
            RuleFor(x => x.Img)
                .NotEmpty()
                .Must(BeAValidBase64).WithMessage("Hình ảnh xác thực không hợp lệ.");
        }

        private bool BeAValidBase64(string? img)
        {
            if (string.IsNullOrEmpty(img))
            {
                return false;
            }

            return Convert.TryFromBase64String(img, new byte[img.Length], out _);
        }
    }

    public class FindFaceCommandHandler(IIdentityIntegrateService identityIntegrateService, IHttpContextAccessor httpContextAccessor) : IRequestHandler<FindFace, BaseResponseModel<FindFaceResponse>>
    {
        public Task<BaseResponseModel<FindFaceResponse>> Handle(FindFace request, CancellationToken cancellationToken)
        {
            return identityIntegrateService.FindFace(new FindFaceRequest
            {
                Img = request.Img,
                OnBoardId = httpContextAccessor.HttpContext?.Request.Headers["x-trace-id"].ToString() ?? string.Empty
            });
        }
    }
}
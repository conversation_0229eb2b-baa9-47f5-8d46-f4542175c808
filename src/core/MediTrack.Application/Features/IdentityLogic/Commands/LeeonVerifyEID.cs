﻿using IdentityClient.Lib.Base;
using IdentityClient.Lib.Request;
using IdentityClient.Lib.Response;
using MediatR;
using MediTrack.Application.Integrate;
using Microsoft.AspNetCore.Http;

namespace MediTrack.Application.Features.IdentityLogic.Commands
{
    public class LeeonVerifyEID : IRequest<BaseResponseModel<LeeoneIDVerificationResponse>>
    {
        /// <summary>
        /// Cert đọc được từ SODFile trong CCCD gắn chip dưới định dạng PEMfile covert sang base64 sử dụng encoding ASCII.
        /// </summary>
        public string? DsCert { get; set; }

        /// <summary>
        /// Số căn cước công dân của người dân gửi xác thực.
        /// </summary>
        public string? IdCard { get; set; }

        /// <summary>
        /// Mã của partner/subpartner
        /// </summary>
        public string? Province { get; set; }
    }

    public class CommandHandler(IIdentityIntegrateService identityIntegrateService, IHttpContextAccessor httpContextAccessor) : IRequestHandler<LeeonVerifyEID, BaseResponseModel<LeeoneIDVerificationResponse>>
    {
        public Task<BaseResponseModel<LeeoneIDVerificationResponse>> Handle(LeeonVerifyEID request, CancellationToken cancellationToken)
        {
            return identityIntegrateService.VerifyEID(new LeeoneIDVerificationRequest
            {
                DsCert = request.DsCert,
                IdCard = request.IdCard,
                Province = request.Province,
                OnBoardId = httpContextAccessor.HttpContext?.Request.Headers["x-trace-id"].ToString() ?? string.Empty
            });
        }
    }
}
﻿using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.ReceiptLogic.Dtos
{
    public class ReceiptDocumentDto
    {
        public string RegisterNumber { get; set; } = string.Empty;
        public string RegisterRefNo { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; } = string.Empty;
        public string CompanyName { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string TaxNumber { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }

        public string HospitalName { get; set; } = string.Empty;
        public string HospitalCode { get; set; } = string.Empty;
        public string HospitalTaxCode { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;

        public DateTime? CreateAt { get; set; }
        public string Clinic { get; set; } = string.Empty;
        public int Priority { get; set; } = 0;
        public string Number { get; set; } = string.Empty;

        public string LinkCode { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string RateOfInsurance { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public string HealthInsuranceNo { get; set; } = string.Empty;
        public string CustomerAge { get; set; } = string.Empty;
        public string PaymentStatusDesc
        {
            get => this.PaymentStatus switch
                {
                    PaymentConstant.FailPayment => "Thất bại",
                    PaymentConstant.Success => "Thành công",
                    PaymentConstant.Partial  => "Thanh toán một phần",
                    _ => "Chưa thanh toán"
                };
        }

        public string InvoiceInfoRef {get; set;} = string.Empty;
    }
}

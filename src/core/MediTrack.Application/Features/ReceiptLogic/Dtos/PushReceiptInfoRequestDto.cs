﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ReceiptLogic.Dtos
{
    public class PushReceiptInfoRequestDto
    {
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyAddress { get; set; } = string.Empty;
        public string TaxNo { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string RefNo { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ReceiptLogic.Dtos
{
    public class UpdateReceiptPaymentStatusDto
    {
        public string HospitalId { get; set; } = string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal PaidAmount { get; set; }
        public DateTime PaidAt { get; set; }
        public string PaidDescription { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
    }
}

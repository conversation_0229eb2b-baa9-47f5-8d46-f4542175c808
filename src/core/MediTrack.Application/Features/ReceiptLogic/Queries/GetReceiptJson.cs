﻿using System.Text;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CareerLogic.Queries;
using MediTrack.Application.Features.HospitalLogic.Queries;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.ReceiptLogic.Queries
{
    public class GetReceiptJson : IRequest<BaseCommandResultWithData<string>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetReceiptJsonHandler(IDatabaseService databaseService,
       ICurrentHospitalService currentHospitalService,
       IHospitalMetadataRepository hospitalMetadataRepository,
       IMediator mediator) : IRequestHandler<GetReceiptJson, BaseCommandResultWithData<string>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<string>> Handle(GetReceiptJson request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<string>();

            //1. Lấy thông tin phiếu thu từ database
            var receipt = await databaseService.Receipts
                .Include(x => x.Customer)
                .Include(x => x.Register)
                .Include(x => x.Hospital)

                .FirstOrDefaultAsync(x => x.Number == request.Number, cancellationToken: cancellationToken);

            if (receipt is null)
            {
                result.Set(false, ReceiptConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentStatus = string.Empty;
            string invoiceInfoRef = "Chưa phát hành HĐĐT";
            if (receipt != null)
            {
                var hospitalMetadatasCache = await hospitalMetadataRepository
                    .GetHospitalMetadatasByHospitalAsync(currentHospitalService.CurrentHospital.HospitalId, cancellationToken);

                if (hospitalMetadatasCache == null || hospitalMetadatasCache.Count == 0)
                {
                    result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu.", ErrorTypeEnum.MediPayError);
                    return result;
                }

                var hospitalMetadatas = hospitalMetadatasCache
                    .Where(x => x.Code == HospitalMetadataConstant.PrinterReceipt
                            || x.Code == HospitalMetadataConstant.PrinterReceiptInsurance
                            || x.Code == HospitalMetadataConstant.PrinterReceiptHealthPackage
                            || x.Code == HospitalMetadataConstant.PrinterReceiptHealthServiceN)
                    .ToList();
                if (hospitalMetadatas.Count == 0)
                {
                    result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu", ErrorTypeEnum.MediPayError);
                    return result;
                }

                var hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterReceiptInsurance);
                // nếu không phải bảo hiểm hoặc không có mẫu in phiếu thu bảo hiểm thì lấy mẫu in phiếu thông thường
                if (receipt.Register?.HealthInsurance != 1 || hospitalMetadata is null)
                {
                    hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterReceipt);
                }

                if (receipt.Register?.RegisterType == "GOI_KHAM")
                {
                    hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterReceiptHealthPackage);
                }

                if (receipt.Register?.RegisterType == "DANG_KY_N")
                {
                    hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterReceiptHealthServiceN);
                }

                if (hospitalMetadata is null)
                {
                    result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu", ErrorTypeEnum.MediPayError);
                    return result;
                }

                var customerHospital = await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId
                        && x.CustomerId == receipt.CustomerId, cancellationToken: cancellationToken) ?? null;

                if (receipt.Register?.HealthInsurance != 1 && receipt.Status != ReceiptConstant.STATUS_PAID)
                {
                    (_, _, _, PaymentStatusDto resPayment) = await hisService.CheckPaymentStatus(receipt.RefNo ?? string.Empty);
                    paymentStatus = resPayment.PaymentStatus;
                    invoiceInfoRef = string.IsNullOrEmpty(resPayment.InvoiceInfoRef) ? "Chưa phát hành HĐĐT" : resPayment.InvoiceInfoRef;
                }
                else
                {
                    paymentStatus = PaymentConstant.Success;
                }
                var dataPrint = hospitalMetadata.Value!;

                dataPrint = dataPrint.Replace("[RegisterNumber]", receipt.Register?.RefNo ?? "-");
                dataPrint = dataPrint.Replace("[ReceiptNumber]", receipt.RefNo ?? "-");
                dataPrint = dataPrint.Replace("[CustomerName]", receipt.Customer?.GetFullName().ToUpper() ?? "-");
                dataPrint = dataPrint.Replace("[CustomerNameFmt]", PrintHelper.ServiceNameConvert(receipt.Customer?.GetFullName().ToUpper() ?? "-", 18)); // tên khách hàng
                dataPrint = dataPrint.Replace("[DateOfBirth]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("dd/MM/yyyy") ?? "-"); // ngày tháng năm sinh khách hàng
                dataPrint = dataPrint.Replace("[YearOfBirth]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("yyyy") ?? "-"); // ngày tháng năm sinh khách hàng
                dataPrint = dataPrint.Replace("[Gender]", receipt.Customer?.Sex ?? "-");
                dataPrint = dataPrint.Replace("[CompanyName]", "-");
                dataPrint = dataPrint.Replace("[PatientCode]", !string.IsNullOrEmpty(receipt.Register?.PatientCode)
                                        ? receipt.Register.PatientCode
                                        : customerHospital?.PatientCode ?? "-");
                dataPrint = dataPrint.Replace("[PaymentStatus]", GetPaymentStatusDesc(paymentStatus)); // trạng thái thanh toán
                dataPrint = dataPrint.Replace("[Address]", receipt.Customer?.Address ?? "-");
                dataPrint = dataPrint.Replace("[AddressFmt]", PrintHelper.AddressConvert(receipt.Customer?.Address ?? "-"));
                dataPrint = dataPrint.Replace("[TaxNumber]", "-");
                dataPrint = dataPrint.Replace("[STT]", receipt.Register?.QueueNumber ?? "-"); // số thứ tự
                dataPrint = dataPrint.Replace("[ServiceName]", receipt.Register?.ServiceName ?? "-");
                dataPrint = dataPrint.Replace("[ServiceNameFmt]", PrintHelper.ServiceNameConvert(receipt.Register?.ServiceName ?? "-"));
                dataPrint = dataPrint.Replace("[ServiceNameFmt40]", PrintHelper.ServiceNameConvert(receipt.Register?.ServiceName ?? "-", 40));
                dataPrint = dataPrint.Replace("[Quantity]", "1");
                dataPrint = dataPrint.Replace("[UnitPrice]", receipt.TotalAmount.ToString());
                dataPrint = dataPrint.Replace("[Total]", receipt.TotalAmount.ToString());
                dataPrint = dataPrint.Replace("[HospitalCode]", receipt.Register?.Hospital?.Id ?? "-");
                dataPrint = dataPrint.Replace("[HospitalTaxCode]", receipt.Register?.Hospital?.TaxCode ?? "-");
                dataPrint = dataPrint.Replace("[HospitalName]", receipt.Register?.Hospital?.Name ?? "-");
                dataPrint = dataPrint.Replace("[LogoUrl]", receipt.Register?.Hospital?.LogoUrl ?? "-");
                dataPrint = dataPrint.Replace("[CreateAt]", receipt.CreatedAt?.ToString() ?? "-");
                dataPrint = dataPrint.Replace("[InsuranceStatus]", receipt.Register?.HealthInsurance == 1 ? "Có" : "Không"); // Có bảo hiểm không
                dataPrint = dataPrint.Replace("[DeviceId]", currentHospitalService.KioskId); // mã kiosk
                dataPrint = dataPrint.Replace("[PrintedAt]", DateTimeHelper.GetCurrentLocalDateTime().ToString("dd/MM/yyyy HH:mm")); // ngày in
                dataPrint = dataPrint.Replace("[ReceiptTime]", DateTimeHelper.GetCurrentLocalDateTime().AddHours(23).ToString("HH'h ngày' dd/MM/yyyy")); // hạn xuất hóa đơn
                dataPrint = dataPrint.Replace("[Priority]", receipt.Register?.QueueNumberPriority == true ? "ƯU TIÊN" : "THƯỜNG");

                dataPrint = dataPrint.Replace("[PriorityWithYesNo]", receipt.Register?.QueueNumberPriority == true ? "CÓ" : "KHÔNG");

                dataPrint = dataPrint.Replace("[PriorityWithEmpty]", receipt.Register?.QueueNumberPriority == true ? "ƯU TIÊN" : "");
                dataPrint = dataPrint.Replace("[Number]", receipt.Register?.QueueNumber ?? "-");
                dataPrint = dataPrint.Replace("[Clinic]", receipt.Register?.Clinic ?? "-");
                dataPrint = dataPrint.Replace("[ClinicGroup]", receipt.Register?.ClinicGroup ?? "-"); // tên nhóm phòng khám
                dataPrint = dataPrint.Replace("[RefDocNo]", receipt.Register?.RefDocNo ?? "-");
                dataPrint = dataPrint.Replace("[MedicalTreatmentCategoryName]", receipt.Register?.MedicalTreatmentCategoryName ?? "-");
                dataPrint = dataPrint.Replace("[ExaminationLocation]", receipt.Register?.ExaminationLocation ?? "-");
                dataPrint = dataPrint.Replace("[ExaminationHour]", receipt.Register?.RegisterAt.GetValueOrDefault().AddHours(7).ToString("HH:mm") ?? "-"); // giờ khám
                dataPrint = dataPrint.Replace("[ExaminationDate]", receipt.Register?.RegisterAt.GetValueOrDefault().AddHours(7).ToString("dd/MM/yyyy") ?? "-"); // ngày khám
                dataPrint = dataPrint.Replace("[RateOfInsurance]", receipt.Register?.RateOfInsurance?.ToString() ?? "-");
                dataPrint = dataPrint.Replace("[InvoiceInfoRef]", invoiceInfoRef);
                dataPrint = dataPrint.Replace("[LinkCode]", receipt.Register?.LinkCode ?? "-");
                dataPrint = dataPrint.Replace("[RegisterRefNo]", string.IsNullOrEmpty(receipt.Register?.RefNo) ? "-" : receipt.Register?.RefNo ?? "-");
                dataPrint = dataPrint.Replace("[CustomerAge]", receipt.Customer?.DateOfBirth.GetAge().ToString() ?? "-");
                dataPrint = dataPrint.Replace("[ExpectedAppointmentAt]", receipt.Register?.ExpectedAppointmentAt?.ToString("HH:mm") ?? "-");
                dataPrint = dataPrint.Replace("[ExameType]", receipt.Register?.ExameType ?? "-"); // loại khám
                dataPrint = dataPrint.Replace("[ClinicUpper]", receipt.Register?.Clinic.ToUpper() ?? "-"); //  in hoa khoa
                dataPrint = dataPrint.Replace("[HealthInsuranceNo]", receipt.Register?.HealthInsurance == 0 ? "" : receipt.Customer?.HealthInsuranceNo?.ToUpper() ?? "-"); //  Số bảo hiểm y tế
                dataPrint = dataPrint.Replace("[CustomerPhoneNumber]", receipt.Customer?.Phone); //  Số điện thoại
                dataPrint = dataPrint.Replace("[StatusIpnMessage]", receipt.Register?.IsHisCreateFormSuccess == false
                  ? "BỆNH VIỆN CHƯA GHI NHẬN THANH TOÁN, VUI LÒNG ĐẾN QUẦY THU TIỀN ĐỂ ĐƯỢC HỖ TRỢ" : "");

                dataPrint = dataPrint.Replace("[UnitPriceFormatNumber]", string.Format("{0:N0}", receipt.TotalAmount)); // số tiền đã thanh toán , ví dụ : 200,000
                dataPrint = dataPrint.Replace("[UnitPriceNumberToVietnameseText]", StringHelper.ConvertNumberToVietnameseText(Math.Round(receipt.TotalAmount, MidpointRounding.AwayFromZero))); // số tiền đã thanh toán , ví dụ : 200,000 -> hai trăm ngàn
                dataPrint = dataPrint.Replace("[IsVisibility]", receipt.TotalAmount > 0 ? "true" : "false"); // nếu false thì ẩn đòng đó

                // thông tin bảo hiểm y tế
                dataPrint = dataPrint.Replace("[ExpiredInsurance]", receipt.Customer?.HealthInsuranceExpiredDate ?? "-"); // ngày hết hạn bảo hiểm
                dataPrint = dataPrint.Replace("[FromDateInsurance]", receipt.Customer?.HealthInsuranceFromDate ?? "-"); // ngày bắt đầu bảo hiểm
                dataPrint = dataPrint.Replace("[RegisterPlaceIDInsurance]", receipt.Customer?.HealthInsurancePlaceId ?? "-"); // nơi đăng ký bảo hiểm
                dataPrint = dataPrint.Replace("[RegisterPlaceNameInsurance]", receipt.Customer?.HealthInsurancePlace ?? "-"); // nơi đăng ký bảo hiểm

                dataPrint = dataPrint.Replace("[CertificateNumber]", "-");

                string statusHanlder = receipt.Register?.HealthInsurance == 0 ? paymentStatus : paymentStatus + "_INSURANCE";

                dataPrint = dataPrint.Replace("[BirthDay]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("dd/MM") ?? "-");

                string careerCurrentId = receipt.Customer?.CareerId ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(careerCurrentId))
                {
                    var careerCurrent = await mediator.Send(new GetCareers(), cancellationToken);
                    if (careerCurrent.Success && careerCurrent.Data?.Count > 0)
                        dataPrint = dataPrint.Replace("[CareerCurrent]", careerCurrent.Data.FirstOrDefault(x => x.Id == careerCurrentId)?.Name);      // tên nghề nghiệp hệ thống
                }

                string? statusPaymentHandle = hospitalMetadatasCache
                    .FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterStatusReceipt)?.Value;
                dataPrint = dataPrint.Replace("[StatusPaymentHandle]", string.IsNullOrWhiteSpace(statusPaymentHandle)
                    ? "-"
                    : PrintHelper.GetPaymentStatusJsonMapDesc(statusHanlder, statusPaymentHandle)); //  in hoa khoa

                dataPrint = dataPrint.Replace("[IsVisibilityQR]", !string.IsNullOrEmpty(receipt.QrCode) ? "true" : "false"); //// nếu false thì ẩn đòng đó
                dataPrint = dataPrint.Replace("[QrCodes]", receipt.QrCode ?? ""); // nếu false thì ẩn đòng đó

                var metata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, "healthcare_service_tiers", cancellationToken: cancellationToken);
                if (metata is not null && !string.IsNullOrEmpty(metata.Value))
                {
                    var tuyenMap = JsonConvert.DeserializeObject<List<GetHealthcareServiceTierDto>>(metata.Value ?? string.Empty);
                    string healthcareTier = string.Empty;
                    if (tuyenMap is not null && tuyenMap.Count > 0)
                    {
                        string healthcareServiceTierId = receipt.Register?.HealthcareServiceTierId ?? string.Empty;
                        healthcareTier = tuyenMap.FirstOrDefault(x => x.Id == healthcareServiceTierId)?.Name ?? "-";
                    }
                    dataPrint = dataPrint.Replace("[HealthcareServiceTier]", healthcareTier); //  in thông tin tuyến
                }

                if (receipt.Register != null && receipt.Register.PaymentType == "qr")
                {
                    receipt.Register.PaymentType = "cash";
                    databaseService.Receipts.Update(receipt);
                    await databaseService.SaveChangesAsync(cancellationToken);
                }
                // gói khám sức khỏe
                if (dataPrint.Contains("HEALTH_PACKAGE_SERVICE"))
                {
                    var details = await databaseService.RegisterDetails
                        .Where(x => x.RegisterNumber == receipt.RegisterNumber && x.HealthServiceId == "Technical")
                        .ToListAsync(cancellationToken);
                    var template = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, code: "HEALTH_PACKAGE", cancellationToken: cancellationToken);
                    if (template is null || string.IsNullOrEmpty(template.Value))
                    {
                        result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu in dịch vụ sức khỏe", ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    dataPrint = ReplaceServices(dataPrint, receipt.Register, details ?? [], template.Value, "HEALTH_PACKAGE_SERVICE");
                }
                // nhiều dịch vụ
                if (dataPrint.Contains("MULTI_SERVICE"))
                {
                    var details = await databaseService.RegisterDetails
                        .Where(x => x.RegisterNumber == receipt.RegisterNumber)
                        .ToListAsync(cancellationToken);
                    var template = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, code: "MULTI_SERVICE", cancellationToken: cancellationToken);
                    if (template is null || string.IsNullOrEmpty(template.Value))
                    {
                        result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu in nhiều dịch vụ", ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    dataPrint = ReplaceServices(dataPrint, null, details ?? [], template.Value, "MULTI_SERVICE");
                }

                result.Set(true, ErrorConstant.SUCCESS, dataPrint);
                return result;
            }
            else
            {
                result.Set(false, RegisterConstant.NotFound, ErrorTypeEnum.MediPayError);
            }

            return result;
        }

        public static string GetPaymentStatusDesc(string paymentStatus)
        {
            return paymentStatus switch
            {
                PaymentConstant.FailPayment => "Thất bại",
                PaymentConstant.Success => "Thành công",
                PaymentConstant.Partial => "Thanh toán một phần",
                _ => "Chưa thanh toán"
            };
        }

        private static string ReplaceServices(string dataPrint, Register? register, List<RegisterDetail> registerDetails, string template, string type)
        {
            var replacementContent = new StringBuilder();
            if (register != null)
            {
                var replacementpackage = template;
                replacementpackage = replacementpackage.Replace("[ServiceName]", register.ServiceName ?? "-");
                replacementpackage = replacementpackage.Replace("[Quantity]", "1");
                replacementpackage = replacementpackage.Replace("[Total]", register.TotalAmount.ToString());
                replacementpackage = replacementpackage.Replace("[ExaminationLocation]", register.ExaminationLocation ?? "-");
                replacementContent.Append(replacementpackage);
            }

            foreach (var detail in registerDetails)
            {
                var replacement = template;
                replacement = replacement.Replace("[ServiceName]", detail.ServiceName.ToString());
                replacement = replacement.Replace("[Quantity]", detail.Quantity.ToString());
                replacement = replacement.Replace("[Total]", detail.TotalAmount.ToString());
                replacement = replacement.Replace("[STT]", detail.QueueNumber.ToString());
                replacement = replacement.Replace("[ExaminationLocation]", detail.ExaminationLocation);
                replacement = replacement.Replace("[Clinic]", detail.Clinic);
                replacementContent.Append(replacement);
            }
            return dataPrint.Replace(type, replacementContent.ToString());
        }
    }
}
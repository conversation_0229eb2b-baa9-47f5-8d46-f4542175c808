﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ReceiptLogic.Queries
{
    public class GetReceipt : IRequest<BaseCommandResultWithData<Receipt>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetReceiptHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<GetReceipt, BaseCommandResultWithData<Receipt>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<Receipt>> Handle(
            GetReceipt request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Receipt>();

            var receipt = await databaseService.Receipts
                .FirstOrDefaultAsync(x => x.Number == request.Number, cancellationToken: cancellationToken);

            if (receipt is null)
            {
                result.Set(false, ReceiptConstant.NotFound);
            }
            else
            {
                result.Set(true, ReceiptConstant.Ok, receipt);
            }

            return result;
        }
    }
}

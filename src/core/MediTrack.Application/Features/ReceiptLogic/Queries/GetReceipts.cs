﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ReceiptLogic.Queries
{
    public class GetReceipts : IRequest<BaseCommandResultWithData<IEnumerable<Receipt>>>
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? InStatuses { get; set; }
        public List<string>? InHealthServiceIds { get; set; }
    }

    public class GetReceiptsHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<GetReceipts, BaseCommandResultWithData<IEnumerable<Receipt>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<IEnumerable<Receipt>>> Handle(
            GetReceipts request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<Receipt>>();

            request.FromDate ??= new DateTime(2012, 01, 01);
            request.ToDate ??= DateTime.UtcNow;

            var data = await databaseService.
                Receipts.Where(x => x.ReceiptDate >= request.FromDate &&
                                    x.ReceiptDate <= request.ToDate)
                .ToListAsync(cancellationToken: cancellationToken);

            return result;
        }
    }
}

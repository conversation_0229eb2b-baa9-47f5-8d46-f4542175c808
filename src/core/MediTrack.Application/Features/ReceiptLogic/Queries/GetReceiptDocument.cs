﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Helpers;
using Microsoft.EntityFrameworkCore;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.ReceiptLogic.Queries
{
    public class GetReceiptDocument
        : IRequest<BaseCommandResultWithData<ReceiptDocumentDto>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetReceiptDocumentHandler(IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetReceiptDocument, BaseCommandResultWithData<ReceiptDocumentDto>>
    {
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<ReceiptDocumentDto>> Handle(
            GetReceiptDocument request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<ReceiptDocumentDto>();

            //1. Lấy thông tin phiếu thu từ database
            var receipt = await databaseService.Receipts
                .Include(x => x.Customer)
                .Include(x => x.Register)
                .Include(x => x.Hospital)
                .FirstOrDefaultAsync(x => x.Number == request.Number, cancellationToken: cancellationToken);

            if (receipt is null)
            {
                result.Set(false, ReceiptConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentStatus = string.Empty;
            string invoiceInfoRef = string.Empty;

            //Không có bảo hiểm y tế, call sang HIS để check trạng thái thanh toán
            if (receipt.Register?.HealthInsurance != 1 && receipt.Status != ReceiptConstant.STATUS_PAID)
            {
                (_, _, _, PaymentLogic.Dtos.PaymentStatusDto resPayment) = await hisService.CheckPaymentStatus(receipt?.RefNo ?? string.Empty);
                paymentStatus = resPayment.PaymentStatus;
                invoiceInfoRef = resPayment.InvoiceInfoRef;
            }
            else
            {
                paymentStatus = PaymentConstant.Success;
            }

            var customerHospital = await databaseService.CustomerHospitals
                .FirstOrDefaultAsync(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId
                && x.CustomerId == receipt!.CustomerId, cancellationToken: cancellationToken);

            var data = new ReceiptDocumentDto()
            {
                RegisterNumber = receipt!.Register?.RefNo ?? string.Empty,
                ReceiptNumber = receipt.RefNo ?? string.Empty,
                CustomerName = receipt.Customer?.GetFullName()?.ToUpper() ?? string.Empty,
                DateOfBirth = receipt.Customer?.DateOfBirth,
                Gender = receipt.Customer?.Sex ?? string.Empty,
                CompanyName = string.Empty,
                PatientCode = !string.IsNullOrEmpty(receipt.Register?.PatientCode)
                    ? receipt.Register.PatientCode : (customerHospital?.PatientCode ?? string.Empty),
                Address = receipt.Customer?.Address ?? string.Empty,
                TaxNumber = string.Empty,
                ServiceName = receipt.Register?.ServiceName ?? string.Empty,
                Quantity = 1,
                UnitPrice = receipt.TotalAmount,
                Total = receipt.TotalAmount,
                HospitalCode = receipt.Register?.Hospital?.Id ?? string.Empty,
                HospitalTaxCode = receipt.Register?.Hospital?.TaxCode ?? string.Empty,
                HospitalName = receipt.Register?.Hospital?.Name ?? string.Empty,
                LogoUrl = string.Empty,
                CreateAt = receipt.CreatedAt,
                Priority = receipt.Register?.QueueNumberPriority == true ? 1 : 0,
                Number = receipt.Register?.QueueNumber ?? string.Empty,
                Clinic = receipt.Register?.Clinic ?? string.Empty,
                RefDocNo = receipt.Register?.RefDocNo ?? string.Empty,
                ExaminationLocation = receipt.Register?.ExaminationLocation ?? string.Empty,
                RateOfInsurance = receipt?.Register?.RateOfInsurance ?? string.Empty,
                PaymentStatus = paymentStatus,
                InvoiceInfoRef = invoiceInfoRef,
                LinkCode = receipt?.Register?.LinkCode ?? string.Empty,
                RegisterRefNo = receipt?.Register?.RefNo ?? string.Empty,
                HealthInsuranceNo = receipt?.Customer?.HealthInsuranceNo ?? string.Empty,
                CustomerAge = receipt?.Customer?.DateOfBirth.GetAge() ?? string.Empty
            };

            result.Set(true, ReceiptConstant.Ok, data);

            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ReceiptLogic.Commands
{
    public class CreateReceipt : IRequest<BaseCommandResultWithData<Receipt>>
    {
        public string CustomerKey { get; set; } = string.Empty;
        public string? RegisterNumber { get; set; }
    }

    public class CreateReceiptHandler(ICachedService cachedService,
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService,
        IIdentityService identityService) : IRequestHandler<CreateReceipt, BaseCommandResultWithData<Receipt>>
    {
        private readonly ICachedService cachedService = cachedService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResultWithData<Receipt>> Handle(CreateReceipt request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Receipt>();

            var customer = await databaseService.Customers
                .FirstOrDefaultAsync(x => x.Id == request.CustomerKey, cancellationToken: cancellationToken);

            customer ??= await databaseService.Customers
                    .FirstOrDefaultAsync(x => x.IdentityNo == request.CustomerKey, cancellationToken: cancellationToken);

            if (customer is null)
            {
                result.Set(false, CustomerConstant.InvalidCustomer, ErrorTypeEnum.MediPayError);
                return result;
            }

            //0. Lấy phiếu đăng ký
            var register = await databaseService.Registers
                .FirstOrDefaultAsync(x => x.Number == request.RegisterNumber, cancellationToken: cancellationToken);
            if (register is null)
            {
                result.Set(false, RegisterConstant.InvalidRegister, ErrorTypeEnum.MediPayError);
                return result;
            }

            var receiptExisted = await databaseService.Receipts
                .FirstOrDefaultAsync(x => x.RegisterNumber == request.RegisterNumber, cancellationToken: cancellationToken);

            if (receiptExisted is not null)
            {
                result.Set(true, RegisterConstant.SaveChangesSuccess, receiptExisted);
                return result;
            }

            //1. Tạo và lưu phiếu thu
            var receipt = new Receipt
            {
                Number = IdentityHelper.Guid(15),
                CustomerId = customer.Id,
                TotalAmount = register.TotalAmount,
                ReceiptDate = DateTime.UtcNow,
                RegisterNumber = register.Number,
                Status = ReceiptConstant.STATUS_NEW,
                HospitalId = register.HospitalId,
                DeviceId = register.DeviceId,
            };

            //2. Tích hợp phiếu thu với HIS
            (bool createResult, string createMessage, ErrorTypeEnum errorType, string receiptNo, string qrCode) = await hisService.CreateReceipt(register);

            if (createResult)
            {
                receipt.RefNo = receiptNo;
                receipt.QrCode = qrCode;
                databaseService.Receipts.Add(receipt);
            }
            else
            {
                result.Set(false, createMessage, errorType);
                return result;
            }

            var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (saveResult > 0)
            {
                result.Set(true, ReceiptConstant.SaveChangesSuccess, receipt);
            }
            else
            {
                result.Set(false, ReceiptConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.ReceiptLogic.Commands
{
    public class UpdateReceiptPaymentStatus : IRequest<BaseCommandResult>
    {
        public string Signature { get; set; } = string.Empty;
        public UpdateReceiptPaymentStatusDto Data { get; set; } = new();
    }

    public class UpdateReceiptPaymentStatusHandler(IDatabaseService databaseService, IHisServiceHelper hisServiceHelper)
        : IRequestHandler<UpdateReceiptPaymentStatus, BaseCommandResult>
    {
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResult> Handle(
            UpdateReceiptPaymentStatus request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            try
            {
                //1. Check hospital infor
                var hospital = await hisServiceHelper.GetHospital(request.Data.HospitalId, databaseService, cancellationToken);
                if(hospital is null)
                {
                    result.Set(false, HospitalConstant.InvalidHospital);
                    return result;
                }

                //2. Check signature
                var plainText = request.Data.ReceiptNumber + request.Data.TransactionId;
                var signature = SignatureHelper.HmacSha256Hash(plainText, hospital.SecretKey);
                
                if(signature != request.Signature)
                {
                    result.Set(false, ReceiptConstant.InvalidSignature);
                    return result;
                }

                //3. Update receipt
                var receipt = await databaseService.Receipts
                    .FirstOrDefaultAsync(x => x.RefNo == request.Data.ReceiptNumber &&
                                              x.HospitalId == request.Data.HospitalId);

                if(receipt is null)
                {
                    result.Set(false, ReceiptConstant.InvalidReceipt);
                    return result;
                }

                if(receipt.TotalAmount != request.Data.Amount)
                {
                    result.Set(false, ReceiptConstant.InvalidAmount);
                    return result;
                }

                receipt.Status = ReceiptConstant.STATUS_PAID;
                receipt.UpdatedAt = DateTime.UtcNow;

                var payment = new Payment()
                {
                    Id = IdentityHelper.Guid(10),
                    PaymentDate = DateTime.UtcNow,
                    PaymentAmount = request.Data.Amount,
                    ReceiptNumber = receipt.Number,
                    CreatedAt = DateTime.UtcNow,
                    Status = PaymentConstant.Success,
                    QrCode = receipt.QrCode,
                    RefNo = receipt.RefNo,
                    RefTran = request.Data.TransactionId,
                    RefDesc = request.Data.PaidDescription,
                    IsHisGenQr = hospital.IsGenQR,
                    HospitalId = receipt.HospitalId
                };

                databaseService.Receipts.Update(receipt);
                databaseService.Payments.Add(payment);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                if(saveResult > 0)
                {
                    result.Set(true, ReceiptConstant.SaveChangesSuccess);
                }
                else
                {
                    result.Set(false, ReceiptConstant.SaveChangesError);
                }
            }
            catch(Exception ex)
            {
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

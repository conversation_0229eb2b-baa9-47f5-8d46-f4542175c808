using FluentValidation;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ReceiptLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.ReceiptLogic.Commands
{
    public class PushReceiptInfoCommand : IRequest<BaseCommandResult>
    {
        public PushReceiptInfoRequestDataDto Data { get; set; } = new();
    }

    public class PushReceiptInfoValidator
        : AbstractValidator<PushReceiptInfoCommand>
    {
        public PushReceiptInfoValidator()
        {
            RuleFor(x => x.Data.CompanyAddress).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Địa chỉ công ty không được để trống");

            RuleFor(x => x.Data.CompanyName).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Tên công ty không được để trống");

            RuleFor(x => x.Data.TaxNo).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Mã số thuế không được để trống");

            RuleFor(x => x.Data.Number).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Mã hóa đơn không được để trống");

            RuleFor(x => x.Data.Amount).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Số tiền không được để trống");
        }
    }
    public class PushReceiptInfoCommandHandler : IRequestHandler<PushReceiptInfoCommand, BaseCommandResult>
    {
        private readonly IHisServiceHelper hisServiceHelpers;
        private readonly IHisService hisService;
        private readonly ICurrentHospitalService currentHospitalService;
        private readonly IDatabaseService databaseService;
        private readonly IHttpClientFactory httpClientFactory;

        public PushReceiptInfoCommandHandler(
            IHisServiceHelper hisServiceHelpers,
            IDatabaseService databaseService,
            ICurrentHospitalService currentHospitalService,
            IHttpClientFactory httpClientFactory)
        {
            this.hisServiceHelpers = hisServiceHelpers;
            this.hisService = currentHospitalService.HisService;
            this.currentHospitalService = currentHospitalService;
            this.databaseService = databaseService;
            this.httpClientFactory = httpClientFactory;
        }

        public async Task<BaseCommandResult> Handle(PushReceiptInfoCommand request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var receipt = await databaseService.Receipts.FirstOrDefaultAsync(x => x.Number == request.Data.Number, cancellationToken);
            if (receipt == null)
            {
                result.Set(false, "Hóa đơn không tồn tại");
                return result;
            }

            var customerHospital = await databaseService.CustomerHospitals.FirstOrDefaultAsync(x => x.CustomerId == receipt.CustomerId && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
            if (customerHospital == null)
            {
                result.Set(false, "Bệnh viện khách hàng không tồn tại");
                return result;
            }

            var dto = new PushReceiptInfoRequestDto
            {
                CompanyName = request.Data.CompanyName,
                CompanyAddress = request.Data.CompanyAddress,
                TaxNo = request.Data.TaxNo,
                PatientCode = customerHospital.PatientCode,
                RefNo = receipt.RefNo!,
                Email = request.Data.Email,
                PhoneNumber = request.Data.PhoneNumber,
                Amount = request.Data.Amount
            };

            (bool isSuccess, string message, ErrorTypeEnum errorType, PushReceiptInfoResponseDto response) = await hisService.PushReceiptInfo(dto);

            if (!isSuccess)
            {
                result.Set(false, message, errorType);
                return result;
            }

            Log.Information("PushReceiptInfo successful: {Response}", response);
            result.Set(true, PaymentConstant.Ok);
            return result;
        }
    }
}

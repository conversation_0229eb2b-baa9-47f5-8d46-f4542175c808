﻿using System.Text.Json.Serialization;

namespace MediTrack.Application.Features.PaymentLogic.Dtos
{
    public class PaymentStatusDto
    {
        public string PaymentStatus { get; set; } = string.Empty;
        public string Number { get; set; } = string.Empty;
        public string RefNo { get; set; } = string.Empty;
        public string InvoiceInfoRef { get; set; } = string.Empty;
        public string IpnStatus { get;set ; } = string.Empty;
        public decimal PaidAmount { get; set; } 
    }
}

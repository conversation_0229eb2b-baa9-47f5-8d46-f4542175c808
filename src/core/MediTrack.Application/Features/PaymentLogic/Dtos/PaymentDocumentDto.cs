﻿using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.PaymentLogic.Dtos
{
    public class PaymentDocumentDto
    {
        public string InvoiceCode { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;
        public string PaymentAt { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string DateOfBirth { get; set; } = string.Empty;
        public string Clinic { get; set; } = string.Empty;
        public string AccountHolderName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string DetailsOfPayment { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;

        public string HospitalName { get; set; } = string.Empty;
        public string HospitalTaxCode { get; set; } = string.Empty;
        public string HospitalCode { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public string ServiceUnit { get; set; } = string.Empty;
        public string ServiceAmount { get; set; } = string.Empty;
        public string ServiceTotalAmount { get; set; } = string.Empty;

        public string LinkCode { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string RateOfInsurance { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string PaymentStatus { get; set; } = string.Empty;
        public string RegisterRefNo { get; set; } = string.Empty;
        public string HealthInsuranceNo { get; set; } = string.Empty;
        public string CustomerAge { get; set; } = string.Empty;
        public int Quantity { get; set; } = 1;
        public string PaymentStatusDesc
        {
            get => this.PaymentStatus switch
                {
                    PaymentConstant.FailPayment => "Chưa thu",
                    PaymentConstant.Success => "Đã thu",
                    PaymentConstant.Partial  => "Đã thu một phần",
                    _ => "Chưa thu"
                };
        }
        public DateTime? CreateAt { get; set; }
        public string InvoiceInfoRef { get; set; } = string.Empty;
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.PaymentLogic.Dtos
{
    public class QrPaymentDto
    {
        public string Number { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public decimal? Amount { get; set; }
        public string TransactionDescription { get; set; } = string.Empty;
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using MediTrack.Domain.Helpers;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.PaymentLogic.Queries
{
    public class GetPaymentDocument : IRequest<BaseCommandResultWithData<PaymentDocumentDto>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetPaymentDocumentHandler(IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetPaymentDocument, BaseCommandResultWithData<PaymentDocumentDto>>
    {
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<PaymentDocumentDto>> Handle(
            GetPaymentDocument request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<PaymentDocumentDto>();

            //1. Lấy thông tin phiếu thu từ database
            var payment = await databaseService.Payments
                .Include(x => x.Receipt)
                .ThenInclude(x => x!.Customer)
                .Include(x => x.Receipt)
                .ThenInclude(x => x!.Register)
                .ThenInclude(x => x!.Hospital)
                .FirstOrDefaultAsync(x => x.Id == request.Number, cancellationToken: cancellationToken);

            if (payment is null)
            {
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var customerHospital = payment.Receipt != null
                ? await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.CustomerId == payment.Receipt.CustomerId
                    && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken: cancellationToken)
                : null;

            var receipt = payment.Receipt;
            string paymentStatus = string.Empty;
            string invoiceInfoRef = string.Empty;

            if (receipt?.Register?.HealthInsurance != 1 && payment.Status != PaymentConstant.Success)
            {
                (_, _, _, PaymentStatusDto resPayment) = await currentHospitalService.HisService.CheckPaymentStatus(payment.RefNo ?? string.Empty);
                paymentStatus = resPayment.PaymentStatus;
                invoiceInfoRef = resPayment.InvoiceInfoRef;
            }
            else
            {
                paymentStatus = PaymentConstant.Success;
            }

            var data = new PaymentDocumentDto()
            {
                InvoiceCode = receipt?.RefNo ?? string.Empty,
                Amount = payment.PaidAmount.ToString("N0"),
                PaymentAt = payment.PaymentDate.AddHours(7).ToString("dd/MM/yyyy HH:mm:ss"),
                CustomerName = receipt?.Customer?.GetFullName()?.ToUpper() ?? string.Empty,
                DateOfBirth = receipt?.Customer?.DateOfBirth.GetValueOrDefault().ToString("dd/MM/yyyy") ?? string.Empty,
                Clinic = receipt?.Register?.Clinic ?? string.Empty,
                AccountHolderName = receipt?.Register?.Hospital?.AccountHolderName ?? string.Empty,
                BankName = receipt?.Register?.Hospital?.BankName ?? string.Empty,
                AccountNumber = receipt?.Register?.Hospital?.AccountNumber ?? string.Empty,
                LogoUrl = string.Empty,
                DetailsOfPayment = payment.RefDesc ?? string.Empty,
                ServiceName = receipt?.Register?.ServiceName ?? string.Empty,
                ServiceAmount = receipt?.Register?.SubTotalAmount.ToString("N0") ?? string.Empty,
                ServiceTotalAmount = receipt?.Register?.TotalAmount.ToString("N0") ?? string.Empty,
                HospitalCode = receipt?.HospitalId ?? string.Empty,
                HospitalTaxCode = receipt?.Register?.Hospital?.TaxCode ?? string.Empty,
                HospitalName = receipt?.Register?.Hospital?.Name ?? string.Empty,
                RefDocNo = receipt?.Register?.RefDocNo ?? string.Empty,
                LinkCode = receipt?.Register?.LinkCode ?? string.Empty,
                ExaminationLocation = receipt?.Register?.ExaminationLocation ?? string.Empty,
                RateOfInsurance = receipt?.Register?.RateOfInsurance ?? string.Empty,
                PaymentStatus = paymentStatus,
                CreateAt = payment.CreatedAt,
                PatientCode = !string.IsNullOrEmpty(receipt?.Register?.PatientCode)
                    ? receipt.Register.PatientCode
                    : (customerHospital?.PatientCode ?? string.Empty),
                InvoiceInfoRef = invoiceInfoRef,
                RegisterRefNo = receipt?.Register?.RefNo ?? string.Empty,
                HealthInsuranceNo = receipt?.Customer?.HealthInsuranceNo ?? string.Empty,
                CustomerAge = receipt?.Customer?.DateOfBirth.GetAge() ?? string.Empty,
                Quantity = 1,
            };

            result.Set(true, PaymentConstant.Success, data);

            return result;
        }
    }
}

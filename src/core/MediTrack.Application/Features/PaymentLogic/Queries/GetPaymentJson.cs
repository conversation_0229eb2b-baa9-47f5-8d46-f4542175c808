﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using MediTrack.Domain.Helpers;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Ultils.Helpers;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Domain;
using MediTrack.Application.Features.CareerLogic.Queries;
using Newtonsoft.Json;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using System.Text;
using Serilog;
using MediTrack.Domain.Enums;
using MediTrack.Application.Features.HospitalLogic.Queries;

namespace MediTrack.Application.Features.PaymentLogic.Queries
{
    public class GetPaymentJson : IRequest<BaseCommandResultWithData<string>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetPaymentJsonHandler(IDatabaseService databaseService,
       ICurrentHospitalService currentHospitalService,
       IHospitalMetadataRepository hospitalMetadataRepository,
       IMediator mediator) : IRequestHandler<GetPaymentJson, BaseCommandResultWithData<string>>
    {
        public async Task<BaseCommandResultWithData<string>> Handle(GetPaymentJson request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<string>();

            //1. Lấy thông tin phiếu thu từ database
            var payment = await databaseService.Payments
                .Include(x => x.Receipt!.Customer)
                .Include(x => x.Receipt!.Register)
                .Include(x => x.Hospital)
                .FirstOrDefaultAsync(x => x.Id == request.Number, cancellationToken);

            if (payment is null)
            {
                result.Set(false, ErrorConstant.NOT_FOUND_DATA, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                Log.Error("{LogPrefix} Not found payment by number {Number}", currentHospitalService.LogPrefix, request.Number);
                return result;
            }

            var customerHospital = payment.Receipt != null
                ? await databaseService.CustomerHospitals
                    .FirstOrDefaultAsync(x => x.CustomerId == payment.Receipt.CustomerId
                    && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken: cancellationToken)
                : null;

            var receipt = payment.Receipt;
            string paymentStatus = string.Empty;
            string invoiceInfoRef = "Chưa phát hành HĐĐT";

            if (receipt is null)
            {
                result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy phiếu thu", ErrorTypeEnum.MediPayError);
                Log.Error("{LogPrefix} Not found receipt by payment number {Number}", currentHospitalService.LogPrefix, request.Number);
                return result;
            }

            if (receipt.Register?.HealthInsurance != 1 && payment.Status != PaymentConstant.Success)
            {
                (_, _, ErrorTypeEnum errorType, PaymentStatusDto resPayment) = await currentHospitalService.HisService.CheckPaymentStatus(payment.RefNo ?? string.Empty);
                paymentStatus = resPayment.PaymentStatus;
                invoiceInfoRef = string.IsNullOrEmpty(resPayment.InvoiceInfoRef) ? "Chưa phát hành HĐĐT" : resPayment.InvoiceInfoRef;
            }
            else
            {
                paymentStatus = PaymentConstant.Success;
            }

            var hospitalMetadatasCache = await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(currentHospitalService.CurrentHospital.HospitalId, cancellationToken);

            if (hospitalMetadatasCache.Count == 0)
            {
                result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu.", ErrorTypeEnum.MediPayError);
                Log.Error("{LogPrefix} Not found hospital metadata by number {Number}", currentHospitalService.LogPrefix, request.Number);
                return result;
            }

            var hospitalMetadatas = hospitalMetadatasCache
                                .Where(x => x.Code == HospitalMetadataConstant.PrinterPayment
                                        || x.Code == HospitalMetadataConstant.PrinterPaymentInsurance
                                        || x.Code == HospitalMetadataConstant.PrinterPaymentHealthPackage
                                        || x.Code == HospitalMetadataConstant.PrinterPaymentHealthServiceN)
                                .ToList();
            if (hospitalMetadatas.Count == 0)
            {
                result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu", ErrorTypeEnum.MediPayError);
                Log.Error("{LogPrefix} Not found hospital metadata by number {Number}", currentHospitalService.LogPrefix, request.Number);
                return result;
            }

            var hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterPaymentInsurance);

            // nếu không phải bảo hiểm hoặc không có mẫu in phiếu thu bảo hiểm thì lấy mẫu in phiếu thu thông thường
            if (receipt.Register?.HealthInsurance != 1 || hospitalMetadata is null)
            {
                hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterPayment);
            }

            if (receipt.Register?.RegisterType == "GOI_KHAM")
            {
                hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterPaymentHealthPackage);
            }

            if (receipt.Register?.RegisterType == "DANG_KY_N")
            {
                hospitalMetadata = hospitalMetadatas.FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterPaymentHealthServiceN);
            }

            if (hospitalMetadata is null)
            {
                result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu thu", ErrorTypeEnum.MediPayError);
                Log.Error("{LogPrefix} Not found hospital metadata by number {Number}", currentHospitalService.LogPrefix, request.Number);
                return result;
            }

            var dataPrint = hospitalMetadata.Value!;

            dataPrint = dataPrint.Replace("[InvoiceCode]", receipt.RefNo ?? "-"); // mã hóa đơn
            dataPrint = dataPrint.Replace("[UrlLogo]", receipt.Register?.Hospital?.LogoUrl ?? "-"); // logo bệnh viện
            dataPrint = dataPrint.Replace("[HospitalName]", receipt.Register?.Hospital?.Name ?? "-"); // Tên bệnh viện
            dataPrint = dataPrint.Replace("[HospitalCode]", receipt.Register?.Hospital?.Id ?? "-");  // Mã bệnh viện
            dataPrint = dataPrint.Replace("[PatientCode]", !string.IsNullOrEmpty(receipt.Register?.PatientCode)
                    ? receipt.Register.PatientCode
                    : customerHospital?.PatientCode ?? "-"); // mã bệnh nhân
            dataPrint = dataPrint.Replace("[RefDocno]", receipt.Register?.RefDocNo ?? "-"); // Code tiếp nận His (Đà Nẵng)
            dataPrint = dataPrint.Replace("[CustomerName]", receipt.Customer?.GetFullName().ToUpper() ?? "-"); // tên khách hàng
            dataPrint = dataPrint.Replace("[CustomerNameFmt]", PrintHelper.ServiceNameConvert(receipt.Customer?.GetFullName().ToUpper() ?? "-", 18)); // tên khách hàng
            dataPrint = dataPrint.Replace("[PaymentStatus]", PrintHelper.GetPaymentStatusDesc(paymentStatus, receipt.Register?.HealthInsurance == 1)); // trạng thái thanh toán
            dataPrint = dataPrint.Replace("[DateOfBirth]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("dd/MM/yyyy") ?? "-"); // ngày tháng năm sinh khách hàng
            dataPrint = dataPrint.Replace("[YearOfBirth]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("yyyy") ?? "-"); // ngày tháng năm sinh khách hàng
            dataPrint = dataPrint.Replace("[Gender]", receipt.Customer?.Sex ?? "-"); // giới tính khách hàng
            dataPrint = dataPrint.Replace("[ExaminationDate]", receipt.Register?.RegisterAt.GetValueOrDefault().AddHours(7).ToString("dd/MM/yyyy") ?? "-"); // ngày khám
            dataPrint = dataPrint.Replace("[RateOfInsurance]", receipt.Register?.RateOfInsurance ?? "-"); // tỉ lệ bảo hiểm
            dataPrint = dataPrint.Replace("[STT]", receipt.Register?.QueueNumber ?? "-"); // số thứ tự
            dataPrint = dataPrint.Replace("[InsuranceStatus]", receipt.Register?.HealthInsurance == 1 ? "Có" : "Không"); // Có bảo hiểm không
            dataPrint = dataPrint.Replace("[MedicalTreatmentCategoryName]", receipt.Register?.MedicalTreatmentCategoryName ?? "-"); // Tên đối tượng khám chữa bệnh
            dataPrint = dataPrint.Replace("[ExaminationLocation]", receipt.Register?.ExaminationLocation ?? "-"); // vị trí khám (Đà Nẵng)
            dataPrint = dataPrint.Replace("[ExaminationHour]", receipt.Register?.RegisterAt.GetValueOrDefault().AddHours(7).ToString("HH:mm") ?? "-"); // giờ khám
            dataPrint = dataPrint.Replace("[DeviceId]", currentHospitalService.KioskId); // mã kiosk
            dataPrint = dataPrint.Replace("[PrintedAt]", DateTimeHelper.GetCurrentLocalDateTime().ToString("dd/MM/yyyy HH:mm")); // ngày in
            dataPrint = dataPrint.Replace("[ReceiptTime]", DateTimeHelper.GetCurrentLocalDateTime().AddHours(23).ToString("HH'h ngày' dd/MM/yyyy")); // hạn xuất hóa đơn
            dataPrint = dataPrint.Replace("[Priority]", receipt.Register?.QueueNumberPriority == true ? "ƯU TIÊN" : "THƯỜNG");
            dataPrint = dataPrint.Replace("[PriorityWithYesNo]", receipt.Register?.QueueNumberPriority == true ? "CÓ" : "KHÔNG");

            dataPrint = dataPrint.Replace("[PriorityWithEmpty]", receipt.Register?.QueueNumberPriority == true ? "ƯU TIÊN" : "");
            dataPrint = dataPrint.Replace("[InvoiceInfoRef]", invoiceInfoRef); // mã hóa đơn
            dataPrint = dataPrint.Replace("[RegisterRefNo]", string.IsNullOrEmpty(receipt.Register?.RefNo) ? "-" : receipt.Register?.RefNo ?? "-"); // mã tiếp nhận
            dataPrint = dataPrint.Replace("[ServiceTotalAmount]", receipt.Register?.TotalAmount.ToString("N0") ?? "-"); // tổng tiền
            dataPrint = dataPrint.Replace("[Clinic]", receipt.Register?.Clinic ?? "-"); // phòng khám
            dataPrint = dataPrint.Replace("[ClinicGroup]", receipt.Register?.ClinicGroup ?? "-"); // tên nhóm phòng khám


            dataPrint = dataPrint.Replace("[PaidAmount]", payment.PaidAmount.ToString("N0")); // số tiền đã thanh toán
            dataPrint = dataPrint.Replace("[AccountHolderName]", receipt.Register?.Hospital?.AccountHolderName ?? "-"); // tên thụ hưởng ngân hàng
            dataPrint = dataPrint.Replace("[AccountNumber]", receipt.Register?.Hospital?.AccountNumber ?? "-"); // stk ngân hàng
            dataPrint = dataPrint.Replace("[BankName]", receipt.Register?.Hospital?.BankName ?? "-"); //Tên ngân hàng
            dataPrint = dataPrint.Replace("[ServiceName]", receipt.Register?.ServiceName ?? "-"); // tên dịch vụ
            dataPrint = dataPrint.Replace("[ServiceNameFmt]", PrintHelper.ServiceNameConvert(receipt.Register?.ServiceName ?? "-")); // tên dịch vụ
            dataPrint = dataPrint.Replace("[ServiceNameFmt40]", PrintHelper.ServiceNameConvert(receipt.Register?.ServiceName ?? "-", 40));
            dataPrint = dataPrint.Replace("[ServiceUnit]", receipt.Register?.SubTotalAmount.ToString("N0") ?? "-"); // đơn vị dịch vụ
            dataPrint = dataPrint.Replace("[ServiceAmount]", receipt.Register?.SubTotalAmount.ToString("N0") ?? "-"); // số lượng dịch vụ
            dataPrint = dataPrint.Replace("[DetailOfPayment]", payment.RefDesc ?? "-"); // nôi dung miêu tả trạng thái thanh toán (ĐÃ THU  - CHƯA THU)
            dataPrint = dataPrint.Replace("[PaymentAt]", payment.PaymentDate.AddHours(7).ToString("dd/MM/yyyy HH:mm:ss")); // ngày thanh toán
            dataPrint = dataPrint.Replace("[VatInvoiceIssueTimeLimit]", $"11h ngày {DateTimeHelper.GetCurrentLocalDateTime().AddDays(1).AddSeconds(-1):dd/MM/yyyy}"); // ngày thanh toán
            dataPrint = dataPrint.Replace("[Address]", receipt.Customer?.Address ?? "-"); // địa chỉ khách hàng
            dataPrint = dataPrint.Replace("[AddressFmt]", PrintHelper.AddressConvert(receipt.Customer?.Address ?? "-")); // địa chỉ khách hàng
            dataPrint = dataPrint.Replace("[ExpectedAppointmentAt]", receipt.Register?.ExpectedAppointmentAt.GetValueOrDefault().ToString("HH:mm") ?? "-"); // ngày hẹn khám
            dataPrint = dataPrint.Replace("[ExameType]", receipt.Register?.ExameType ?? "-"); // loại khám
            dataPrint = dataPrint.Replace("[ClinicUpper]", receipt.Register?.Clinic.ToUpper() ?? "-"); //  in hoa khoa
            dataPrint = dataPrint.Replace("[HealthInsuranceNo]", receipt.Register?.HealthInsurance == 0 ? "" : receipt.Customer?.HealthInsuranceNo?.ToUpper() ?? "-"); //  Số bảo hiểm y tế
            dataPrint = dataPrint.Replace("[CustomerPhoneNumber]", receipt.Customer?.Phone); //  Số điện thoại
            dataPrint = dataPrint.Replace("[StatusIpnMessage]", receipt.Register?.IsHisCreateFormSuccess == false
                ? "BỆNH VIỆN CHƯA GHI NHẬN THANH TOÁN, VUI LÒNG ĐẾN QUẦY THU TIỀN ĐỂ ĐƯỢC HỖ TRỢ" : "");

            dataPrint = dataPrint.Replace("[PaidAmountFormatNumber]", string.Format("{0:N0}", payment.PaidAmount)); // số tiền đã thanh toán , ví dụ : 200,000
            dataPrint = dataPrint.Replace("[PaidAmountNumberToVietnameseText]", StringHelper.ConvertNumberToVietnameseText(Math.Round(payment.PaidAmount, MidpointRounding.AwayFromZero))); 
            dataPrint = dataPrint.Replace("[IsVisibility]", payment.PaidAmount > 0 ? "true" : "false"); // nếu false thì ẩn đòng đó
            // thông tin bảo hiểm y tế
            dataPrint = dataPrint.Replace("[ExpiredInsurance]", receipt.Customer?.HealthInsuranceExpiredDate ?? "-"); // ngày hết hạn bảo hiểm
            dataPrint = dataPrint.Replace("[FromDateInsurance]", receipt.Customer?.HealthInsuranceFromDate ?? "-"); // ngày bắt đầu bảo hiểm
            dataPrint = dataPrint.Replace("[RegisterPlaceIDInsurance]", receipt.Customer?.HealthInsurancePlaceId ?? "-"); // nơi đăng ký bảo hiểm
            dataPrint = dataPrint.Replace("[RegisterPlaceNameInsurance]", receipt.Customer?.HealthInsurancePlace ??  "-"); // nơi đăng ký bảo hiểm
            dataPrint = dataPrint.Replace("[CertificateNumber]", "-");

            dataPrint = dataPrint.Replace("[BirthDay]", receipt.Customer?.DateOfBirth.GetValueOrDefault().ToString("dd/MM") ?? "-");

            string careerCurrentId = receipt.Customer?.CareerId ?? string.Empty;
            if (!string.IsNullOrWhiteSpace(careerCurrentId))
            {
                var careerCurrent = await mediator.Send(new GetCareers(), cancellationToken);
                if (careerCurrent.Success && careerCurrent.Data?.Count > 0)
                    dataPrint = dataPrint.Replace("[CareerCurrent]", careerCurrent.Data.FirstOrDefault(x => x.Id == careerCurrentId)?.Name);      // tên nghề nghiệp hệ thống
            }

            string statusHanlder = receipt.Register?.HealthInsurance == 0 ? paymentStatus : paymentStatus + "_INSURANCE";

            string? statusPaymentHandle = hospitalMetadatasCache
                .FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterStatusPayment)?.Value;
            dataPrint = dataPrint.Replace("[StatusPaymentHandle]", string.IsNullOrWhiteSpace(statusPaymentHandle)
                ? "-"
                : PrintHelper.GetPaymentStatusJsonMapDesc(statusHanlder, statusPaymentHandle)); //  in chỉnh  text status theo từng bệnh viện

            var metata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, "healthcare_service_tiers", cancellationToken: cancellationToken);
            if (metata is not null && !string.IsNullOrEmpty(metata.Value))
            {
                var tuyenMap = JsonConvert.DeserializeObject<List<GetHealthcareServiceTierDto>>(metata.Value ?? string.Empty);
                string healthcareTier = string.Empty;
                if (tuyenMap is not null && tuyenMap.Count > 0)
                {
                    string healthcareServiceTierId = receipt.Register?.HealthcareServiceTierId ?? string.Empty;
                    healthcareTier = tuyenMap.FirstOrDefault(x => x.Id == healthcareServiceTierId)?.Name ?? "-";
                }
                dataPrint = dataPrint.Replace("[HealthcareServiceTier]", healthcareTier); //  in thông tin tuyến
            }

            string statusHanlderByAmount = receipt.TotalAmount > 0 ? paymentStatus : paymentStatus + "_NOT_PAID";

            string? statusPaymentHandleByAmount = hospitalMetadatasCache
                .FirstOrDefault(x => x.Code == HospitalMetadataConstant.PrinterStatusPaymentByAmount)?.Value;
            dataPrint = dataPrint.Replace("[StatusPaymentHandleByAmount]", string.IsNullOrWhiteSpace(statusPaymentHandle)
                ? "-"
                : PrintHelper.GetPaymentStatusJsonMapDesc(statusHanlder, statusPaymentHandle)); //  in chỉnh  t

            if (receipt.Register != null && receipt.Register.PaymentType == "cash")
            {
                receipt.Register.PaymentType = "qr";
                databaseService.Receipts.Update(receipt);
                await databaseService.SaveChangesAsync(cancellationToken);
            }

            // gói khám sức khỏe
            if (dataPrint.Contains("HEALTH_PACKAGE_SERVICE"))
            {
                var details = await databaseService.RegisterDetails
                    .Where(x => x.RegisterNumber == receipt.RegisterNumber && x.HealthServiceId == "Technical")
                    .ToListAsync(cancellationToken);
                var template = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, code: "HEALTH_PACKAGE", cancellationToken: cancellationToken);
                if (template is null || string.IsNullOrEmpty(template.Value))
                {
                    result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu in dịch vụ sức khỏe", ErrorTypeEnum.MediPayError);
                    return result;
                }
                dataPrint = ReplaceServices(dataPrint, receipt.Register, details ?? [], template.Value, "HEALTH_PACKAGE_SERVICE");
            }
            // nhiều dịch vụ gói khám sức khỏe
            if (dataPrint.Contains("MULTI_SERVICE"))
            {
                var details = await databaseService.RegisterDetails
                    .Where(x => x.RegisterNumber == receipt.RegisterNumber)
                    .ToListAsync(cancellationToken);
                var template = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(receipt.HospitalId, code: "MULTI_SERVICE", cancellationToken: cancellationToken);
                if (template is null || string.IsNullOrEmpty(template.Value))
                {
                    result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu in nhiều dịch vụ", ErrorTypeEnum.MediPayError);
                    return result;
                }
                dataPrint = ReplaceServices(dataPrint, null, details ?? [], template.Value, "MULTI_SERVICE");
            }

            result.Set(true, ErrorConstant.SUCCESS, dataPrint);
            return result;
        }

        private static string ReplaceServices(string dataPrint, Register? register, List<RegisterDetail> registerDetails, string template, string type)
        {
            var replacementContent = new StringBuilder();
            if (register != null)
            {
                var replacementpackage = template;
                replacementpackage = replacementpackage.Replace("[ServiceName]", register.ServiceName ?? "-");
                replacementpackage = replacementpackage.Replace("[Quantity]", "1");
                replacementpackage = replacementpackage.Replace("[Total]", register.TotalAmount.ToString());
                replacementpackage = replacementpackage.Replace("[ExaminationLocation]", register.ExaminationLocation ?? "-");
                replacementContent.Append(replacementpackage);
            }

            foreach (var detail in registerDetails)
            {
                var replacement = template;
                replacement = replacement.Replace("[ServiceName]", detail.ServiceName.ToString());
                replacement = replacement.Replace("[Quantity]", detail.Quantity.ToString());
                replacement = replacement.Replace("[Total]", detail.TotalAmount.ToString());
                replacement = replacement.Replace("[STT]", detail.QueueNumber.ToString());
                replacement = replacement.Replace("[ExaminationLocation]", detail.ExaminationLocation);
                replacement = replacement.Replace("[Clinic]", detail.Clinic.ToString());
                replacementContent.Append(replacement);
            }
            return dataPrint.Replace(type, replacementContent.ToString());
        }
    }
}
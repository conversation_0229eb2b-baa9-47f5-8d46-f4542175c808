﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.PaymentLogic.Queries
{
    public class GetPaymentStatus : IRequest<BaseCommandResultWithData<PaymentStatusDto>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetPaymentStatusHandler(IDatabaseService databaseService,
        ICurrentUserService currentUserService,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetPaymentStatus, BaseCommandResultWithData<PaymentStatusDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<PaymentStatusDto>> Handle(
            GetPaymentStatus request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<PaymentStatusDto>();

            var payment = await databaseService.Payments.FindAsync([request.Number], cancellationToken: cancellationToken);

            if (payment is null)
            {
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, HospitalConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            (bool success, string mess, ErrorTypeEnum errorType, PaymentStatusDto paymentStatus)
                = await hisService.CheckPaymentStatus(payment.RefNo ?? string.Empty);

            // Cập nhật trạng thái thanh toán
            if (success && payment.Status != PaymentConstant.Success && paymentStatus.PaymentStatus == PaymentConstant.Success)
            {
                payment.Status = PaymentConstant.Success;
                payment.PaidAmount = payment.PaymentAmount;
                payment.SetUpdate(currentUserService.UserName);
                databaseService.Payments.Update(payment);
                await databaseService.SaveChangesAsync(cancellationToken);
            }

            result.Set(true, PaymentConstant.Ok, new PaymentStatusDto()
            {
                Number = payment.Id,
                RefNo = paymentStatus?.RefNo ?? string.Empty,
                PaymentStatus = paymentStatus?.PaymentStatus ?? string.Empty,
                InvoiceInfoRef = paymentStatus?.InvoiceInfoRef ?? string.Empty,
                IpnStatus = payment.IpnStatus ?? string.Empty,
            });

            return result;
        }
    }
}
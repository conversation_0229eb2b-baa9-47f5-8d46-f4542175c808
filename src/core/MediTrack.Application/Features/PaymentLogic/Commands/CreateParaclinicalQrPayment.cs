﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib.Request;
using MediTrack.Application.Integrate;
using Serilog;
using System.Text.RegularExpressions;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Helpers;
using MediTrack.Application.Features.ParaclinicalLogic.Commands;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class CreateParaclinicalQrPayment : IRequest<BaseCommandResultWithData<QrPaymentDto>>
    {
        public string CustomerId { get; set; } = string.Empty;
        public string RefNo { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public List<Indication> Indications { get; set; } = [];
    }

    public class CreateParaclinicalQrPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentParaclinicalConfig> paymentConfig,
        IHospitalMetadataRepository hospitalMetadataRepository,
        IHisServiceHelper hisServiceHelper,
        IMediator mediator,
        ICurrentHospitalService currentHospitalService, IHttpClientFactory httpClientFactory) : IRequestHandler<CreateParaclinicalQrPayment, BaseCommandResultWithData<QrPaymentDto>>
    {
        private readonly PaymentParaclinicalConfig paymentConfig = paymentConfig.Value;
        private readonly IHisService hisService = currentHospitalService.HisService;
        public async Task<BaseCommandResultWithData<QrPaymentDto>> Handle(
            CreateParaclinicalQrPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<QrPaymentDto>();
            var resultData = new QrPaymentDto();

            //1. Create receipt and payment
            var customer = await databaseService.Customers.FindAsync([request.CustomerId], cancellationToken);
            var respone = await mediator.Send(new GetParaclinicalIndications { SearchText = request.RefNo, SearchType = 3 }, cancellationToken);
            if (!respone.Success)
            {
                result.Set(false, respone.Messages, respone.ErrorType);
                return result;
            }
            string PatientCodeRef = respone.Data?.PatientCode ?? string.Empty;
            string PatientNameRef = respone.Data?.FullName ?? string.Empty;
            string PatientDobRef = respone.Data?.DateOfBirth?.ToString("dd/MM/yyyy") ?? string.Empty;
            string PatientGenderRef = respone.Data?.Sex ?? string.Empty;

            var receipt = new Receipt
            {
                Number = IdentityHelper.Guid(15),
                CustomerId = customer?.Id,
                TotalAmount = request.TotalAmount,
                ReceiptDate = DateTime.UtcNow,
                Status = request.TotalAmount > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                DeviceId = currentHospitalService.KioskId,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                RefNo = request.RefNo,
                CreatedAt = DateTime.UtcNow,
                PatientCodeRef = PatientCodeRef,
                PatientNameRef = PatientNameRef,
                PatientDateOfBirthRef = PatientDobRef,
                PatientGenderRef = PatientGenderRef,
                ReceiptType = "CLS"
            };

            var payment = new Payment()
            {
                Id = receipt.Number,
                PaymentDate = DateTime.UtcNow,
                PaymentAmount = receipt.TotalAmount,
                ReceiptNumber = receipt.Number,
                CreatedAt = DateTime.UtcNow,
                Status = PaymentConstant.NewPayment,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId
            };

            // 2. Luồng phía bệnh viện gen QR
            if (currentHospitalService.CurrentHospital.IsGenQR)
            {
                (bool boolean, string Message, ErrorTypeEnum errorType, resultData) = await hisService.GenQRPayment(receipt.RefNo);
                if (!boolean)
                {
                    result.Set(false, Message, errorType);
                    return result;
                }
                receipt.QrCode = resultData.QrCode;
                payment.QrCode = resultData.QrCode;
                payment.RefNo = resultData.Number;
                databaseService.Payments.Add(payment);
                databaseService.Receipts.Add(receipt);

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
                return result;
            }

            //3. Call API Gen Qr
            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };

            //get hospital
            var hospital = await hisServiceHelper.GetHospital(receipt.HospitalId, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, HospitalConstant.InvalidHospital, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;
            var hospitalMetadata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(hospital.Id, groupType: "bank_transfer_message_template", cancellationToken: cancellationToken);
            if (hospitalMetadata != null)
            {
                paymentDescription = hospitalMetadata.Value ?? paymentDescription;
                paymentDescription = paymentDescription.Replace("[RefNo]", receipt.RefNo);
                paymentDescription = paymentDescription.Replace("[CustomerName]", receipt.Customer?.GetFullName().ToUpper());
                paymentDescription = paymentDescription.RemoveVietnameseCharacter();
            }

            //remove special characters include space, 0-9a-zA-Z, and -, .
            paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
            // limit to 45 characters
            paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
            var paymentRequest = new CreateQrRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = payment.Id,
                Type = PaymentConstant.DefaultType,
                TransactionAmount = (double)payment.PaymentAmount,
                Ipn = paymentConfig.IpnUrl,
                TransactionDescription = hospitalMetadata != null ? paymentDescription : $"THANH TOAN PHIEU THU {paymentDescription}",
            };

            Log.Information("{LogPrefix} CreateParaclinicalQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

            (bool processResult, string processMessage, CreateQrResponse? res) = await
                PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

            Log.Information("{LogPrefix} CreateParaclinicalQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                currentHospitalService.LogPrefix, processResult, processMessage, res);

            config.Dispose();

            if (processResult)
            {
                payment.QrCode = res!.QrCode;
                payment.RefNo = res!.PaymentCode;
                payment.UpdatedAt = DateTime.UtcNow;
                payment.Status = PaymentConstant.WaitForPayment;
                payment.RefDescReq = res!.TransactionDescription;
                receipt.QrCode = payment.QrCode;

                databaseService.Payments.Add(payment);
                databaseService.Receipts.Add(receipt);

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    resultData.QrCode = receipt.QrCode;
                    resultData.TransactionDescription = res.TransactionDescription;

                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
            }
            else
            {
                result.Set(false, processMessage, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

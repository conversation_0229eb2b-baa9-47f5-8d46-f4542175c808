﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib.Request;
using MediTrack.Application.Integrate;
using Serilog;
using System.Text.RegularExpressions;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Helpers;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class CreateQrPayment : IRequest<BaseCommandResultWithData<QrPaymentDto>>
    {
        public string ReceiptNumber { get; set; } = string.Empty;
    }

    public class CreateQrPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentConfig> paymentConfig,
        IHospitalMetadataRepository hospitalMetadataRepository,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService, IHttpClientFactory httpClientFactory) : IRequestHandler<CreateQrPayment, BaseCommandResultWithData<QrPaymentDto>>
    {
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<QrPaymentDto>> Handle(
            CreateQrPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<QrPaymentDto>();
            var resultData = new QrPaymentDto();

            //1. Check receipt
            var receipt = await databaseService.Receipts
                .Include(x => x.Customer)
                .Include(x => x.Register)
                .FirstOrDefaultAsync(x => x.Number == request.ReceiptNumber && x.TotalAmount > 0, cancellationToken: cancellationToken);

            if (receipt is null)
            {
                result.Set(false, ReceiptConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            // Luồng phía bệnh viện gen QR, QR sẽ được tạo lúc gọi API tạo phiếu đăng kí 
            if (currentHospitalService.CurrentHospital.IsGenQR == true)
            {
                var paymentResult = await databaseService.Payments.FirstOrDefaultAsync(x => x.ReceiptNumber == request.ReceiptNumber, cancellationToken: cancellationToken);

                if (paymentResult == null)
                {
                    result.Set(false, PaymentConstant.NotFound);
                    return result;
                }

                if (string.IsNullOrEmpty(paymentResult.QrCode))
                {
                    (bool boolean, string Message, ErrorTypeEnum errorType, resultData) = await hisService.GenQRPayment(receipt.RefNo ?? string.Empty);
                    if (!boolean)
                    {
                        result.Set(false, Message, errorType);
                        return result;
                    }
                    receipt.QrCode = resultData.QrCode;
                    paymentResult.QrCode = resultData.QrCode;
                    paymentResult.RefNo = resultData.Number;
                    paymentResult.RefDesc = resultData.TransactionDescription;
                    databaseService.Payments.Update(paymentResult);
                    databaseService.Receipts.Update(receipt);
                    var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                    if (updateResult < 1)
                    {
                        result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                        return result;
                    }
                }
                result.Set(true, PaymentConstant.Success, new QrPaymentDto
                {
                    Number = paymentResult.Id,
                    QrCode = paymentResult.QrCode ?? string.Empty,
                    TransactionDescription = resultData.TransactionDescription
                });

                return result;
            }

            //2. Tạo và lưu payment
            var payment = await databaseService.Payments
                .FirstOrDefaultAsync(x => x.ReceiptNumber == request.ReceiptNumber && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken: cancellationToken);

            if (payment is null)
            {
                payment = new Payment()
                {
                    Id = IdentityHelper.Guid(10),
                    PaymentDate = DateTime.UtcNow,
                    PaymentAmount = receipt.TotalAmount,
                    ReceiptNumber = receipt.Number,
                    CreatedAt = DateTime.UtcNow,
                    Status = PaymentConstant.NewPayment,
                    IsHisGenQr = currentHospitalService.CurrentHospital.IsGenQR,
                    HospitalId = currentHospitalService.CurrentHospital.HospitalId
                };

                databaseService.Payments.Add(payment);

                var savePaymentResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (savePaymentResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                    return result;
                }
            }

            if (!string.IsNullOrEmpty(payment.QrCode))
            {
                result.Set(true, PaymentConstant.SaveChangesSuccess, new QrPaymentDto
                {
                    Number = payment.Id,
                    QrCode = payment.QrCode,
                    TransactionDescription = payment?.RefDesc ?? string.Empty
                });

                return result;
            }

            //3. Call API Gen Qr
            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };

            //get hospital
            var hospital = await hisServiceHelper.GetHospital(receipt.HospitalId ?? string.Empty, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, HospitalConstant.InvalidHospital, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;
            var hospitalMetadata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(hospital.Id, groupType: "bank_transfer_message_template", cancellationToken: cancellationToken);
            if (hospitalMetadata != null)
            {
                paymentDescription = hospitalMetadata.Value ?? paymentDescription;
                paymentDescription = paymentDescription.Replace("[RefNo]", receipt.RefNo);
                paymentDescription = paymentDescription.Replace("[CustomerName]", receipt.Customer?.GetFullName().ToUpper());
                paymentDescription = paymentDescription.Replace("[CustomerNameMidLast]", receipt.Customer?.GetMidAndLastName().ToUpper());
                paymentDescription = paymentDescription.Replace("[PatientCode]", receipt.Register?.PatientCode);
                paymentDescription = paymentDescription.RemoveVietnameseCharacter();
            }

            //remove special characters include space, 0-9a-zA-Z, and -, .
            paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
            // limit to 45 characters
            paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
            var paymentRequest = new CreateQrRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = payment.Id,
                Type = PaymentConstant.DefaultType,
                TransactionAmount = (double)payment.PaymentAmount,
                Ipn = paymentConfig.IpnUrl,
                TransactionDescription = hospitalMetadata != null ? paymentDescription : $"THANH TOAN PHIEU THU {paymentDescription}",
            };

            Log.Information("{LogPrefix} CreateQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

            (bool processResult, string processMessage, CreateQrResponse? res) = await
                PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

            Log.Information("{LogPrefix} CreateQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                currentHospitalService.LogPrefix, processResult, processMessage, res);

            config.Dispose();

            if (processResult)
            {
                payment.QrCode = res!.QrCode;
                payment.RefNo = res!.PaymentCode;
                payment.UpdatedAt = DateTime.UtcNow;
                payment.Status = PaymentConstant.WaitForPayment;
                payment.RefDescReq = res!.TransactionDescription;
                receipt.QrCode = payment.QrCode;

                databaseService.Payments.Update(payment);
                databaseService.Receipts.Update(receipt);

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    resultData.QrCode = receipt.QrCode;
                    resultData.TransactionDescription = res.TransactionDescription;

                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
            }
            else
            {
                result.Set(false, processMessage, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

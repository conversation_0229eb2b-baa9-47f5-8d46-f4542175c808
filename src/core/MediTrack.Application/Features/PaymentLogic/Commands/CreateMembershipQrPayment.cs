﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib.Request;
using MediTrack.Application.Integrate;
using Serilog;
using System.Text.RegularExpressions;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Helpers;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class CreateMembershipQrPayment : IRequest<BaseCommandResultWithData<QrPaymentDto>>
    {
        public string CustomerId { get; set; } = string.Empty;
        public string MembershipId { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
    }

    public class CreateMembershipQrPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentMembershipConfig> paymentConfig,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService, IHttpClientFactory httpClientFactory) : IRequestHandler<CreateMembershipQrPayment, BaseCommandResultWithData<QrPaymentDto>>
    {
        private readonly PaymentMembershipConfig paymentConfig = paymentConfig.Value;
        public async Task<BaseCommandResultWithData<QrPaymentDto>> Handle(
            CreateMembershipQrPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<QrPaymentDto>();
            var resultData = new QrPaymentDto();

            //1. Create receipt and payment
            var customer = await databaseService.Customers.FindAsync([request.CustomerId], cancellationToken);

            var receipt = new Receipt
            {
                Number = IdentityHelper.Guid(15),
                CustomerId = customer?.Id,
                TotalAmount = request.TotalAmount,
                ReceiptDate = DateTime.UtcNow,
                Status = request.TotalAmount > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                CreatedAt = DateTime.UtcNow,
                MembershipId = request.MembershipId,
                ReceiptType = "MEMBERSHIP"
            };
            receipt.RefNo = receipt.Number;

            var payment = new Payment()
            {
                Id = receipt.Number,
                PaymentDate = DateTime.UtcNow,
                PaymentAmount = receipt.TotalAmount,
                ReceiptNumber = receipt.Number,
                CreatedAt = DateTime.UtcNow,
                Status = PaymentConstant.NewPayment,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId
            };

            //2. Call API Gen Qr
            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };

            //get hospital
            var hospital = await hisServiceHelper.GetHospital(receipt.HospitalId, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, HospitalConstant.InvalidHospital, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;

            //remove special characters include space, 0-9a-zA-Z, and -, .
            paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
            // limit to 45 characters
            paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
            var paymentRequest = new CreateQrRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = payment.Id,
                Type = PaymentConstant.DefaultType,
                TransactionAmount = (double)payment.PaymentAmount,
                Ipn = paymentConfig.IpnUrl,
                TransactionDescription = $"NAP THE THANH VIEN {paymentDescription}",
            };

            Log.Information("{LogPrefix} CreateMembershipQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

            (bool processResult, string processMessage, CreateQrResponse? res) = await
                PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

            Log.Information("{LogPrefix} CreateMembershipQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                currentHospitalService.LogPrefix, processResult, processMessage, res);

            config.Dispose();

            if (processResult)
            {
                payment.QrCode = res!.QrCode;
                payment.RefNo = res!.PaymentCode;
                payment.UpdatedAt = DateTime.UtcNow;
                payment.Status = PaymentConstant.WaitForPayment;
                payment.RefDescReq = res!.TransactionDescription;
                receipt.QrCode = payment.QrCode;

                databaseService.Payments.Add(payment);
                databaseService.Receipts.Add(receipt);

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    resultData.QrCode = receipt.QrCode;
                    resultData.TransactionDescription = res.TransactionDescription;

                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
            }
            else
            {
                result.Set(false, processMessage, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

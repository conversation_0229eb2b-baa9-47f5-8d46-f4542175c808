using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib.Request;
using MediTrack.Application.Integrate;
using Serilog;
using System.Text.RegularExpressions;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Helpers;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class CreateDonationQrPayment : IRequest<BaseCommandResultWithData<QrPaymentDto>>
    {
        public string CampaignId { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string? Message { get; set; }
        public bool IsAnonymous { get; set; } = true;
    }

    public class CreateDonationQrPaymentHandler(
        IDatabaseService databaseService,
        IOptions<PaymentDonationConfig> paymentConfig,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService,
        IHttpClientFactory httpClientFactory
    ) : IRequestHandler<CreateDonationQrPayment, BaseCommandResultWithData<QrPaymentDto>>
    {
        private readonly PaymentDonationConfig paymentConfig = paymentConfig.Value;
        public async Task<BaseCommandResultWithData<QrPaymentDto>> Handle(
            CreateDonationQrPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<QrPaymentDto>();
            var resultData = new QrPaymentDto();

            // 1. Create receipt and payment
            var customer = await databaseService.Customers
                .Include(x => x.User)
                .FirstOrDefaultAsync(x => x.Id == request.CustomerId, cancellationToken);

            var receipt = new Receipt
            {
                Number = IdentityHelper.Guid(15),
                CustomerId = customer?.Id,
                TotalAmount = request.Amount,
                ReceiptDate = DateTime.UtcNow,
                Status = request.Amount > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                CreatedAt = DateTime.UtcNow,
                DonationCampaignId = request.CampaignId,
                ReceiptType = "DONATION"
            };
            receipt.RefNo = receipt.DonationCampaignId ?? receipt.Number;

            var payment = new Payment()
            {
                Id = receipt.Number,
                PaymentDate = DateTime.UtcNow,
                PaymentAmount = receipt.TotalAmount,
                ReceiptNumber = receipt.Number,
                CreatedAt = DateTime.UtcNow,
                Status = PaymentConstant.NewPayment,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId,
            };

            // 2. Call API Gen Qr
            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };

            // get hospital
            var hospital = await hisServiceHelper.GetHospital(receipt.HospitalId, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, HospitalConstant.InvalidHospital, ErrorTypeEnum.MediPayError);
                return result;
            }

            string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;
            paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
            paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;

            var paymentRequest = new CreateQrRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = payment.Id,
                Type = PaymentConstant.DefaultType,
                TransactionAmount = (double)payment.PaymentAmount,
                Ipn = paymentConfig.IpnUrl,
                TransactionDescription = $"QUYEN GOP CHIEN DICH {paymentDescription}",
            };

            Log.Information("{LogPrefix} CreateDonationQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

            (bool processResult, string processMessage, CreateQrResponse? res) = await
                PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

            Log.Information("{LogPrefix} CreateDonationQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                currentHospitalService.LogPrefix, processResult, processMessage, res);

            config.Dispose();

            if (processResult)
            {
                payment.QrCode = res!.QrCode;
                payment.RefNo = res!.PaymentCode;
                payment.UpdatedAt = DateTime.UtcNow;
                payment.Status = PaymentConstant.WaitForPayment;
                payment.RefDescReq = res!.TransactionDescription;
                receipt.QrCode = payment.QrCode;

                databaseService.Payments.Add(payment);
                databaseService.Receipts.Add(receipt);

                // Add donation history when QR is created
                if (!string.IsNullOrEmpty(receipt.DonationCampaignId) && customer?.User != null)
                {
                    var donationHistory = new DonationHistory
                    {
                        Id = IdentityHelper.Guid(15),
                        CampaignId = receipt.DonationCampaignId,
                        UserId = customer.User.Id,
                        FullName = customer.GetFullName() ?? string.Empty,
                        Message = request.Message,
                        Amount = request.Amount,
                        PaymentStatus = PaymentConstant.WaitForPayment,
                        IsAnonymous = request.IsAnonymous,
                    };
                    databaseService.DonationHistories.Add(donationHistory);
                }

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    resultData.QrCode = receipt.QrCode;
                    resultData.TransactionDescription = res.TransactionDescription;

                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
            }
            else
            {
                result.Set(false, processMessage, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

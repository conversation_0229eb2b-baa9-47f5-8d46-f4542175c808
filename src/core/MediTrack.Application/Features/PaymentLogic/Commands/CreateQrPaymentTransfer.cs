using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PaymentClient.Lib.Request;
using Microsoft.AspNetCore.Razor.TagHelpers;
using MediTrack.Ultils.Extensions;
using MediTrack.Application.Integrate;
using MediBankClient.Lib.Request;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class CreateQrPaymentTransfer : IRequest<BaseCommandResultWithData<QrPaymentDto>>
    {
        public string TerminalId {get; set;} =string.Empty;
        public string ReceiptNumber { get; set; } = string.Empty;
    }

    public class CreateQrPaymentTransferHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IOptions<PaymentConfig> paymentConfig,
        IMediBankService mediBankService) : IRequestHandler<CreateQrPaymentTransfer, BaseCommandResultWithData<QrPaymentDto>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseCommandResultWithData<QrPaymentDto>> Handle(
            CreateQrPaymentTransfer request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<QrPaymentDto>();
            var resultData = new QrPaymentDto();

            //1. Check receipt
            var receipt = await databaseService.Receipts
                .Include(x => x.Customer)
                .Include(x => x.Register)
                .FirstOrDefaultAsync(x => x.Number == request.ReceiptNumber);

            if (receipt is null)
            {
                result.Set(false, ReceiptConstant.NotFound);
                return result;
            }

            //2. Tạo và lưu payment
            var payment = new Payment()
            {
                Id = IdentityHelper.Guid(10),
                PaymentDate = DateTime.UtcNow,
                PaymentAmount = receipt.TotalAmount,
                ReceiptNumber = receipt.Number,
                CreatedAt = DateTime.UtcNow,
                Status = PaymentConstant.NewPayment,
                HospitalId = receipt.HospitalId,
            };

            databaseService.Payments.Add(payment);
            var savePaymentResult = await databaseService.SaveChangesAsync(cancellationToken);

            if(savePaymentResult < 1)
            {
                result.Set(false, PaymentConstant.SaveChangesError);
                return result;
            }

            //3. Call API Gen Qr

            var hospital = await databaseService.Hospitals
                .FirstOrDefaultAsync(x => x.Id == receipt.HospitalId);

            if(hospital is null)
            {
                result.Set(false, ErrorConstant.NOT_FOUND_DATA);
                return result;
            }

            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };

            var response = await mediBankService.CreateQrTransaction(new CreateHdBankQrPayment()
                {
                    MerchantId = hospital.BankTransferId,
                    Amount = (int)payment.PaymentAmount,
                    BillNumber = request.ReceiptNumber,
                    OrderId = payment.Id,
                    TranId = receipt.Customer!.BankTranId ?? string.Empty,
                    TerminalId = request.TerminalId,
                    Description = $"THANH TOAN PHIEU THU {receipt.Number}",
                });

            config.Dispose();

            if(response.Code == "00")
            {
                payment.QrCode = response.Data?.ImageStr;
                payment.UpdatedAt = DateTime.UtcNow;
                payment.Status = PaymentConstant.WaitForPayment;
                payment.QrTransactionId = response.Data?.TransactionId;

                receipt.QrCode = payment.QrCode;

                databaseService.Payments.Update(payment);
                databaseService.Receipts.Update(receipt);

                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

                if(updateResult < 1)
                {
                    result.Set(false, PaymentConstant.SaveChangesError);
                }
                else
                {
                    resultData.Number = payment.Id;
                    resultData.QrCode = receipt.QrCode ?? string.Empty;
                    resultData.TransactionDescription = response.Data?.Content ?? string.Empty;

                    result.Set(true, PaymentConstant.SaveChangesSuccess, resultData);
                }
            }
            else
            {
                result.Set(false, response.Message);
            }

            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class UpdateStatusOfMembershipPayment : IRequest<BaseCommandResult>
    {
        public string Signature { get; set; } = string.Empty;
        public UpdateStatusPaymentDto Data { get; set; } = new();
    }

    public class UpdateStatusOfMembershipPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentConfig> paymentConfig,
        IOptions<EnvironmentConfig> telegramConfig,
        IHisServiceHelper hisServiceHelpers,
        IHttpClientFactory httpClientFactory
        ) : IRequestHandler<UpdateStatusOfMembershipPayment, BaseCommandResult>
    {
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;
        private readonly EnvironmentConfig telegramConfig = telegramConfig.Value;

        public async Task<BaseCommandResult> Handle(
            UpdateStatusOfMembershipPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            //1. Check signature and merchant
            var payment = await databaseService.Payments
                .Include(x => x.Receipt)
                .FirstOrDefaultAsync(x => x.Id == request.Data.InvoiceId, cancellationToken: cancellationToken);
            if (payment is null)
            {
                Log.Information("{HospitalId} Not found payment on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var hospital = await hisServiceHelpers.GetHospital(payment.HospitalId ?? string.Empty, databaseService, cancellationToken);
            if (hospital is null)
            {
                Log.Information("{HospitalId} Not found hospital on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var plainText = JsonConvert.SerializeObject(request.Data, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
            });
            var signature = SignatureHelper.HmacSha256Hash(plainText, paymentConfig.SecretKey);

            Log.Information("{HospitalId} Check signature plain text {PlainText} - Signature: {Signature}", hospital.Id, plainText, signature);

            if (signature != request.Signature)
            {
                Log.Information("{HospitalId} Invalid signature", hospital.Id);
                result.Set(false, signature, ErrorTypeEnum.MediPayError);
                return result;
            }

            string descriptionTele = $"KIOSK - {hospital.Name} - {payment.Id} - {payment.Receipt!.CustomerId} thanh toán {request.Data.PaidAmount}";
            _ = httpClientFactory.CreateClient().SendAsync(telegramConfig.TelegramAPI, telegramConfig.TelegramChatId, descriptionTele);

            //2. Query and check payment
            if (payment.RefNo != request.Data.PaymentCode)
            {
                Log.Information("{HospitalId} Invalid RefNo", hospital.Id);
                result.Set(false, PaymentConstant.InvalidPayment, ErrorTypeEnum.MediPayError);
                return result;
            }

            if (request.Data.MerchantId != (hospital.MerchantId ?? string.Empty))
            {
                Log.Information("{HospitalId} Invalid Merchant", hospital?.Id);
                result.Set(false, PaymentConstant.InvalidMerchantId, ErrorTypeEnum.MediPayError);
                return result;
            }

            //3. Update payment
            payment.PaidAmount = request.Data.PaidAmount;

            if (request.Data.Status == "01")
            {
                payment.Status = PaymentConstant.FailPayment;
            }
            else if (request.Data.Status == "00")
            {
                payment.Status = PaymentConstant.Success;

                if (payment.Receipt != null)
                {
                    payment.Receipt.Status = ReceiptConstant.STATUS_PAID;
                }
            }
            else if (request.Data.Status == "10")
            {
                payment.Status = PaymentConstant.Partial;
            }

            payment.Message = request.Data.PaidDescription;
            payment.UpdatedAt = DateTime.UtcNow;
            payment.RefDesc = request.Data.TransactionDescription;
            payment.RefTran = request.Data.TransactionId;

            databaseService.Payments.Update(payment);

            if (payment.Status == PaymentConstant.Success || payment.Status == PaymentConstant.Partial)
            {
                var membership = await databaseService.Memberships
                    .FindAsync([payment.Receipt!.MembershipId], cancellationToken: cancellationToken);
                if (membership is null)
                {
                    Log.Information("{HospitalId} Not found membership for payment {PaymentId}", hospital?.Id, payment.Id);
                    result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                    return result;
                }
                var oldBalance = membership.Balance;
                membership.Balance += request.Data.PaidAmount;
                databaseService.Memberships.Update(membership);

                // Add MembershipHistory for top-up
                var history = new MembershipHistory
                {
                    Id = IdentityHelper.Guid(15),
                    MembershipId = membership.Id,
                    MembershipCode = membership.MemberCode,
                    OldBalance = oldBalance,
                    NewBalance = membership.Balance,
                    OldUsageCount = membership.UsageCount,
                    NewUsageCount = membership.UsageCount,
                    Description = $"Nạp tiền vào thẻ thành viên. Thanh toán: {request.Data.PaidAmount:N0}",
                    TransactionType = "TOP_UP",
                    RegisterNumber = null,
                    ReceiptNumber = payment.Receipt?.Number,
                    RefNo = payment.Receipt?.RefNo,
                    CustomerId = payment.Receipt?.CustomerId ?? string.Empty,
                    HospitalId = payment.HospitalId ?? string.Empty,
                    CreatedBy = payment.UpdatedBy,
                    CreatedAt = payment.UpdatedAt ?? DateTime.UtcNow
                };
                databaseService.MembershipHistories.Add(history);
            }

            var updateResult = await databaseService.SaveChangesAsync(cancellationToken);
            if (updateResult < 1)
            {
                Log.Information("{HospitalId} Update payment fail", hospital?.Id);
                result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                return result;
            }

            result.Set(true, PaymentConstant.Ok);
            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class UpdateStatusOfParaclinicalPayment : IRequest<BaseCommandResult>
    {
        public string Signature { get; set; } = string.Empty;
        public UpdateStatusPaymentDto Data { get; set; } = new();
    }

    public class UpdateStatusOfParaclinicalPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentConfig> paymentConfig,
        IHisServiceHelper hisServiceHelpers,
        ICachedService cachedService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IHttpClientFactory httpClientFactory
        ) : IRequestHandler<UpdateStatusOfParaclinicalPayment, BaseCommandResult>
    {
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseCommandResult> Handle(
            UpdateStatusOfParaclinicalPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            //1. Check signature and merchant
            var payment = await databaseService.Payments
                .Include(x => x.Receipt)
                .FirstOrDefaultAsync(x => x.Id == request.Data.InvoiceId, cancellationToken: cancellationToken);
            if (payment is null)
            {
                Log.Information("{HospitalId} Not found payment on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }
            
            var hospital = await hisServiceHelpers.GetHospital(payment.HospitalId ?? string.Empty, databaseService, cancellationToken);
            if (hospital is null)
            {
                Log.Information("{HospitalId} Not found hospital on ipn for invoice {InvoiceId}", payment?.Receipt?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var plainText = JsonConvert.SerializeObject(request.Data, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
            });
            var signature = SignatureHelper.HmacSha256Hash(plainText, paymentConfig.SecretKey);

            Log.Information("{HospitalId} Check signature plain text {PlainText} - Signature: {Signature}", hospital.Id, plainText, signature);

            if (signature != request.Signature)
            {
                Log.Information("{HospitalId} Invalid signature", hospital.Id);
                result.Set(false, signature, ErrorTypeEnum.MediPayError);
                return result;
            }

            //2. Query and check payment
            if (payment.RefNo != request.Data.PaymentCode)
            {
                Log.Information("{HospitalId} Invalid RefNo", hospital.Id);
                result.Set(false, PaymentConstant.InvalidPayment, ErrorTypeEnum.MediPayError);
                return result;
            }

            if (request.Data.MerchantId != (hospital.MerchantId ?? string.Empty))
            {
                Log.Information("{HospitalId} Invalid Merchant", hospital?.Id);
                result.Set(false, PaymentConstant.InvalidMerchantId, ErrorTypeEnum.MediPayError);
                return result;
            }

            //3. Update payment
            payment.PaidAmount = request.Data.PaidAmount;

            if (request.Data.Status == "01")
            {
                payment.Status = PaymentConstant.FailPayment;
            }
            else if (request.Data.Status == "00")
            {
                payment.Status = PaymentConstant.Success;

                if (payment.Receipt != null)
                {
                    payment.Receipt.Status = ReceiptConstant.STATUS_PAID;
                }
            }
            else if (request.Data.Status == "10")
            {
                payment.Status = PaymentConstant.Partial;
            }

            payment.Message = request.Data.PaidDescription;
            payment.UpdatedAt = DateTime.UtcNow;
            payment.RefDesc = request.Data.TransactionDescription;
            payment.RefTran = request.Data.TransactionId;

            databaseService.Payments.Update(payment);
            var updateResult = await databaseService.SaveChangesAsync(cancellationToken);
            if (updateResult < 1)
            {
                Log.Information("{HospitalId} Update payment fail", hospital?.Id);
                result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                return result;
            }

            if (hospital.Id is null)
            {
                Log.Information("{HospitalId} Not found hospital", hospital?.Id);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var logPrefix = $"{hospital.Id}|{payment.Receipt?.DeviceId}|:";
            var currentHospital = hisServiceHelpers.GetHospitalServiceById(hospital?.Id ?? string.Empty, databaseService);
            var hisService = hisServiceHelpers.CreateHisService(
                currentHospital, httpClientFactory, databaseService, cachedService, logPrefix,
                    null, provinceRepository, districtRepository, wardRepository);

            //4. Call IPN (cận lâm sàng)
            (bool ipnResult, string ipnMessage, ErrorTypeEnum errorType, UpdateParaclinicalPaymentStatusResponse postbackRes)
                = await hisService.UpdateParaclinicalPaymentStatus(new UpdateParaclinicalPaymentStatusRequest
                {
                    MerchantId = hospital?.MerchantId ?? string.Empty,
                    InvoiceId = payment.Receipt!.RefNo ?? string.Empty,
                    TransactionId = request.Data.TransactionId,
                    KioskId = payment.Receipt!.DeviceId ?? string.Empty,
                    Status = request.Data.Status,
                    TransactionAmount = request.Data.PaidAmount,
                    PaidAmount = request.Data.PaidAmount,
                    TransactionDescription = request.Data.TransactionDescription,
                    PaidDescription = request.Data.PaidDescription,
                    PaidTime = request.Data.PaidAt ?? DateTime.UtcNow,
                });

            Log.Information("{HospitalId} Call Ipn Paraclinical Res {Result} - Message {Message} - Response {Response}", hospital?.Id, ipnResult, ipnMessage, postbackRes);

            //5. Cập nhật trạng thái sau khi IPN
            payment.IpnUrl = postbackRes.Url;
            payment.IpnMessage = ipnMessage;
            payment.IpnStatus = ipnResult ? "SUCCESS" : "FAIL";
            payment.InvoiceInfoRef = postbackRes.IpnResponse;
            databaseService.Payments.Update(payment);
            var updatePaymentResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (updatePaymentResult < 1)
            {
                Log.Information("{HospitalId} Update Ipn Fail", hospital?.Id);
                result.Set(false, PaymentConstant.UpdateIpnFail, ErrorTypeEnum.MediPayError);
                return result;
            }

            result.Set(true, PaymentConstant.Ok);

            return result;
        }
    }
}

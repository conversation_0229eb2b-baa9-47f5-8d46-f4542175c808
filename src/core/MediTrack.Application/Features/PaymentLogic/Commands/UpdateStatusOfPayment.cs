﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.MemberShipLogic.Commands;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Serilog;

namespace MediTrack.Application.Features.PaymentLogic.Commands
{
    public class UpdateStatusOfPayment : IRequest<BaseCommandResult>
    {
        public string Signature { get; set; } = string.Empty;
        public UpdateStatusPaymentDto Data { get; set; } = new();
    }

    public class UpdateStatusOfPaymentHandler(IDatabaseService databaseService,
        IOptions<PaymentConfig> paymentConfig,
        IOptions<EnvironmentConfig> telegramConfig,
        IHisServiceHelper hisServiceHelpers,
        ICachedService cachedService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IHttpClientFactory httpClientFactory,
        IMediator mediator
        ) : IRequestHandler<UpdateStatusOfPayment, BaseCommandResult>
    {
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;
        private readonly EnvironmentConfig telegramConfig = telegramConfig.Value;

        public async Task<BaseCommandResult> Handle(
            UpdateStatusOfPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            //1. Check signature and merchant
            var payment = await databaseService.Payments
                .Include(x => x.Receipt)
                .FirstOrDefaultAsync(x => x.Id == request.Data.InvoiceId, cancellationToken: cancellationToken);
            if (payment is null)
            {
                Log.Information("{HospitalId} Not found payment on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var hospital = await hisServiceHelpers.GetHospital(payment.HospitalId ?? string.Empty, databaseService, cancellationToken);
            if (hospital is null)
            {
                Log.Information("{HospitalId} Not found hospital on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var plainText = JsonConvert.SerializeObject(request.Data, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
            });
            var signature = SignatureHelper.HmacSha256Hash(plainText, paymentConfig.SecretKey);

            Log.Information("{HospitalId} Check signature plain text {PlainText} - Signature: {Signature}", hospital.Id, plainText, signature);

            if (signature != request.Signature)
            {
                Log.Information("{HospitalId} Invalid signature", hospital.Id);
                result.Set(false, signature, ErrorTypeEnum.MediPayError);
                return result;
            }

            string descriptionTele = $"KIOSK - {hospital.Name} - {payment.Id} - {payment.Receipt!.CustomerId} thanh toán {request.Data.PaidAmount}";
            _ = httpClientFactory.CreateClient().SendAsync(telegramConfig.TelegramAPI, telegramConfig.TelegramChatId, descriptionTele);

            //2. Query and check payment
            if (payment.RefNo != request.Data.PaymentCode)
            {
                Log.Information("{HospitalId} Invalid RefNo", hospital.Id);
                result.Set(false, PaymentConstant.InvalidPayment, ErrorTypeEnum.MediPayError);
                return result;
            }

            if (request.Data.MerchantId != (hospital.MerchantId ?? string.Empty))
            {
                Log.Information("{HospitalId} Invalid Merchant", hospital?.Id);
                result.Set(false, PaymentConstant.InvalidMerchantId, ErrorTypeEnum.MediPayError);
                return result;
            }

            //3. Update payment
            payment.PaidAmount = request.Data.PaidAmount;

            if (request.Data.Status == "01")
            {
                payment.Status = PaymentConstant.FailPayment;
            }
            else if (request.Data.Status == "00")
            {
                payment.Status = PaymentConstant.Success;

                if (payment.Receipt != null)
                {
                    payment.Receipt.Status = ReceiptConstant.STATUS_PAID;
                }
            }
            else if (request.Data.Status == "10")
            {
                payment.Status = PaymentConstant.Partial;
            }

            payment.Message = request.Data.PaidDescription;
            payment.UpdatedAt = DateTime.UtcNow;
            payment.RefDesc = request.Data.TransactionDescription;
            payment.RefTran = request.Data.TransactionId;
            if (hospital.HisVersion != "v6")
            {
                databaseService.Payments.Update(payment);
                var updateResult = await databaseService.SaveChangesAsync(cancellationToken);
                if (updateResult < 1)
                {
                    Log.Information("{HospitalId} Update payment fail", hospital?.Id);
                    result.Set(false, PaymentConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                    return result;
                }
            }
            if (hospital.Id is null)
            {
                Log.Information("{HospitalId} Not found hospital", hospital?.Id);
                result.Set(false, PaymentConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var logPrefix = $"{hospital.Id}|{payment.Receipt?.DeviceId}|:";
            var currentHospital = hisServiceHelpers.GetHospitalServiceById(hospital?.Id ?? string.Empty, databaseService);
            var hisService = hisServiceHelpers.CreateHisService(
                currentHospital, httpClientFactory, databaseService, cachedService, logPrefix,
                    null, provinceRepository, districtRepository, wardRepository);

            //check if payment for membership flow
            if (payment.Status == PaymentConstant.Success)
            {
                var register = await databaseService.Registers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Number == payment.Receipt!.RegisterNumber, cancellationToken: cancellationToken);

                if (register != null && register.RegisterType == "DANG_KY_THANH_VIEN")
                {
                    // Handle membership flow
                    var createMembershipCommand = new CreateMembershipCommandRequest
                    {
                        CustomerId = register.CustomerId!,
                        PackageServiceId = register.HealthServiceId!,
                        LogPrefix = logPrefix,
                    };
                    
                    var createMembershipResult = await mediator.Send(createMembershipCommand, cancellationToken);
                    if (!createMembershipResult.Success)
                    {
                        Log.Error("{LogPrefix} Create membership failed: {Message}", logPrefix, createMembershipResult.Messages);
                    }
                }
            }
            
            var customerHospital = await databaseService.CustomerHospitals
                .FirstOrDefaultAsync(x => x.CustomerId == payment.Receipt!.CustomerId && x.HospitalId == hospital!.Id, cancellationToken: cancellationToken);

            //4. Call IPN
            (bool ipnResult, string ipnMessage, ErrorTypeEnum errorType, UpdatePaymentStatusResponse postbackRes)
                = await hisService.UpdatePaymentStatus(new UpdatePaymentStatusRequest
                {
                    MerchantId = hospital?.MerchantId ?? string.Empty,
                    InvoiceId = payment.Receipt!.RefNo ?? string.Empty,
                    PatientCode = customerHospital?.PatientCode ?? string.Empty,
                    TransactionId = request.Data.TransactionId,
                    KioskId = payment.Receipt!.DeviceId ?? string.Empty,
                    Status = request.Data.Status,
                    TransactionAmount = request.Data.PaidAmount,
                    PaidAmount = request.Data.PaidAmount,
                    TransactionDescription = request.Data.TransactionDescription,
                    PaidDescription = request.Data.PaidDescription,
                    PaidTime = request.Data.PaidAt ?? DateTime.UtcNow,
                    PaymentRefDocNo = payment.InvoiceInfoRef ?? string.Empty,
                    RegisterNumber = payment.Receipt!.RegisterNumber ?? string.Empty
                });

            Log.Information("{LogPrefix} Call Ipn Res {Result} - Message {Message} - Response {@Response}", logPrefix, ipnResult, ipnMessage, postbackRes);

            //5. Cập nhật trạng thái sau khi IPN
            payment.IpnUrl = hospital?.IpnUrl;
            payment.IpnMessage = ipnMessage;
            payment.IpnStatus = ipnResult ? "SUCCESS" : "FAIL";

            databaseService.Payments.Update(payment);
            
            var updatePaymentResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (updatePaymentResult < 1)
            {
                Log.Information("{LogPrefix} Update Ipn Fail", logPrefix);
                result.Set(false, PaymentConstant.UpdateIpnFail, ErrorTypeEnum.MediPayError);
                return result;
            }

            result.Set(true, PaymentConstant.Ok);

            return result;
        }
    }
}

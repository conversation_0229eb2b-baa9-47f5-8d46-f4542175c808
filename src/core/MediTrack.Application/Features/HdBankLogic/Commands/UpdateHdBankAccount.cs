using MediatR;
using MediBankClient.Lib.Model;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class UpdateHdBankAccount : IRequest<BaseCommandResult>
    {
        public string TranId { get; set; } = string.Empty;
        public string BirthDate { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string IdNumber { get; set; } = string.Empty;
        public string IdNumberOld { get; set; } = string.Empty;
        public string IdNumberType { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Note { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string KioskId { get; set; } = string.Empty;
        public FileEkycInfoModel? FileInfo { get; set; } = new();
        public AdditionalInfoModel? AdditionalInfo { get; set; } = new();
        public DeviceInfoModel? DeviceInfo { get; set; } = new();
        public AddressInfoModel? AddressInfo { get; set; } = new();
        public NfcInfoModel? NfcInfo { get; set; } = new();
        public RarInfoModel? RarInfo { get; set; } = new();
        public ResultC06Model? ResultC06 { get; set; } = new();
        public IdCheckModel? IdCheck { get; set; } = new();
    }

    public class UpdateHdBankAccountHandler(IMediBankService mediBankService,
        IDatabaseService databaseService) 
        : IRequestHandler<UpdateHdBankAccount, BaseCommandResult>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResult> Handle(
            UpdateHdBankAccount request, CancellationToken cancellationToken)
        {
            request.KioskId = string.IsNullOrEmpty(request.KioskId)
                ? request.DeviceInfo?.KioskId ?? string.Empty
                : request.KioskId;

            var kiosk = await databaseService.Kiosks.FirstOrDefaultAsync(a => a.Id == request.KioskId);
            Console.WriteLine($"kiosk --- {JsonConvert.SerializeObject(kiosk)}");
            if (kiosk == null)
                return new BaseCommandResult()
                {
                    Messages = "Kiosk not found",
                    Success = false
                };
            
            var hospital = await databaseService.Hospitals.FirstOrDefaultAsync(a => a.Id == kiosk.HospitalId);
            Console.WriteLine($"hospital --- {JsonConvert.SerializeObject(hospital)}");
            if (hospital == null)
                return new BaseCommandResult()
                {
                    Messages = "Hospital not found",
                    Success = false
                };
            
            if(request.AdditionalInfo == null)
            {
                request.AdditionalInfo = new AdditionalInfoModel
                {
                    MerchantId = hospital.BankTransferId
                };
            }
            else
            {
                request.AdditionalInfo.MerchantId = hospital.BankTransferId;
            }

            var requestUpdate = new UpdateCustomerInfoRequest
            {
                AdditionalInfo = request.AdditionalInfo,
                AddressInfo = request.AddressInfo,
                BirthDate = request.BirthDate,
                DeviceInfo = request.DeviceInfo,
                FullName = request.FullName,
                Email = request.Email,
                FileInfo = request.FileInfo,
                Gender = request.Gender,
                IdNumber = request.IdNumber,
                IdNumberOld = request.IdNumberOld,
                IdNumberType = request.IdNumberType,
                Note = request.Note,
                PhoneNumber = request.PhoneNumber,
                TranId = request.TranId,
                RarInfo = request.RarInfo,
                NfcInfo = request.NfcInfo,
                IdCheck = request.IdCheck,
                ResultC06 = request.ResultC06
            };

            Console.WriteLine($"requestUpdate --- {JsonConvert.SerializeObject(requestUpdate)}");

            var response = await mediBankService.UpdateCustomerInfo(request: requestUpdate);
            Console.WriteLine($"response --- {JsonConvert.SerializeObject(response)}");
            return new BaseCommandResult()
            {
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

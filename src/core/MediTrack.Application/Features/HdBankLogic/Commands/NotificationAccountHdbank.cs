﻿using HisClientV3.Lib.Model;
using MediatR;
using MediBankClient.Lib.Config;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PartnerLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class NotificationAccountHdbank : IRequest<object>
    {
        public string? requestId { get; set; }
        public string? requestTime { get; set; }
        public string? signature { get; set; }
        public NotificationSDKAccountHDBank? data { get; set; }
    }

    public class NotificationSDKAccountHDBank
    {
        public string? medipayId { get; set; }
        public string? phone { get; set; }
        public string? linkId { get; set; }
        public string? clientNameEN { get; set; }
        public string? clientNameVN { get; set; }
        public string? accountNo { get; set; }
        public string? clientNumber { get; set; }
        public string? partnerUserId { get; set; }
        public string? onboardingStatus { get; set; }
        public string? message { get; set; }
        public string? cardType { get; set; }
        public string? cardTier { get; set; }
        public string? cardStatus { get; set; }
        public string? tranId { get; set; }

        public string? flow { get; set; }
    }

    public enum NotificationAccountHdbankStatus
    {
        ACCOUNT_SUCCESS,// Mở tài khoản thanh toán thành công
        ACCOUNT_FAIL,// Mở tài khoản thanh toán thất bại
        DEBIT_SUCCESS, // Mở thẻ debit thành công
        DEBIT_FAIL, // Mở thẻ debit thất bại
    }

    public class NotificationAccountHdbankHandler(IDatabaseService databaseService,
        ILogger<NotificationAccountHdbankHandler> logger,
        IOptions<MediBankConfigModel> options)

         : IRequestHandler<NotificationAccountHdbank, object>
    {
        private readonly MediBankConfigModel medibank = options.Value ?? throw new NullReferenceException(nameof(MediBankConfigModel));

        public async Task<object> Handle(NotificationAccountHdbank request, CancellationToken cancellationToken)
        {
            string plainText = $"{request.requestId}{request.requestTime}" +
                $"{request.data!.medipayId}";

            if (!SignatureHelper.HmacSha256HashV2(plainText, medibank.SecretKeySDKCallbackAccount).Equals(request.signature))
                return new BaseResponseModel<CreateQRsPaymentQRDto>
                {
                    Message = "sign không hợp lệ",
                    Code = ErrorConstant.INVALID_SIGN
                };

            logger.LogInformation("NotificationAccountHdbank request - - - {request}", request);
            var customer = await databaseService.Customers
                .FindAsync(request.data!.medipayId, cancellationToken);
            if (customer == null)
                return new
                {
                    Success = false,
                    Messages = "Invaild customer"
                };

            var customerFollow = new CustomerFlow
            {
                CustomerId = customer.Id,
                Status = request.data!.onboardingStatus,
                Logs = JsonConvert.SerializeObject(request),
            };
            customer.AccountStatus = request.data!.onboardingStatus
                switch
            {
                "ACCOUNT_SUCCESS" => "S2", // Tài khoản thành công
                "ACCOUNT_FAIL" => "S3", // Tài khoản thất bại
                _ => "S1" // Trạng thái khác giữ nguyên
            };
            customer.BankTranId = request.data!.tranId;

            databaseService.Customers.Update(customer);
            databaseService.CustomerFlows.Add(customerFollow);
            var result = await databaseService.SaveChangesAsync(cancellationToken);
            logger.LogInformation("NotificationAccountHdbank result - - - {result}", result);
            return result > 0
                ? new
                {
                    Success = true
                }
                : new
                {
                    Success = false,
                    Messages = "Update Customer fail"
                };

        }
    }
}
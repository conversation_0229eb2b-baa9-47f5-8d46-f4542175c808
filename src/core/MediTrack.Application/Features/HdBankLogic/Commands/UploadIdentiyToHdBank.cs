﻿using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HdBankLogic.Dtos;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class UploadIdentiyToHdBank : IRequest<BaseCommandResultWithData<UploadIdentityToHdBankResponse>>
    {
        public string FrontFileName { get; set; } = string.Empty;
        public string BackFileName { get; set; } = string.Empty;
        public string FrontImage { get; set; } = string.Empty;
        public string BackImage { get; set; } = string.Empty;
        public string Identity { get; set; } = string.Empty;
    }

    public class UploadIdentiyToHdBankHandler (IMediBankService mediBankService) : IRequestHandler<UploadIdentiyToHdBank, BaseCommandResultWithData<UploadIdentityToHdBankResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResultWithData<UploadIdentityToHdBankResponse>> Handle(
            UploadIdentiyToHdBank request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.UploadIdentityToHdBank(new UploadIdentityToHdBankRequest()
            {
                BackFileName = request.BackFileName,
                BackImage = request.BackImage,
                FrontFileName = request.FrontFileName,
                FrontImage = request.FrontImage,
                Identity = request.Identity
            });
            return new BaseCommandResultWithData<UploadIdentityToHdBankResponse>()
            {
                Data = response.Data,
                Messages = response.Message,
                Success = response.Code == "00"
            };
        }
    }
}

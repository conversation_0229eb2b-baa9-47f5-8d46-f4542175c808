﻿
using MediBankClient.Lib.Response;
using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class UploadFaceToHdBank : IRequest<BaseCommandResultWithData<UploadFileResponse>>
    {
        public string FaceImage { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string Identity { get; set; } = string.Empty;
    }

    public class UploadFaceToHdBankHandler (IMediBankService mediBankService) : IRequestHandler<UploadFaceToHdBank, BaseCommandResultWithData<UploadFileResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResultWithData<UploadFileResponse>> Handle(
            UploadFaceToHdBank request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.UploadFile(new UploadFileRequest()
            {
                FaceImage = request.FaceImage,
                FileName = request.FileName,
                Identity = request.Identity
            });

            return new BaseCommandResultWithData<UploadFileResponse>
            {
                Data = response.Data,
                Messages = response.Message,
                Success = response.Code == "00"
            };
        }
    }
}

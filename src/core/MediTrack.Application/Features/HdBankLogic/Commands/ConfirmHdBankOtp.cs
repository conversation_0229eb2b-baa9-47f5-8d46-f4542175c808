﻿using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using System.Text.Json.Serialization;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class ConfirmHdBankOtp : IRequest<BaseCommandResult>
    {
        [JsonIgnore]
        public string RequestId { get; set; } = string.Empty;
        public string OtpCode { get; set; } = string.Empty;
        public string IdNumber { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string TranId { get; set; } = string.Empty;
    }

    public class ConfirmHdBankOtpHandler(IMediBankService mediBankService) 
        : IRequestHandler<ConfirmHdBankOtp, BaseCommandResult>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResult> Handle(
            ConfirmHdBankOtp request, CancellationToken cancellationToken)
        {
           
            var response = await mediBankService.ConfirmOtp(request: new ConfirmHdBankOtpRequest
            {
                IdNumber = request.IdNumber,
                PhoneNumber = request.PhoneNumber,
                TranId = request.TranId,
                OtpCode = request.OtpCode
            });

            return new BaseCommandResult()
            {
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

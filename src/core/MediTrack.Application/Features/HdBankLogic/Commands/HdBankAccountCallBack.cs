using Amazon.Runtime.Internal.Util;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HdBankLogic.Dtos;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class HdBankAccountCallBack : HdBankBaseModel<HdBankCallBackRequest>, IRequest<BaseCommandResultWithData<HdBankBaseResponse>>
    {
    }

    public class HdBankAccountCallBackHandler(IDatabaseService databaseService,
        ILogger<HdBankAccountCallBackHandler> logger)
        : IRequestHandler<HdBankAccountCallBack, BaseCommandResultWithData<HdBankBaseResponse>>
    {
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ILogger<HdBankAccountCallBackHandler> logger = logger;

        public async Task<BaseCommandResultWithData<HdBankBaseResponse>> Handle(
            HdBankAccountCallBack request, CancellationToken cancellationToken)
        {
            logger.LogInformation("request - - - {request}", request);
            var account = await databaseService.Customers.FirstOrDefaultAsync(a => a.IdentityNo == request.Data!.IdentityNo
                && (string.IsNullOrEmpty(a.BankTranId) || string.IsNullOrEmpty(request.Data!.TranId) || a.BankTranId == request.Data!.TranId));
            logger.LogInformation("account - - - {account}", account);
            if (account == null)
                return new BaseCommandResultWithData<HdBankBaseResponse>()
                {
                    Messages = "Customer Not Found",
                    Success = false
                };

            account.AccountStatus =  request.Data!.Status == "SUCCESS" ? "S2" : account.AccountStatus;
            databaseService.Customers.Update(account);
            var result = await databaseService.SaveChangesAsync(cancellationToken);


            return result > 0
                ? new BaseCommandResultWithData<HdBankBaseResponse>()
                {
                    Data = new HdBankBaseResponse
                    {
                        ResponseCode = "00",
                        ResponseId = Guid.NewGuid().ToString(),
                        ResponseMessage = "",
                        ResponseTime = DateTime.UtcNow
                    },
                    Success = true
                }
                : new BaseCommandResultWithData<HdBankBaseResponse> 
                {
                    Success = false,
                    Messages = "Update Customer fail"
                };
        }
    }
}

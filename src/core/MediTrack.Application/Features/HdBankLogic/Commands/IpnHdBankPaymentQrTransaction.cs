using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HdBankLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class IpnHdBankPaymentQrTransaction : HdBankBaseModel<UpdatePaymentQrTransactionStatusDto>, IRequest<BaseCommandResultWithData<HdBankBaseResponse>>
    {
    }

    public class IPNHdBankPaymentQrTransactionHandler(IDatabaseService databaseService) : IRequestHandler<IpnHdBankPaymentQrTransaction, BaseCommandResultWithData<HdBankBaseResponse>>
    {
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<HdBankBaseResponse>> Handle(
            IpnHdBankPaymentQrTransaction request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.Data?.RefNo))
                return new BaseCommandResultWithData<HdBankBaseResponse>()
                {
                    Messages = "Receipt not found",
                    Success = false
                };

            var payment = await databaseService.Payments.FirstOrDefaultAsync(a => a.QrTransactionId == request.Data.TransactionId);

            if (payment == null)
                return new BaseCommandResultWithData<HdBankBaseResponse>()
                {
                    Messages = "Payment not found",
                    Success = false
                };

            payment.IpnMessage =  request.Data.ResponseCode switch
            {
                "200" => "Status Success",
                "150" => "Status Fail",
                "100" => "Status Create",
                "120" => "Status Expired",
                _ => payment.IpnMessage
            };
            payment.Status = request.Data.ResponseCode switch
            {
                "200" => PaymentConstant.Success,
                "150" => PaymentConstant.FailPayment,
                "100" => PaymentConstant.CreateSuccessFully,
                "120" => PaymentConstant.Expired,
                _ => payment.Status
            };
            databaseService.Payments.Update(payment);
            var result = await databaseService.SaveChangesAsync(cancellationToken);

            return result > 0 
                ? new BaseCommandResultWithData<HdBankBaseResponse>()
                {
                    Data = new HdBankBaseResponse
                    {
                        ResponseCode = "00",
                        ResponseId = Guid.NewGuid().ToString(),
                        ResponseMessage = "",
                        ResponseTime = DateTime.UtcNow
                    },
                    Success = true
                }
                : new BaseCommandResultWithData<HdBankBaseResponse> 
                {
                    Success = false,
                    Messages = "Payment update fail"
                };
        }
    }
}

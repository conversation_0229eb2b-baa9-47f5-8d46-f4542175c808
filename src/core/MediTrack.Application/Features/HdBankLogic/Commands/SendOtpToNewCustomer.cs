﻿using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class SendOtpToNewCustomer : IRequest<BaseCommandResult>
    {
        public string RequestId { get; set; } = string.Empty;
        public string IdNumber { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string TranId { get; set; } = string.Empty;
    }

    public class SendOtpToNewCustomerHandler(IMediBankService mediBankService) 
        : IRequestHandler<SendOtpToNewCustomer, BaseCommandResult>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResult> Handle(
            SendOtpToNewCustomer request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.SendOtpToNewCustomer(request: new SendOtpToNewCustomerRequest
            {
                IdNumber = request.IdNumber,
                PhoneNumber = request.PhoneNumber,
                TranId = request.TranId,
            });

            return new BaseCommandResult()
            {
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

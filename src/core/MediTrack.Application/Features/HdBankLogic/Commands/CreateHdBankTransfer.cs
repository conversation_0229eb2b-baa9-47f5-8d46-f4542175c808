﻿using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class CreateHdBankTransfer : IRequest<BaseCommandResultWithData<CreateTransferResponse>>
    {
        public string KioskId {get; set;} = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public int AmountRefund { get; set; }
        public int AmountBeneficiary { get; set; }
        public List<string> OrderId { get; set; } = new();
    }

    public class CreateHdBankTransferHandler(IMediBankService mediBankService,
        IDatabaseService databaseService,
        ILogger<CreateHdBankTransferHandler> logger)
        : IRequestHandler<CreateHdBankTransfer, BaseCommandResultWithData<CreateTransferResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ILogger<CreateHdBankTransferHandler> logger = logger;

        public async Task<BaseCommandResultWithData<CreateTransferResponse>> Handle(
            CreateHdBankTransfer request, CancellationToken cancellationToken)
        {
            var kiosk = await databaseService.Kiosks.FirstOrDefaultAsync(a => a.Id == request.KioskId);
            logger.LogInformation("kiosk - - - {kiosk}", kiosk);
            if (kiosk == null)
                return new BaseCommandResultWithData<CreateTransferResponse>()
                {
                    Messages = "Kiosk not found",
                    Success = false
                };

            var hospital = await databaseService.Hospitals.FirstOrDefaultAsync(a => a.Id == kiosk.HospitalId);
            logger.LogInformation("hospital - - - {hospital}", hospital);
            if (hospital == null)
                return new BaseCommandResultWithData<CreateTransferResponse>()
                {
                    Messages = "Hospital not found",
                    Success = false
                };

            var customer = await databaseService.Customers.FirstOrDefaultAsync(a => a.Id == request.CustomerId);
            logger.LogInformation("customer - - - {customer}", hospital);
            if (customer == null)
                return new BaseCommandResultWithData<CreateTransferResponse>()
                {
                    Messages = "Customer not found",
                    Success = false
                };

            var requestCreateTransfer = new CreateTransferRequest
            {
                AmountBeneficiary = request.AmountBeneficiary,
                AmountRefund = request.AmountRefund,
                MerchantId = hospital.BankTransferId,
                OrderId = request.OrderId,
                TranId = customer.BankTranId ?? string.Empty
            };

            logger.LogInformation("requestCreateTransfer - - - {requestCreateTransfer}", hospital);

            var response = await mediBankService.CreateTransfer(requestCreateTransfer);

            return new BaseCommandResultWithData<CreateTransferResponse>()
            {
                Success = response.Code == "00",
                Messages = response.Message,
                Data = response.Data
            };
        }
    }
}

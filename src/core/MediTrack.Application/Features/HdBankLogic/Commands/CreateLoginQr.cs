using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class CreateLoginQr : CreateLoginQrRequest, IRequest<BaseCommandResultWithData<CreateLoginQrResponse>>
    {
    }

    public class CreateLoginQrHandler(IMediBankService mediBankService) 
        : IRequestHandler<CreateLoginQr, BaseCommandResultWithData<CreateLoginQrResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResultWithData<CreateLoginQrResponse>> Handle(
            CreateLoginQr request, CancellationToken cancellationToken)
        {
           
            var response = await mediBankService.CreateLoginQr(request: new CreateLoginQrRequest
            {
                TranId = request.TranId,
                Location = request.Location,
                UserId = request.UserId
            });

            return new BaseCommandResultWithData<CreateLoginQrResponse>()
            {
                Success = response.Code == "00",
                Messages = response.Message,
                Data = response.Data
            };
        }
    }
}

using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Commands
{
    public class CreateHdBankQrPaymentRequest : IRequest<BaseCommandResultWithData<CreatePaymentResponse>>
    {
        public string TerminalId { get; set; } = string.Empty;
        public string OrderId { get; set; } = string.Empty;
        public string BillNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Amount { get; set; }
        public string CustomerId { get; set; } = string.Empty;
        public string KioskId {get; set;} = string.Empty;
    }

    public class CreateHdBankQrPaymentHandler(IMediBankService mediBankService,
        IDatabaseService databaseService,
        ILogger<CreateHdBankQrPaymentHandler> logger) 
        : IRequestHandler<CreateHdBankQrPaymentRequest, BaseCommandResultWithData<CreatePaymentResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ILogger<CreateHdBankQrPaymentHandler> logger = logger;

        public async Task<BaseCommandResultWithData<CreatePaymentResponse>> Handle(
            CreateHdBankQrPaymentRequest request, CancellationToken cancellationToken)
        {
            var kiosk = await databaseService.Kiosks.FirstOrDefaultAsync(a => a.Id == request.KioskId);
            logger.LogInformation("kiosk - - - {kiosk}", kiosk);
            if (kiosk == null)
                return new BaseCommandResultWithData<CreatePaymentResponse>()
                {
                    Messages = "Kiosk not found",
                    Success = false
                };

            var hospital = await databaseService.Hospitals.FirstOrDefaultAsync(a => a.Id == kiosk.HospitalId);
            logger.LogInformation("hospital - - - {hospital}", hospital);
            if (hospital == null)
                return new BaseCommandResultWithData<CreatePaymentResponse>()
                {
                    Messages = "Hospital not found",
                    Success = false
                };

            var customer = await databaseService.Customers.FirstOrDefaultAsync(a => a.Id == request.CustomerId);
            logger.LogInformation("customer - - - {customer}", hospital);
            if (customer == null)
                return new BaseCommandResultWithData<CreatePaymentResponse>()
                {
                    Messages = "Customer not found",
                    Success = false
                };

            var requestCreateQrTransaction = new CreateHdBankQrPayment
            {
                Amount = request.Amount,
                BillNumber = request.BillNumber,
                TranId = customer.BankTranId ?? string.Empty,
                Description = request.Description,
                MerchantId = hospital.BankTransferId,
                OrderId = request.OrderId,
                TerminalId = request.TerminalId
            };

            logger.LogInformation("requestCreateQrTransaction - - - {requestCreateQrTransaction}", requestCreateQrTransaction);
            var response = await mediBankService.CreateQrTransaction(requestCreateQrTransaction);

            return new BaseCommandResultWithData<CreatePaymentResponse>()
            {
                Success = response.Code == "00",
                Messages = response.Message,
                Data = response.Data
            };
        }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.HdBankLogic.Dtos
{
    public class UploadIdentityResultDto
    {
        public string FrontFileName { get; set; } = string.Empty;
        public string FrontFilePath { get; set; } = string.Empty;
        public string FrontDocumentId { get; set; } = string.Empty;
        public string BackFileName { get; set; } = string.Empty;
        public string BackFilePath { get; set; } = string.Empty;
        public string BackDocumentId { get; set; } = string.Empty;
    }
}

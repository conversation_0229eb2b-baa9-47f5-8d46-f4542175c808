namespace MediTrack.Application.Features.HdBankLogic.Dtos
{
    public class AccountAddressInfo
    {
        // Địa chỉ chi tiết
        public string? Address { get; set; }
        // Địa chỉ liên hệ
        public string? ContactAddress { get; set; }
        // Quận/<PERSON><PERSON>ện liên hệ
        public string? DistrictIdContact { get; set; }
        // Quận/Huyện hiện tại
        public string? DistrictIdCurrent { get; set; }
        public string? PermanentAddress { get; set; }
        public string? StateIdContact { get; set; }
        public string? StateIdCurrent { get; set; }
        // Tên đường liên hệ
        public string? StreetNameContact { get; set; }
        // Phường/Xã liên hệ
        public string? WardIdContact { get; set; }
        // Phường/Xã hiện tại
        public string? WardIdCurrent { get; set; }
    }
}
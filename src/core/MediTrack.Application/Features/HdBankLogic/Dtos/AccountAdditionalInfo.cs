namespace MediTrack.Application.Features.HdBankLogic.Dtos
{
    public class AccountAdditionalInfo
    {
        // Nghề nghiệp
        public string CareerId { get; set; } = string.Empty;
        // <PERSON><PERSON><PERSON> cấp giấy tờ
        public string DateOfIssue { get; set; } = string.Empty;
        // Ng<PERSON>y hết hạn giấy tờ
        public string ExpireOfIssue { get; set; } = string.Empty;
        public string? LiteracyDesc { get; set; } = string.Empty;
        // Tình trạng hôn nhân
        public string? MaritalStatus { get; set; } = string.Empty;
        // Nơi cấp giấy tờ
        public string PlaceOfIssue { get; set; } = string.Empty;
        // Chức vụ
        public string? PositionId { get; set; } = string.Empty;
        // Thu nhập
        public string? SalaryIncome { get; set; } = string.Empty;
    }
}
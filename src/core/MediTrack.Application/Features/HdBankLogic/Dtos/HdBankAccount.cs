namespace MediTrack.Application.Features.HdBankLogic.Dtos
{
    public class HdBankAccount
    {
        // truyền cố định MEDIPAY
        public string PartnerId { get; set; } = "MEDIPAY";
        // Mã định danh tài khoản của đối tác
        public string PartnerUserId { get; set; } = string.Empty;
        //  Mã định danh giao dịch
        public string TranId { get; set; } = string.Empty;
        // Ngày sinh  yyyy-MM-dd
        public string BirthDate { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        // Họ tên khách hàng
        public string FullName { get; set; } = string.Empty;
        //  Giới tính Nam lưu trị là  “M”  Nữ lưu trị là “F”
        public string Gender { get; set; } = string.Empty;
        // Số giấy tờ tùy thân
        public string IdNumber { get; set; } = string.Empty;
        // Số giấy tờ tùy thân cũ 
        public string IdNumberOld { get; set; } = string.Empty;
        //  Loại giấy tờ tùy thân CMND, CCCD hoặc CCGC 
        public string IdNumberType { get; set; } = string.Empty;
        // Số điện thoại
        public string PhoneNumber { get; set; } = string.Empty;
        // Ghi chú
        public string Note { get; set; } = string.Empty;
        //  Thông tin giấy tờ tùy thân
        // Upload hình ảnh lên Service DMS , sau đó lấy documentId lưu vào đây //TODO 
        public AccountFileEkycInfo FileEkycInfo { get; set; } = new();
        // Thông tin thiết bị
        public AccountDeviceInfo DeviceInfo { get; set; } = new();
        ///  Thông tin địa chỉ
        public AccountAddressInfo AddressInfo { get; set; } = new();
        // Thông tin hợp đồng
        public AccountContractInfo ContractInfo { get; set; } = new();
        // Thông tin bổ sung
        public AccountAdditionalInfo AdditionalInfo { get; set; } = new();
        // Tổng hợp thông tin
        public AccountSummarizeInfo SummarizeInfo { get; set; } = new();
    }
}
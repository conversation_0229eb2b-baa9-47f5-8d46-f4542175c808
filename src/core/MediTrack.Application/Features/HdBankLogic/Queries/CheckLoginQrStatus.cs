using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class CheckLoginQrStatus : IRequest<BaseCommandResultWithData<object>>
    {
        public string TranId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string AuthCode { get; set; } = string.Empty;
    }

    public class CheckLoginQrStatusHandler(IMediBankService mediBankService,
        IDatabaseService databaseService) 
        : IRequestHandler<CheckLoginQrStatus, BaseCommandResultWithData<object>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<object>> Handle(
            CheckLoginQrStatus request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.CheckLoginQrStatus(request: new CheckLoginQrStatusRequest
            {
                AuthCode = request.AuthCode,
                TranId = request.TranId,
                UserId = request.UserId
            });

            return new BaseCommandResultWithData<object>()
            {
                Data = response.Data,
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class CheckHdBankAccount : IRequest<BaseCommandResultWithData<CheckAccountResponse>>
    {
        public string FullName { get; set; } = string.Empty;
        public string IdNumber { get; set; } = string.Empty;
        public string IdNumberType { get; set; } = "CCCD";
        public string PhoneNumber { get; set; } = string.Empty;
        public string BirthDate { get; set; } = "1";
    }

    public class CheckHdBankAccountHandler(IMediBankService mediBankService,
        IDatabaseService databaseService) 
        : IRequestHandler<CheckHdBankAccount, BaseCommandResultWithData<CheckAccountResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<CheckAccountResponse>> Handle(
            CheckHdBankAccount request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.CheckHdBankAccount(request: new CheckAccountRequest
            {
                BirthDate = request.BirthDate,
                FullName = request.FullName,
                IdNumber = request.IdNumber,
                IdNumberType = request.IdNumberType,
                PhoneNumber = request.PhoneNumber
            });

            return new BaseCommandResultWithData<CheckAccountResponse>()
            {
                Data = response.Data,
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

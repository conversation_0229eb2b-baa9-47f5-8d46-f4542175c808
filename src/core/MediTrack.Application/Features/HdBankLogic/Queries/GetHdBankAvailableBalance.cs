﻿using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class GetHdBankAvailableBalance : 
        IRequest<BaseCommandResultWithData<AvailableBalanceResponse>>
    {
        public string CustomerId { get; set; } = string.Empty;
        public string KioskId { get; set; } = string.Empty;
    }

    public class GetHdBankAvailableBalanceHandler(IMediBankService mediBankService,
        IDatabaseService databaseService,
        ILogger<GetHdBankAvailableBalanceHandler> logger)
        : IRequestHandler<GetHdBankAvailableBalance, BaseCommandResultWithData<AvailableBalanceResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ILogger<GetHdBankAvailableBalanceHandler> logger = logger;

        public async Task<BaseCommandResultWithData<AvailableBalanceResponse>> Handle(
            GetHdBankAvailableBalance request, CancellationToken cancellationToken)
        {
            var kiosk = await databaseService.Kiosks.FirstOrDefaultAsync(a => a.Id == request.KioskId);
            logger.LogInformation("kiosk - - - {kiosk}", kiosk);
            if (kiosk == null)
                return new BaseCommandResultWithData<AvailableBalanceResponse>()
                {
                    Messages = "Kiosk not found",
                    Success = false
                };

            var hospital = await databaseService.Hospitals.FirstOrDefaultAsync(a => a.Id == kiosk.HospitalId);
            logger.LogInformation("hospital - - - {hospital}", hospital);
            if (hospital == null)
                return new BaseCommandResultWithData<AvailableBalanceResponse>()
                {
                    Messages = "Hospital not found",
                    Success = false
                };

            var customer = await databaseService.Customers.FirstOrDefaultAsync(a => a.Id == request.CustomerId);
            logger.LogInformation("customer - - - {customer}", hospital);
            if (customer == null)
                return new BaseCommandResultWithData<AvailableBalanceResponse>()
                {
                    Messages = "Customer not found",
                    Success = false
                };

            var response = await mediBankService.GetAvailableBalance(new GetHdBankAvailableBalanceRequest 
            {
                MerchantId = hospital.BankTransferId,
                TranId = customer.BankTranId ?? string.Empty
            });

            return new BaseCommandResultWithData<AvailableBalanceResponse>()
            {
                Success = response.Code == "00",
                Messages = response.Message,
                Data = response.Data
            };
        }
    }
}

using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class GetAllHdBankAccount : IRequest<BaseCommandResultWithData<List<HdBankAccountResponse>>>
    {
        public string TranId { get; set; } = string.Empty;
        public string IdNumber { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string AuthCode { get; set; } = string.Empty;
    }

    public class GetAllHdBankAccountHandler(IMediBankService mediBankService,
        IDatabaseService databaseService) 
        : IRequestHandler<GetAllHdBankAccount, BaseCommandResultWithData<List<HdBankAccountResponse>>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<List<HdBankAccountResponse>>> Handle(
            GetAllHdBankAccount request, CancellationToken cancellationToken)
        {
            var response = await mediBankService.GetAllAccount(request: new GetAllAccountHdBankRequest
            {
                AuthCode = request.AuthCode,
                TranId = request.TranId,
                IdNumber = request.IdNumber,
                PhoneNumber = request.PhoneNumber
            });

            return new BaseCommandResultWithData<List<HdBankAccountResponse>>()
            {
                Data = response.Data,
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

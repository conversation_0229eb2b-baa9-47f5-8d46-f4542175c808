using MediatR;
using MediBankClient.Lib.Request;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class GetStatusAccountLink : IRequest<BaseCommandResultWithData<object>>
    {
        public string AcctNo { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string CustomerPhone {get; set;} = string.Empty;
        public string KioskId { get; set; } = string.Empty;
    }

    public class GetStatusAccountLinkHandler(IMediBankService mediBankService,
        IDatabaseService databaseService,
        ILogger<GetStatusAccountLinkHandler> logger)
        : IRequestHandler<GetStatusAccountLink, BaseCommandResultWithData<object>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ILogger<GetStatusAccountLinkHandler> logger = logger;

        public async Task<BaseCommandResultWithData<object>> Handle(
            GetStatusAccountLink request, CancellationToken cancellationToken)
        {
            var kiosk = await databaseService.Kiosks.FirstOrDefaultAsync(a => a.Id == request.KioskId);
            logger.LogInformation("kiosk - - - {kiosk}", kiosk);
            if (kiosk == null)
                return new BaseCommandResultWithData<object>()
                {
                    Messages = "Kiosk not found",
                    Success = false
                };

            var hospital = await databaseService.Hospitals.FirstOrDefaultAsync(a => a.Id == kiosk.HospitalId);
            logger.LogInformation("hospital - - - {hospital}", hospital);
            if (hospital == null)
                return new BaseCommandResultWithData<object>()
                {
                    Messages = "Hospital not found",
                    Success = false
                };

            var customer = await databaseService.Customers.FirstOrDefaultAsync(a => a.Id == request.CustomerId);
            logger.LogInformation("customer - - - {customer}", hospital);
            if (customer == null)
                return new BaseCommandResultWithData<object>()
                {
                    Messages = "Customer not found",
                    Success = false
                };


            var response = await mediBankService.GetStatusAccountLink(request: new  GetStatusAccountLinkRequest
            {
                AcctNo = request.AcctNo,
                MerchantId = hospital.BankTransferId,
                PhoneNumber = request.CustomerPhone,
                TranId = customer.BankTranId ?? string.Empty
            });

            if(response.Code == "00")
            {
                customer.AccountStatus = "S2";
                databaseService.Customers.Update(customer);
                var resultUpdate = databaseService.SaveChangesAsync(cancellationToken);
            }

            return new BaseCommandResultWithData<object>()
            {
                Data = response.Data,
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

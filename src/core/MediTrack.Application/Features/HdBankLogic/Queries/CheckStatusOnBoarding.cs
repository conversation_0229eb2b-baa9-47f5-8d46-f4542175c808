﻿using MediatR;
using MediBankClient.Lib.Request;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class CheckStatusOnBoarding
        : IRequest<BaseCommandResultWithData<CheckStatusOnBoardingResponse>>
    {
        public string TranId { get; set; } = string.Empty;
    }

    public class CheckStatusOnBoardingHandler(IMediBankService mediBankService)
        : IRequestHandler<CheckStatusOnBoarding, BaseCommandResultWithData<CheckStatusOnBoardingResponse>>
    {
        private readonly IMediBankService mediBankService = mediBankService;

        public async Task<BaseCommandResultWithData<CheckStatusOnBoardingResponse>> Handle(
            CheckStatusOnBoarding request, CancellationToken cancellationToken)
        {

            var response = await mediBankService.CheckStatusOnBoarding(request: new CheckStatusOnBoardingRequest
            {
                TranId = request.TranId
            });

            return new BaseCommandResultWithData<CheckStatusOnBoardingResponse>()
            {
                Data = response.Data,
                Success = response.Code == "00",
                Messages = response.Message
            };
        }
    }
}

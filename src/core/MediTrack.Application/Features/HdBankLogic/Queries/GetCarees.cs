using MediatR;
using MediBankClient.Lib.Response;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace MediTrack.Application.Features.HdBankLogic.Queries
{
    public class GetCarees : 
        IRequest<BaseCommandResultWithData<List<CareerResponse>>>
    {
    }

    public class GetCareesHandler(IMediBankService mediBankService,
        ILogger<GetCareesHandler> logger,
        IMemoryCache memoryCache)
        : IRequestHandler<GetCarees, BaseCommandResultWithData<List<CareerResponse>>>
    {
        private readonly IMediBankService mediBankService = mediBankService;
        private readonly ILogger<GetCareesHandler> logger = logger;
        private readonly IMemoryCache memoryCache = memoryCache;

        public async Task<BaseCommandResultWithData<List<CareerResponse>>> Handle(
            GetCarees request, CancellationToken cancellationToken)
        {
            if(memoryCache.TryGetValue<List<CareerResponse>>(key: "GetCarees", out List<CareerResponse>? careeesFromDb) && careeesFromDb?.Count > 0)
            {
                 return new BaseCommandResultWithData<List<CareerResponse>>()
                {
                    Success = true,
                    Messages = PaymentConstant.Success,
                    Data = careeesFromDb
                };
            }
           
            var response = await mediBankService.GetCarees();

            if(response.Data?.Count > 0)
            {
                memoryCache.Set(key: "GetCarees", response.Data, DateTime.UtcNow.AddMinutes(30));
            }

            return new BaseCommandResultWithData<List<CareerResponse>>()
            {
                Success = response.Code == "00",
                Messages = response.Message,
                Data = response.Data ?? new()
            };
        }
    }
}

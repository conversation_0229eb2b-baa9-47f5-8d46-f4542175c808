using FluentValidation;
using MediTrack.Application.Features.HdBankLogic.Commands;

namespace MediTrack.Application.Features.HdBankLogic.Validators
{
    public class HdBankAccountCallBackValidator
        : AbstractValidator<HdBankAccountCallBack>
    {
        public HdBankAccountCallBackValidator()
        {
            RuleFor(x => x.RequestId).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("RequestId can't be null or empty");

            RuleFor(x => x.RequestTime).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("RequestTime can't be null or empty");

            RuleFor(x => x.Signature).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Signature can't be null or empty");

            RuleFor(a => a.Data).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Data can't be null or empty");

            RuleFor(x => x.Data!.PartnerId).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Data.PartnerId can't be null or empty");

            RuleFor(x => x.Data!.TranId).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Data.TransactionId can't be null or empty");

            RuleFor(x => x.Data!.Status).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("Data.Status can't be null or empty");
        }
    }
}

using FluentValidation;
using MediTrack.Application.Features.HdBankLogic.Queries;

namespace MediTrack.Application.Features.HdBankLogic.Validators
{
    public class CheckHdBankAccountValidator
        : AbstractValidator<CheckHdBankAccount>
    {
        public CheckHdBankAccountValidator()
        {
            RuleFor(x => x.FullName).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("FullName can't be null or empty");

            RuleFor(x => x.IdNumber).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("IdNumber can't be null or empty");

            RuleFor(x => x.IdNumberType).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("IdNumberType can't be null or empty");

            RuleFor(a => a.PhoneNumber).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("PhoneNumber can't be null or empty");
            
            RuleFor(a => a.BirthDate).NotEmpty()
                .NotNull().WithErrorCode("400")
                .WithMessage("BirthDate can't be null or empty");
        }
    }
}

namespace MediTrack.Application.Features.KioskLogic.Dto
{
    public class KioskAdvertisingMediaDto
    {
        public string Id { get; set; } = string.Empty;
        public string PartnerId { get; set; } = string.Empty;
        public string PartnerName { get; set; } = string.Empty;
        public int DisplayOrder { get; set; }
        public int MediaOrder { get; set; }
        public string MediaType { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FileFormat { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int DurationInSeconds { get; set; }
    }
}
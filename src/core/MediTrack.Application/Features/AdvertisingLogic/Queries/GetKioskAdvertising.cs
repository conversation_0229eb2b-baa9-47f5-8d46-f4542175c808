using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.KioskLogic.Dto;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.AdvertisingLogic.Queries
{
    public class GetKioskAdvertising : IRequest<BaseCommandResultWithData<List<KioskAdvertisingDto>>>
    {
    }

    public class GetKioskAdvertisingHandler(
        IDatabaseService databaseService,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService,
        ICachedService cachedService)
        : IRequestHandler<GetKioskAdvertising, BaseCommandResultWithData<List<KioskAdvertisingDto>>>
    {
        private const string KioskCampaignCacheKey = "KioskCampaign_";

        public async Task<BaseCommandResultWithData<List<KioskAdvertisingDto>>> Handle(
            GetKioskAdvertising request,
            CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<KioskAdvertisingDto>>();
            try
            {
                // Get kiosk ID from current context
                string kioskId = currentHospitalService.KioskId;

                if (string.IsNullOrEmpty(kioskId))
                {
                    result.Set(false, "Kiosk ID not found in current context");
                    return result;
                }

                var kiosk = await hisServiceHelper.GetKiosk(kioskId, databaseService, cancellationToken);

                if (kiosk == null)
                {
                    result.Set(false, KioskConstant.NotFound);
                    return result;
                }

                // If kiosk is not participating in any campaign
                if (!kiosk.IsParticipatingInCampaign)
                {
                    result.Set(true, "Kiosk is not participating in any campaign", []);
                    return result;
                }

                // Get kiosk advertising information from cache
                List<KioskAdvertisingDto>? cachedData = await cachedService.GetAsync<List<KioskAdvertisingDto>>($"{KioskCampaignCacheKey}{kioskId}", cancellationToken);

                if (cachedData is not null && cachedData.Count > 0)
                {
                    result.Set(true, "OK", cachedData);
                    return result;
                }

                var kioskCampaigns = await databaseService.CampaignParticipations
                .Include(cp => cp.Campaign)
                .Where(cp => cp.Campaign != null && cp.KioskId == kioskId && cp.Campaign.IsActive)
                .OrderByDescending(cp => cp.CreatedAt)
                .ToListAsync(cancellationToken);

                if (kioskCampaigns == null || kioskCampaigns.Count == 0)
                {
                    result.Set(true, "Kiosk has no campaign assigned", []);
                    return result;
                }

                var currentDateTime = DateTime.UtcNow;
                var currentTimeOfDay = DateTime.UtcNow.AddHours(7).TimeOfDay;
                var resultList = new List<KioskAdvertisingDto>();

                // Process each campaign
                foreach (var kioskCampaign in kioskCampaigns)
                {
                    var campaign = kioskCampaign.Campaign;

                    // Check if campaign is currently running
                    bool isCurrentlyRunning = campaign.IsActive &&
                        campaign.StartDate <= currentDateTime &&
                        (!campaign.EndDate.HasValue || campaign.EndDate >= currentDateTime) &&
                        (!campaign.DailyStartTime.HasValue || campaign.DailyStartTime <= currentTimeOfDay) &&
                        (!campaign.DailyEndTime.HasValue || campaign.DailyEndTime >= currentTimeOfDay);

                    // Skip campaigns that are not currently running
                    if (!isCurrentlyRunning) continue;

                    // Get all partner campaign media for this campaign
                    var mediaItems = await databaseService.AdvertisingPartnerCampaigns
                        .Include(x => x.Partner)
                        .Where(x => x.CampaignId == campaign.Id && x.IsMediaActive)
                        .OrderBy(x => x.DisplayOrder)
                        .ThenBy(x => x.MediaOrder)
                        .Select(x => new KioskAdvertisingMediaDto
                        {
                            Id = x.Id,
                            PartnerId = x.PartnerId,
                            PartnerName = x.Partner.Name,
                            DisplayOrder = x.DisplayOrder,
                            MediaOrder = x.MediaOrder,
                            MediaType = x.MediaType,
                            FilePath = x.FilePath,
                            FileName = x.FileName,
                            FileFormat = x.FileFormat,
                            FileSize = x.FileSize,
                            Width = x.Width,
                            Height = x.Height,
                            DurationInSeconds = x.DurationInSeconds
                        })
                        .ToListAsync(cancellationToken);

                    // Skip campaigns with no media
                    if (mediaItems.Count == 0) continue;

                    // Add campaign to result list
                    resultList.Add(new KioskAdvertisingDto()
                    {
                        Campaign = new KioskAdvertisingCampaignDto
                        {
                            Id = campaign.Id,
                            Name = campaign.Name,
                            Description = campaign.Description,
                            StartDate = campaign.StartDate,
                            EndDate = campaign.EndDate,
                            DailyStartTime = campaign.DailyStartTime,
                            DailyEndTime = campaign.DailyEndTime,
                        },
                        MediaItems = mediaItems
                    });
                }

                // Set cache
                await cachedService.SetAsync($"{KioskCampaignCacheKey}{kioskId}", resultList, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECONDS_PER_DAY);

                result.Set(true, "OK", resultList);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when getting kiosk advertising information", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}
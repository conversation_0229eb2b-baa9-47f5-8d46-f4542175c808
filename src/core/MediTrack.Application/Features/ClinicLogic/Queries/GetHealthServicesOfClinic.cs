﻿using System.Globalization;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;
using MediTrack.Application.Features.KioskMetadataLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.ClinicLogic.Queries
{
    public class GetHealthServicesOfClinic : IRequest<BaseCommandResultWithData<List<HealthService>>>
    {
        public string ClinicId { get; set; } = string.Empty;
        public string? SubClinicId { get; set; } = string.Empty;
        public string? ExameTypeId { get; set; } = string.Empty;
        public string? ClinicCode { get; set; } = string.Empty;
        public string? HealthInsuranceNo { get; set; } = string.Empty;
        public bool IsIgnoreFilter { get; set; }
        public string? DateOfBirth { get; set; }
        public string? Gender { get; set; }
    }

    public class GetHealthServicesOfClinicHandler(ICachedService cachedService,
            IHospitalMetadataRepository? hospitalMetadataRepository,
            IDatabaseService databaseService,
            ICurrentHospitalService current) : IRequestHandler<GetHealthServicesOfClinic, BaseCommandResultWithData<List<HealthService>>>
    {
        private readonly IHisService hisService = current.HisService;

        public async Task<BaseCommandResultWithData<List<HealthService>>> Handle(
            GetHealthServicesOfClinic request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HealthService>>();

            List<HealthService>? healthServices = await cachedService.GetAsync<List<HealthService>>(
                    "HealthServices" +
                    current.CurrentHospital.HospitalId +
                    request.ExameTypeId +
                    request.ClinicId +
                    request.SubClinicId +
                    request.ClinicCode +
                    // Ở Thành Phố Vinh, giá sẽ phụ thuộc vào mã bảo hiểm y tế của bệnh nhân
                    (current.CurrentHospital.HisVersion == "v14" ? request.HealthInsuranceNo : string.Empty)
                , cancellationToken);

            Log.Information("{LogPrefix} HealthServices from cache: {healthServices}", current.LogPrefix, healthServices);

            if (healthServices == null || healthServices.Count == 0)
            {
                Log.Information("{LogPrefix} HealthServices: get from HIS", current.LogPrefix);

                (bool getResult, string message, _, healthServices) =
                    await hisService.GetHealthServices(new GetHealthServicesDto
                    {
                        ExameTypeId = request.ExameTypeId,
                        ClinicId = request.ClinicId,
                        SubClinicId = request.SubClinicId,
                        ClinicCode = request.ClinicCode,
                        HealthInsuranceNo = request.HealthInsuranceNo,
                        KioskId = current.KioskId,
                        HospitalId = current.CurrentHospital.HospitalId
                    });

                Log.Information("{LogPrefix} HIS GetHealthServices  {getResult} {message} {@healthServices}", current.LogPrefix, getResult, message, healthServices);

                if (healthServices is not null && healthServices.Count > 0)
                {
                    foreach (var hs in healthServices)
                    {
                        if (string.IsNullOrEmpty(hs.ExameTypeId)) hs.ExameTypeId = request.ExameTypeId;
                        if (string.IsNullOrEmpty(hs.ClinicId)) hs.ClinicId = request.ClinicId;
                        if (string.IsNullOrEmpty(hs.ClinicCode)) hs.ClinicCode = request.ClinicCode;
                    }

                    await cachedService.SetAsync(
                        "HealthServices" +
                        current.CurrentHospital.HospitalId +
                        request.ExameTypeId +
                        request.ClinicId +
                        request.SubClinicId +
                        request.ClinicCode +
                        (current.CurrentHospital.HisVersion == "v14" ? request.HealthInsuranceNo : string.Empty)
                    , healthServices, cancellationToken);
                }
                else // If health services not found
                {
                    result.Set(true, ClinicConstant.Ok, []);
                    return result;
                }
            }

            var hospitalMetaDatas = hospitalMetadataRepository != null
            ? await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(current.CurrentHospital.HospitalId, cancellationToken)
            : null;

            var limitMetadatas = hospitalMetaDatas?.Where(x => x.GroupType == $"HEALTH_SERVICE_CONFIG").ToList();
            var examinationHour = hospitalMetaDatas?.FirstOrDefault(x => x.GroupType == "examination_hour_default");

            string hospitalId = current.CurrentHospital.HospitalId;
            string kioskId = current.KioskId;

            //kiosk_configs:exame_types:{exameTypeId} là nối chuỗi của kiosk config  
            // _ exameTypes Id của FE truyền lên  dùng để lấy config exame type tương ứng có trong database
            /// get data kiosk meta data
            var kioskMetaData = await databaseService.KioskMetaDatas.Where(x => x.GroupType == $"kiosk_configs:exame_types:{request.ExameTypeId}:{request.ClinicId}:{request.ClinicCode}"
                                 && x.HospitalId == hospitalId && x.KioskId == kioskId).ToListAsync(cancellationToken);
            if (!request.IsIgnoreFilter && kioskMetaData.Count != 0)
            {
                var dataHealthServiceKioskMetadata = new List<HealthServiceKioskMetadataDto>();
                foreach (var item in kioskMetaData)
                    dataHealthServiceKioskMetadata.Add(JsonConvert.DeserializeObject<HealthServiceKioskMetadataDto>(item.Value ?? string.Empty) ?? new HealthServiceKioskMetadataDto());

                var dataValidation = HospitalHelper.GetHealthServiceRealTimeActive(dataHealthServiceKioskMetadata);
                if (current.CurrentHospital.IsActiveConfigExaminationTime && dataValidation.Count != 0)
                {
                    // Nếu tất cả metadata đều inactive, giữ nguyên danh sách gốc
                    healthServices = [.. healthServices!.Where(x => dataValidation.Contains((x.Id, x.Code)))];
                }
            }

            healthServices = [.. healthServices!.Where(x => x.ClinicId == request.ClinicId && x.ClinicCode == request.ClinicCode)
                .Select(x =>
                {
                    var limitMetadata = limitMetadatas?.FirstOrDefault(c => c.Code == $"{x.Id}_{x.Code}");
                    var limitConfig = limitMetadata != null
                        ? JsonConvert.DeserializeObject<ConfigHospitalHealthServiceDto>(limitMetadata.Value ?? string.Empty)
                        : null;

                    int? maxPatients = (limitConfig?.IsDisabledNumberMedical == true)
                        ? null
                        : limitConfig?.MaxNumberMedical ?? x.TotalPatientCount;

                    if (maxPatients is <= 0)
                        maxPatients = null;

                    x.TotalPatientCount = maxPatients;
                    x.RemainingPatientCount = (maxPatients.HasValue && x.ProcessingNumber.HasValue)
                        ? Math.Max(maxPatients.Value - x.ProcessingNumber.Value, 0)
                        : null;
                    x.ExaminationHour = examinationHour?.Value ?? string.Empty;

                    if (limitConfig == null || request.IsIgnoreFilter)
                    {
                        // Nếu không có cấu hình giới hạn hoặc bỏ qua bộ lọc, thêm vào danh sách
                        return x;
                    }

                    if (!limitConfig.IsDisabledAgeRange)
                    {
                        if (!DateTime.TryParseExact(request.DateOfBirth, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var ngaySinh))
                            return null;

                        if (!DateTimeHelper.IsAgeInRange(ngaySinh, limitConfig.MinAge, limitConfig.MaxAge))
                            return null;
                    }

                    if (!limitConfig.IsDisabledGender)
                    {
                        int gender = request.Gender switch
                        {
                            "Nam" => 1,
                            "Nữ" => 2,
                            _ => 0
                        };
                        if (limitConfig.Gender.HasValue && limitConfig.Gender.Value != gender)
                            return null;
                    }

                    return x;
                })
                .Where(x => x != null)
                .Cast<HealthService>()];

            result.Set(true, ClinicConstant.Ok, healthServices);

            return result;
        }
    }
}

﻿using System.Globalization;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;
using MediTrack.Application.Features.KioskMetadataLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.ClinicLogic.Queries
{
    public class GetHisClinics : IRequest<BaseCommandResultWithData<List<Clinic>>>
    {
        public string? ExameTypeID { get; set; } = string.Empty;
        public bool IsIgnoreFilter { get; set; }
        public string? DateOfBirth { get; set; }
        public string? Gender { get; set; }
    }

    public class GetHisClinicsHandler(
        IHospitalMetadataRepository hospitalMetadataRepository,
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetHisClinics, BaseCommandResultWithData<List<Clinic>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<Clinic>>> Handle(
            GetHisClinics request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<Clinic>>();

            (_, _, _, List<Clinic> clinics) = await hisService.GetClinics(request.ExameTypeID, currentHospitalService.KioskId);
            List<Clinic> filteredClinics = [];
            if (clinics.Count > 0)
            {
                string currentHospitalId = currentHospitalService.CurrentHospital.HospitalId;
                string currentKioskId = currentHospitalService.KioskId;
                var hospitalMetaDatas = await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(currentHospitalId, cancellationToken);
                var limitMetadatas = hospitalMetaDatas?.Where(x => x.GroupType == $"CLINIC_CONFIG").ToList();

                var kioskMetaDatas = await databaseService.KioskMetaDatas.Where(x => x.GroupType!.StartsWith($"kiosk_configs:exame_types:{request.ExameTypeID}:")
                      && x.HospitalId == currentHospitalId && x.KioskId == currentKioskId).ToListAsync(cancellationToken: cancellationToken);

                if (!request.IsIgnoreFilter && currentHospitalService.CurrentHospital.IsActiveConfigExaminationTime)
                {
                    // Sao lưu danh sách gốc
                    var clinicsResult = clinics.ToList();

                    foreach (Clinic item in clinics.ToList())
                    {
                        var kioskMetaData = kioskMetaDatas.Where(x => x.GroupType == $"kiosk_configs:exame_types:{request.ExameTypeID}:{item.Id}:{item.Code}").ToList();
                        if (kioskMetaData.Count == 0)
                        {
                            clinicsResult.Remove(item);
                            continue;
                        }

                        var dataHealthServiceKioskMetadata = new List<HealthServiceKioskMetadataDto>();

                        foreach (var itemKioskMetaData in kioskMetaData)
                            dataHealthServiceKioskMetadata.Add(JsonConvert.DeserializeObject<HealthServiceKioskMetadataDto>(itemKioskMetaData.Value ?? string.Empty) ?? new HealthServiceKioskMetadataDto());

                        var dataValidation = HospitalHelper.GetHealthServiceRealTimeActive(dataHealthServiceKioskMetadata);

                        if (dataValidation.Count == 0)
                        {
                            clinicsResult.Remove(item);
                            continue;
                        }
                    }
                    clinics = clinicsResult;
                }

                // set response ExameTypeID to request ExameTypeID if it null or empty

                foreach (var clinic in clinics)
                {
                    if (string.IsNullOrEmpty(clinic.ExameTypeID))
                    {
                        clinic.ExameTypeID = request.ExameTypeID;
                    }

                    var limitMetadata = limitMetadatas?.Where(c => c.Code == clinic.Id + "_" + clinic.Code).FirstOrDefault();
                    var limitConfig = JsonConvert.DeserializeObject<ConfigHospitalClinicDto>(limitMetadata?.Value ?? string.Empty);

                    int? maxPatients = limitConfig?.IsDisabledNumberMedical == true ? null : limitConfig?.MaxNumberMedical ?? clinic.TotalPatientCount;

                    // Nếu maxPatients nhỏ hơn hoặc bằng 0, đặt nó là null
                    if (maxPatients.HasValue && maxPatients.Value <= 0)
                        maxPatients = null;

                    clinic.TotalPatientCount = maxPatients;
                    clinic.RemainingPatientCount = (maxPatients.HasValue && clinic.ProcessingNumber.HasValue)
                                            ? Math.Max(maxPatients.Value - clinic.ProcessingNumber.Value, 0)
                                            : null;
                    if (limitConfig == null || request.IsIgnoreFilter)
                    {
                        // Nếu không có cấu hình giới hạn hoặc bỏ qua bộ lọc, thêm vào danh sách
                        filteredClinics.Add(clinic);
                        continue;
                    }

                    if (!limitConfig.IsDisabledAgeRange)
                    {
                        bool canConvert = DateTime.TryParseExact(request.DateOfBirth, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime ngaySinh);
                        if (!canConvert)
                            continue;

                        // Kiểm tra tuổi của bệnh nhân có nằm trong khoảng giới hạn không
                        bool age = DateTimeHelper.IsAgeInRange(ngaySinh, limitConfig.MinAge, limitConfig.MaxAge);
                        if (!age)
                            continue;
                    }

                    if (!limitConfig.IsDisabledGender)
                    {
                        int gender = request.Gender switch
                        {
                            "Nam" => 1,
                            "Nữ" => 2,
                            _ => 0
                        };

                        if (limitConfig.Gender.HasValue && limitConfig.Gender.Value != gender)
                            continue;
                    }

                    filteredClinics.Add(clinic);
                }
            }

            result.Set(true, ClinicConstant.Ok, filteredClinics);

            return result;
        }
    }
}
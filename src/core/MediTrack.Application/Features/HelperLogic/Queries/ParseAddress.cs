﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerLogic.Helpers;
using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.HelperLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.HelperLogic.Queries
{
    public class ParseAddress : IRequest<BaseCommandResultWithData<ParseAddressResponse>>
    {
        public string FullAddress { get; set; } = string.Empty;
    }

    public class ParseAddressHandler(IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository) : IRequestHandler<ParseAddress, BaseCommandResultWithData<ParseAddressResponse>>
    {
        public async Task<BaseCommandResultWithData<ParseAddressResponse>> Handle(ParseAddress request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<ParseAddressResponse>();

            //2. Prepare data
            var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);

            var districts = await districtRepository.GetDistrictByParentIdAsync(null, cancellationToken);

            var wards = await wardRepository.GetWardByParentIdAsync(null, false, cancellationToken);

            (ProvinceDto? provinceDto, DistrictDto? districtDto, WardDto? wardDto, string? streetAddress, _) =
                    ApplicationCustomerHelper.GetLocationDto(request.FullAddress, provinces, districts, wards);

            result.Set(true, ProvinceConstant.Ok, new ParseAddressResponse
            {
                ProvinceId = provinceDto?.Id,
                DistrictId = districtDto?.Id,
                WardId = wardDto?.Id,
                Street = streetAddress,
                Ward = wardDto?.Name,
                District = districtDto?.Name,
                Province = provinceDto?.Name,
                NewProvinceId = provinceDto?.NewId,
                NewProvince = provinceDto?.NewName,
                NewWardId = wardDto?.NewId,
                NewWard = wardDto?.NewName
            });

            return result;
        }
    }
}

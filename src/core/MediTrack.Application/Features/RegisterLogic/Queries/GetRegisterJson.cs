﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PaymentLogic.Queries;
using MediTrack.Application.Features.ReceiptLogic.Queries;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.RegisterLogic.Queries
{
    public class GetRegisterJson : IRequest<BaseCommandResultWithData<string>>
    {
        public string Number { get; set; } = string.Empty;
    }

    public class GetRegisterJsonHandler(IDatabaseService databaseService,
        IMediator mediator)
       : IRequestHandler<GetRegisterJson, BaseCommandResultWithData<string>>
    {
        public async Task<BaseCommandResultWithData<string>> Handle(GetRegisterJson request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<string>();

            //1. Lấy thông tin phiếu thu từ database
            var register = await databaseService.Registers.FindAsync([request.Number], cancellationToken);

            if (register is null)
            {
                result.Set(false, "Không tìm thấy thông tin phiếu đăng ký");
                return result;
            }

            var receipt = await databaseService.Receipts.FirstOrDefaultAsync(x => x.RegisterNumber == register.Number, cancellationToken);
            if (receipt is null)
            {
                result.Set(false, "Không tìm thấy thông tin phiếu thu");
                return result;
            }

            if (register.PaymentType == "free" || register.PaymentType == "qr")
            {
                var payment = await databaseService.Payments.FirstOrDefaultAsync(x => x.ReceiptNumber == receipt.Number, cancellationToken);
                if (payment is null)
                {
                    result.Set(false, "Không tìm thấy thông tin thanh toán");
                    return result;
                }
                return await mediator.Send(new GetPaymentJson() { Number = payment.Id }, cancellationToken);
            }
            
            return await mediator.Send(new GetReceiptJson() { Number = receipt.Number }, cancellationToken);
        }
    }
}
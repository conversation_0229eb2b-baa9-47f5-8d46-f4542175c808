﻿using System.Text.Json.Serialization;
using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.RegisterLogic.Commands
{
    public class CreateRegisterFormFromBooking : PatientBookingDto, IRequest<BaseCommandResultWithData<CreateRegisterFormFromBookingResponseDto>>
    {
        [JsonIgnore]
        public string Number { get; set; } = string.Empty;
    }

    public class CreateRegisterFormFromBookingHandler(IMediator mediator,
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<CreateRegisterFormFromBooking, BaseCommandResultWithData<CreateRegisterFormFromBookingResponseDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<CreateRegisterFormFromBookingResponseDto>> Handle(
            CreateRegisterFormFromBooking req, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateRegisterFormFromBookingResponseDto>();
            Log.Information("{LogPrefix} Request: {@Request}", currentHospitalService.LogPrefix, req);
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                // 1. Get and validate booking
                var booking = await databaseService.PatientBookings.FindAsync([req.Number], cancellationToken);
                if (booking == null)
                {
                    result.Set(false, "Không tìm thấy phiếu tiếp nhận", ErrorTypeEnum.MediPayError);
                    Log.Error("{LogPrefix} Request: {@Request}", currentHospitalService.LogPrefix, req);
                    return result;
                }

                req.Adapt(booking);

                // 2. Validate appointment time
                if (DateTime.UtcNow.Date != booking.AppointmentDate?.Date
                    // || !(booking.AppointmentTimeZone?.Split(" - ") is { Length: 2 } parts)
                    // || !TimeSpan.TryParse(parts[0], out var startTime)
                    // || !TimeSpan.TryParse(parts[1], out var endTime)
                    // || DateTime.UtcNow.AddHours(7).TimeOfDay < startTime
                    // || DateTime.UtcNow.AddHours(7).TimeOfDay > endTime
                    )
                {
                    result.Set(false, "Thời gian hẹn không hợp lệ", ErrorTypeEnum.MediPayError);
                    return result;
                }

                CustomerHealthInsurance? insurance = null;
                if (booking.IsInsurance)
                {
                    var customer = await databaseService.Customers
                        .FirstOrDefaultAsync(x => x.Id == booking.CustomerKey || x.IdentityNo == booking.CustomerKey, cancellationToken);
                    Log.Information("{LogPrefix} Customer Id: {Id}", currentHospitalService.LogPrefix, customer?.Id);

                    if (customer is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomer, ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    
                    // check thẻ bảo hiểm còn hiệu lực hay không
                    (bool getResult, string message, errorType, insurance) = await hisService.GetCustomerHealthInsurance(customer);
                }

                // 3. Convert booking to register form request
                var registerForm = new CreateRegisterForm
                {
                    CustomerKey = booking.CustomerId ?? string.Empty,
                    CustomerRelationKey = booking.CustomerRelationKey,
                    HealthServiceId = booking.HealthServiceId,
                    ServiceCode = booking.ServiceCode,
                    ExameTypeId = booking.ExameTypeId,
                    MaPhong = booking.MaPhong,
                    ClinicId = booking.ClinicId,
                    ClinicCode = booking.ClinicCode,
                    SubClinicId = booking.SubClinicId,
                    ClinicGroupId = booking.ClinicGroupId,
                    ClinicGroupCode = booking.ClinicGroupCode,
                    HealthcareServiceTierId = booking.HealthcareServiceTierId,
                    HealthcareServiceTypeId = booking.HealthcareServiceTypeId,
                    MedicalTreatmentCategoryId = booking.MedicalTreatmentCategoryId,
                    MedicalTreatmentCategoryHisId = booking.MedicalTreatmentCategoryHisId,
                    ReasonForVisit = booking.ReasonForVisit,
                    IsInsurance = booking.IsInsurance,
                    CareerId = booking.CareerId,
                    SocialCareerId = booking.SocialCareerId,
                    EducationLevel = booking.EducationLevel,
                    WorkPlace = booking.WorkPlace,
                    WorkAddress = booking.WorkAddress,
                    ProvinceId = booking.ProvinceId,
                    WardId = booking.WardId,
                    DistrictId = booking.DistrictId,
                    Street = booking.Street,
                    RefRegisterNumber = booking.RefRegisterNumber,
                    ClinicName = booking.ClinicName,
                    Insurance = insurance,
                    TransferReferralDocumentNumber = booking.TransferReferralDocumentNumber,
                    TransferReferralDiseaseCode = booking.TransferReferralDiseaseCode,
                    TransferReferralDiseaseCodeAndName = booking.TransferReferralDiseaseCodeAndName,
                    TransferReferralUnit = booking.TransferReferralUnit,
                    TransferReferralType = booking.TransferReferralType,
                    TransferReferralReason = booking.TransferReferralReason,
                    TransferReferralDate = booking.TransferReferralDate,
                    TransferReferralDiagnosisInfo = booking.TransferReferralDiagnosisInfo,
                    BloodPressure = booking.BloodPressure,
                    HeartRate = booking.HeartRate,
                    RespiratoryRate = booking.RespiratoryRate,
                    BloodOxygen = booking.BloodOxygen,
                    Height = booking.Height,
                    Weight = booking.Weight,
                    PulseRate = booking.PulseRate,
                    Temperature = booking.Temperature,
                    PaymentType = booking.PaymentType,
                    IsOnARVTreatment = booking.IsOnARVTreatment,
                    PatientId = booking.PatientId,
                    PatientCode = booking.PatientCode,
                    UuTien = booking.UuTien,
                    AdvancePayment = booking.AdvancePayment,
                    RegisterType = "DAT_LICH",
                    PatientBookingNumber = booking.Number
                };

                // 4. Call CreateRegisterForm handler
                var registerRes = await mediator.Send(registerForm, cancellationToken);
                result.Set(registerRes.Success, registerRes.Messages, new CreateRegisterFormFromBookingResponseDto()
                {
                    Number = registerRes.Data?.Number ?? string.Empty,
                    RefNo = registerRes.Data?.RefNo ?? string.Empty,
                    HealthInsurance = registerRes.Data?.HealthInsurance ?? 0,
                    UnitPrice = registerRes.Data?.UnitPrice ?? 0
                }, errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Có lỗi xảy ra khi tạo phiếu tiếp nhận", currentHospitalService.LogPrefix);
                result.Set(false, RegisterConstant.CreateError, ErrorTypeEnum.MediPayError);
            }
            return result;
        }
    }
}
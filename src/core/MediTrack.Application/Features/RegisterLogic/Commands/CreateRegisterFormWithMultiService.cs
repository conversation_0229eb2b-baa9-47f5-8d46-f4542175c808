using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.ClinicLogic.Queries;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.ExameTypeLogic.Queries;
using MediTrack.Application.Features.RegisterLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using PaymentClient.Lib.Response;
using Serilog;
using System.Text.RegularExpressions;

namespace MediTrack.Application.Features.RegisterLogic.Commands
{
    public class CreateRegisterFormWithMultiService : RegisterFormWithMultiServiceDto, IRequest<BaseCommandResultWithData<CreateRegisterFormWithMultiServiceResponseDto>>
    {
        public string? MembershipId { get; set; }
        public int UuTien { get; set; }
        public bool IsInsurance { get; set; }
        public CustomerHealthInsurance? Insurance { get; set; }
        public string? RefRegisterNumber { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }

        /// <summary>
        /// Tuyến khám bệnh: Đúng tuyến, trái tuyến, Đa tuyến đúng tuyến
        /// </summary>
        public string? HealthcareServiceTierId { get; set; }

        /// <summary>
        /// Loại hình khám bệnh: Khám bệnh, ngoại trú, Nội trú....
        /// </summary>
        public string? HealthcareServiceTypeId { get; set; }

        public string? MedicalTreatmentCategoryId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh HIS: Bảo hiểm, tự nguyện, viện phí,..
        /// </summary>
        public string? MedicalTreatmentCategoryHisId { get; set; }

        /// <summary>
        /// Lý do khám bệnh
        /// </summary>
        public string? ReasonForVisit { get; set; }

        public string? EducationLevel { get; set; }
        public string? WorkPlace { get; set; }
        public string? WorkAddress { get; set; }

        /// <summary>
        /// Số giấy chuyển tuyến
        /// </summary>
        public string? TransferReferralDocumentNumber { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến
        /// </summary>
        public string? TransferReferralDiseaseCode { get; set; }

        /// <summary>
        /// Mã bệnh chuyển tuyến (Kèm tên bệnh)
        /// </summary>
        public string? TransferReferralDiseaseCodeAndName { get; set; }

        /// <summary>
        /// Đơn vị chuyển tuyến
        /// </summary>
        public string? TransferReferralUnit { get; set; }

        /// <summary>
        /// Hình thức chuyển tuyến
        /// </summary>
        public string? TransferReferralType { get; set; }

        /// <summary>
        /// Lý do chuyển tuyến
        /// </summary>
        public string? TransferReferralReason { get; set; }

        /// <summary>
        /// Ngày chuyển tuyến
        /// </summary>
        public string? TransferReferralDate { get; set; }

        /// <summary>
        /// Thông tin chuẩn đoán tuyến dưới
        /// </summary>
        public string? TransferReferralDiagnosisInfo { get; set; }
        /// <summary>
        /// Tổng giá tiền FE tính được
        /// </summary>
        public long? TotalPrice { get; set; }

        public string? ProvinceId { get; set; }
        public string? WardId { get; set; }
        public string? DistrictId { get; set; }
        public string? Street { get; set; }

        //Chỉ số sinh tồn
        public string? BloodPressure { get; set; } = string.Empty;

        public int? HeartRate { get; set; }
        public int? RespiratoryRate { get; set; }
        public double? BloodOxygen { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public int? PulseRate { get; set; }
        public double? Temperature { get; set; }

        //Loại hình thanh toán: Tiền mặt (cash), thẻ (card), chuyển khoản (qr), miễn phí (free)...truy
        public string? PaymentType { get; set; }

        public string? PatientId { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;
        public string PatientNameRef { get; set; } = string.Empty;
        public string PatientDateOfBirthRef { get; set; } = string.Empty;
        public string PatientPhoneRef { get; set; } = string.Empty;
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class CreateRegisterFormWithMultiServiceHandler(ICurrentUserService currentUserService,
        IMediator mediator,
        IDatabaseService databaseService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IOptions<PaymentConfig> paymentConfig,
        ICurrentHospitalService currentHospitalService,
        IHisServiceHelper hisServiceHelper,
        IHttpClientFactory httpClientFactory) : IRequestHandler<CreateRegisterFormWithMultiService, BaseCommandResultWithData<CreateRegisterFormWithMultiServiceResponseDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseCommandResultWithData<CreateRegisterFormWithMultiServiceResponseDto>> Handle(
            CreateRegisterFormWithMultiService request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateRegisterFormWithMultiServiceResponseDto>();
            Log.Information("{LogPrefix} Request: {@Request}", currentHospitalService.LogPrefix, request);
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;

            try
            {
                // Check services tồn tại
                if (request.Service.Count == 0)
                {
                    result.Set(false, "Danh sách dịch vụ không được để trống", ErrorTypeEnum.MediPayError);
                    return result;
                }

                var customer = await databaseService.Customers.FindAsync([request.CustomerKey], cancellationToken);
                Log.Information("{LogPrefix} Customer Id: {Id}", currentHospitalService.LogPrefix, customer?.Id);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.InvalidCustomer, ErrorTypeEnum.MediPayError);
                    return result;
                }

                var customerRelationship = new Customer();
                string customerRelationshipName = "";
                string customerRelationshipId = "";
                if (!string.IsNullOrEmpty(request.CustomerRelationKey))
                {
                    customerRelationship = await databaseService.Customers.FindAsync([request.CustomerRelationKey], cancellationToken);
                    if (customerRelationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelation, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var customerRelation = await databaseService.CustomerRelationShips
                        .Include(a => a.Relationship)
                        .FirstOrDefaultAsync(a => a.CustomerId.Equals(request.CustomerRelationKey) && a.CustomerIdRefer.Equals(request.CustomerKey), cancellationToken);

                    if (customerRelation is null || customerRelation.Relationship is null)
                    {
                        result.Set(false, CustomerConstant.InvalidCustomerRelationship, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    customerRelationshipName = customerRelation.Relationship.Name;
                    customerRelationshipId = customerRelation.Relationship.Id;
                }

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customer.CareerId != request.CareerId)
                {
                    customer.CareerId = request.CareerId ?? string.Empty;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                // Cập nhật thông tin bảo hiểm y tế
                if (!string.IsNullOrEmpty(request.Insurance?.InsuranceNo)
                    && (customer.HealthInsuranceNo != request.Insurance?.InsuranceNo
                    || customer.HealthInsurancePlaceId != request.Insurance?.RegisterPlaceID))
                {
                    customer.HealthInsuranceNo = request.Insurance?.InsuranceNo;
                    customer.HealthInsurancePlaceId = request.Insurance?.RegisterPlaceID;
                    customer.HealthInsurancePlace = request.Insurance?.RegisterPlaceName;
                    customer.HealthInsuranceFromDate = request.Insurance?.FromDate;
                    customer.HealthInsuranceExpiredDate = request.Insurance?.ExpiredDate;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.EducationLevel)
                    && customer.EducationLevel != request.EducationLevel)
                {
                    customer.EducationLevel = request.EducationLevel;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkPlace)
                    && customer.WorkPlace != request.WorkPlace)
                {
                    customer.WorkPlace = request.WorkPlace;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if (!string.IsNullOrEmpty(request.WorkAddress)
                    && customer.WorkAddress != request.WorkAddress)
                {
                    customer.WorkAddress = request.WorkAddress;
                    customer.SetUpdate(currentUserService.UserName);
                    databaseService.Customers.Update(customer);
                }

                if ((!string.IsNullOrEmpty(request.ProvinceId) && customer.ProvinceId != request.ProvinceId) ||
                    (!string.IsNullOrEmpty(request.WardId) && customer.WardId != request.WardId) ||
                    (!string.IsNullOrEmpty(request.DistrictId) && customer.DistrictId != request.DistrictId) ||
                    (!string.IsNullOrEmpty(request.Street) && customer.Street != request.Street) ||
                    (!customer.IsSyncedTwoLevelAddress && currentHospitalService.CurrentHospital.IsTwoLevelAddress))
                {
                    var isRequestTwoLevelAddress = request.IsTwoLevelAddress.GetValueOrDefault();

                    var ward = await wardRepository.GetWardByIdAsync(request.WardId!, isRequestTwoLevelAddress, cancellationToken);

                    string? districtName = null;
                    if (!isRequestTwoLevelAddress)
                    {
                        districtName = (await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId, cancellationToken)).FirstOrDefault(p => p.Id == request.DistrictId)?.Name;
                    }

                    var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);
                    var province = provinces.FirstOrDefault(p => (isRequestTwoLevelAddress ? p.NewId : p.Id) == request.ProvinceId);

                    if (ward != null && province != null && (isRequestTwoLevelAddress || districtName != null))
                    {
                        customer.IsSyncedTwoLevelAddress = isRequestTwoLevelAddress;

                        if (isRequestTwoLevelAddress)
                        {
                            customer.ProvinceId = province?.NewId;
                            customer.DistrictId = string.Empty;
                            customer.WardId = ward?.NewId;
                            customer.Address = $"{customer.Street}, {ward?.NewName}, {province?.NewName}";
                        }
                        else
                        {
                            customer.ProvinceId = request.ProvinceId;
                            customer.DistrictId = request.DistrictId;
                            customer.WardId = request.WardId;
                            customer.Address = $"{customer.Street}, {ward?.Name}, {districtName}, {province?.Name}";
                        }

                        customer.Street = request.Street;

                        customer.SetUpdate(currentUserService.UserName);
                        databaseService.Customers.Update(customer);
                    }
                }       

                var customerHospital = await databaseService.CustomerHospitals
                    .FindAsync([customer.Id, currentHospitalService.CurrentHospital.HospitalId], cancellationToken);

                var isNotFoundCustomerHospital = customerHospital == null;
                if (isNotFoundCustomerHospital)
                {
                    customerHospital = new CustomerHospital { HospitalId = currentHospitalService.CurrentHospital.HospitalId, CustomerId = customer.Id };
                    customerHospital.SetCreate(currentUserService.UserName);
                }
                else
                {
                    customerHospital!.SetUpdate(currentUserService.UserName);
                }

                customerHospital.IsFavourite = true;

                // Nếu có cập nhật thông tin nghề nghiệp
                if (customerHospital.CareerId != request.SocialCareerId)
                {
                    customerHospital.CareerId = request.SocialCareerId ?? string.Empty;
                }

                bool getResult = false;
                string message = string.Empty;

                // 0. Nếu khách hàng chưa có mã bệnh nhân thì gọi sang HIS tích hợp mã bệnh nhân
                if (string.IsNullOrEmpty(customerHospital.PatientCode))
                {
                    if (!string.IsNullOrEmpty(request.PatientId) && !string.IsNullOrEmpty(request.PatientCode))
                    {
                        customerHospital.PatientId = request.PatientId;
                        customerHospital.PatientCode = request.PatientCode;
                    }
                    else
                    {
                        (getResult, message, errorType, HisCustomerDto hisCustomerDto) = await hisService.GetCustomerHis(customer);
                        Log.Information("{LogPrefix} GetPationCode from HIS: {getResult} - - - message: {message} - - - patientCode: {patientCode} ", currentHospitalService.LogPrefix, getResult, message, hisCustomerDto.PatientCode);
                        if (getResult && !string.IsNullOrEmpty(hisCustomerDto.PatientCode))
                        {
                            //1. Save patient code
                            customerHospital.PatientId = hisCustomerDto.PatientId;
                            customerHospital.PatientCode = hisCustomerDto.PatientCode;
                        }
                    }
                }

                // get exame type
                var exameTypes = await mediator.Send(new GetExameTypes(), cancellationToken);
                if (!exameTypes.Success)
                {
                    result.Set(false, $"Không thể lấy danh sách loại khám", errorType);
                    Log.Warning("{LogPrefix} Không thể lấy danh sách loại khám", currentHospitalService.LogPrefix);
                    return result;
                }

                // 1. Tạo và lưu phiếu tiếp nhận
                List<HealthService>? healthServices = [];
                Register register = new();
                List<RegisterDetail> registerDetails = [];
                decimal unitPriceTotal = 0;

                // Thứ tự áp dụng giảm giá:
                // 1. PackageService discount áp dụng cho từng dịch vụ riêng lẻ (trong vòng lặp service)
                //    - PackageType = 0: Miễn phí dịch vụ nếu còn lượt dùng
                //    - PackageType = 1: Giảm giá từng dịch vụ theo cấu hình
                // 2. PackageService UsageCount trừ lượt sử dụng (chỉ áp dụng cho PackageType = 0)
                // 3. Membership discount áp dụng cho tổng tiền (sau khi đã áp dụng PackageService)

                // Lấy thông tin PackageService từ Membership nếu có
                PackageService? packageService = null;
                List<HealthPackageDetail>? packageDetails = null;
                if (!string.IsNullOrEmpty(request.MembershipId))
                {
                    var membership = await databaseService.Memberships
                        .FindAsync([request.MembershipId], cancellationToken);
                    if (membership != null && !string.IsNullOrEmpty(membership.PackageServiceId))
                    {
                        packageService = await databaseService.PackageServices
                            .Include(p => p.HealthPackageDetails)
                            .ThenInclude(hpd => hpd.HealthService)
                            .FirstOrDefaultAsync(p => p.Id == membership.PackageServiceId && p.HospitalId == currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
                        packageDetails = packageService?.HealthPackageDetails?.ToList() ?? [];
                        // Kiểm tra tính hợp lệ của PackageService
                        if (packageService != null && packageService.PackageType == 0)
                        {
                            if (!packageService.UsageCount.HasValue || packageService.UsageCount <= 0)
                            {
                                result.Set(false, "Gói dịch vụ đã hết lượt sử dụng", ErrorTypeEnum.MediPayError);
                                Log.Warning("{LogPrefix} PackageService '{PackageServiceId}' has no remaining usage count", currentHospitalService.LogPrefix, membership.PackageServiceId);
                                return result;
                            }
                        }
                        Log.Information("{LogPrefix} Found package service: {PackageServiceName} with {DetailCount} services", 
                            currentHospitalService.LogPrefix, packageService?.Name ?? string.Empty, packageDetails?.Count ?? 0);
                    }
                }

                //set register number
                register.Number = IdentityHelper.Guid(15);

                foreach (var service in request.Service)
                {
                    //get health services mediator
                    var getHealthServices = await mediator.Send(new GetHealthServicesOfClinic
                    {
                        ExameTypeId = service.ExameTypeId,
                        ClinicId = service.ClinicId ?? string.Empty,
                        ClinicCode = service.ClinicCode,
                        SubClinicId = null,
                        HealthInsuranceNo = request.Insurance?.InsuranceNo,
                        IsIgnoreFilter = true
                    }, cancellationToken);

                    Log.Information("{LogPrefix} Get healthServices getResult: {getResult} - - - message: {message} - - - HealthServices: {@HealthServices} ",
                        currentHospitalService.LogPrefix, getHealthServices.Success, getHealthServices.Messages, getHealthServices.Data);
                    if (getHealthServices.Data == null || getHealthServices.Data.Count == 0)
                    {
                        result.Set(false, "Không thể lấy danh sách dịch vụ khám", errorType);
                        Log.Warning("{LogPrefix} Không thể lấy danh sách dịch vụ khám");
                        return result;
                    }

                    var healthService = getHealthServices.Data.FirstOrDefault(x => x.Id == service.HealthServiceId
                        && (string.IsNullOrEmpty(service.ServiceCode) || x.Code == service.ServiceCode)
                        && (string.IsNullOrEmpty(service.ExameTypeId) || x.ExameTypeId == service.ExameTypeId));
                    if (healthService is null)
                    {
                        result.Set(false, $"Không tìm thấy dịch vụ '{service.HealthServiceId}'", ErrorTypeEnum.MediPayError);
                        Log.Warning("{LogPrefix} Không tìm thấy dịch vụ '{HealthServiceId}' - Danh sách: {@HealthServices}", currentHospitalService.LogPrefix, service.HealthServiceId, getHealthServices.Data);
                        return result;
                    }

                    if (string.IsNullOrWhiteSpace(service.ClinicId))
                    {
                        result.Set(false, "Nhóm chuyên khoa không được để trống", ErrorTypeEnum.MediPayError);
                        Log.Warning("{LogPrefix} Nhóm chuyên khoa không được để trống", currentHospitalService.LogPrefix);
                        return result;
                    }

                    // get clinic
                    (getResult, message, errorType, List<Clinic>? clinics) = await hisService.GetClinics(service.ExameTypeId, currentHospitalService.KioskId);

                    var clinic = clinics?.Find(x => x.Id == healthService?.ClinicId && x.Code == healthService.ClinicCode);
                    var subClinic = new Clinic();
                    if (clinic is null)
                    {
                        Log.Warning("{LogPrefix} Không tìm thấy nhóm chuyên khoa '{ClinicId}' - '{ClinicCode}' - Danh sách: {@Clinics}", currentHospitalService.LogPrefix, healthService?.ClinicId, healthService?.ClinicCode, clinics);
                    }

                    var exameType = exameTypes.Data?.FirstOrDefault(x => x.Id == service.ExameTypeId
                        && (!string.IsNullOrEmpty(x.Id) || x.IsInsurance == request.IsInsurance));
                    if (exameType is null)
                    {
                        Log.Warning("{LogPrefix} Không tìm thấy loại khám '{ExameTypeId}' - Danh sách: {@ExameTypes}", currentHospitalService.LogPrefix, service.ExameTypeId, exameTypes.Data);
                    }

                    //Luồng tạm ứng
                    healthService!.UnitPrice ??= request.AdvancePayment;

                    decimal unitPrice = healthService.UnitPrice ?? 0;
                    
                    if (request.IsInsurance) // Nếu là bảo hiểm
                    {
                        // Nếu bệnh viện không tính tiền bảo hiểm
                        if (currentHospitalService.CurrentHospital.IsIgnoreInsurancePayment)
                        {
                            unitPrice = 0;
                        }
                        // Nếu bệnh viện tính tiền bảo hiểm dựa trên giá thu thêm
                        else if (healthService.ExtraPrice.HasValue && healthService.ExtraPrice > 0)
                        {
                            unitPrice = healthService.ExtraPrice ?? 0;
                        }
                        else if (healthService.InsurancePrice.HasValue)
                        {
                            // Nếu không có giá thì lấy giá mặc định
                            unitPrice = healthService.InsurancePrice ?? 0;
                        }
                    }

                    // Áp dụng discount từ PackageService nếu có
                    if (packageService != null && packageDetails != null)
                    {
                        var packageDetail = packageDetails.FirstOrDefault(pd => pd.HealthServiceId == healthService.Id);
                        if (packageDetail != null)
                        {
                            var originalPrice = unitPrice;

                            // Xử lý theo loại PackageService
                            if (packageService.PackageType == 0) // Loại 1: Quản lý theo số lượt dùng
                            {
                                if (packageService.UsageCount.HasValue && packageService.UsageCount > 0)
                                {
                                    unitPrice = 0; // Miễn phí nếu còn lượt dùng
                                    Log.Information("{LogPrefix} Applied package usage discount for service {ServiceId}: Original={OriginalPrice:N0}, Final={FinalPrice:N0} (Free usage from package)", 
                                        currentHospitalService.LogPrefix, healthService.Id, originalPrice, unitPrice);
                                }
                                else
                                {
                                    Log.Information("{LogPrefix} Package usage count exhausted for service {ServiceId}, using original price {OriginalPrice:N0}", 
                                        currentHospitalService.LogPrefix, healthService.Id, originalPrice);
                                }
                            }
                            else if (packageService.PackageType == 1) // Loại 2: Quản lý theo giảm giá/trừ tiền
                            {
                                decimal discountAmount = 0;
                                // Tính discount dựa trên DiscountUnit của package detail
                                if (!string.IsNullOrEmpty(packageDetail.DiscountUnit))
                                {
                                    switch (packageDetail.DiscountUnit.ToLower())
                                    {
                                        case "%":
                                        case "percent":
                                            discountAmount = unitPrice * (packageDetail.DiscountValue / 100);
                                            break;
                                        case "money":
                                        case "vnd":
                                            discountAmount = packageDetail.DiscountValue;
                                            break;
                                    }
                                }
                                unitPrice = Math.Max(unitPrice - discountAmount, 0);

                                Log.Information("{LogPrefix} Applied package discount for service {ServiceId}: Original={OriginalPrice:N0}, Discount={DiscountAmount:N0} ({DiscountValue}{DiscountUnit}), Final={FinalPrice:N0}", 
                                    currentHospitalService.LogPrefix, healthService.Id, originalPrice, discountAmount, 
                                    packageDetail.DiscountValue, packageDetail.DiscountUnit, unitPrice);
                            }
                        }
                        else
                        {
                            Log.Information("{LogPrefix} Service {ServiceId} not found in package {PackageId}, using original price", 
                                currentHospitalService.LogPrefix, healthService.Id, packageService.Id);
                        }
                    }                    

                    unitPriceTotal += unitPrice;
                    healthServices.Add(healthService);

                    var registerDetail = new RegisterDetail
                    {
                        Id = IdentityHelper.Guid(15),
                        RegisterNumber = register.Number,
                        HealthServiceId = healthService.Id,
                        ServiceCode = healthService.Code,
                        ServiceName = healthService.Name,
                        UnitPrice = unitPrice,
                        Quantity = 1,
                        UnitPriceOfCurrency = unitPrice,
                        CustomerName = $"{customer.FirstName} {customer.LastName}".Trim(),
                        CustomerSex = customer.Sex ?? string.Empty,
                        DateOfBirth = customer.DateOfBirth,
                        IdentityNo = customer.IdentityNo ?? string.Empty,
                        CustomerAddress = customer.Address ?? string.Empty,
                        CustomerPhone = customer.Phone ?? string.Empty,
                        ClinicId = healthService.ClinicId ?? string.Empty,
                        ClinicCode = healthService.ClinicCode ?? string.Empty,
                        Clinic = clinic?.Name ?? string.Empty,
                        ExameTypeId = healthService.ExameTypeId ?? string.Empty,
                        ExameType = exameType?.Name ?? string.Empty,
                    };
                    registerDetail.SetCreate(currentUserService.UserName);
                    registerDetails.Add(registerDetail);
                }

                // Áp dụng discount từ PackageService trước (có thể áp dụng đồng thời với Membership)
                if (packageService != null)
                {
                    // Xử lý theo loại PackageService
                    if (packageService.PackageType == 0) // Loại 1: Quản lý theo số lượt dùng
                    {
                        // Kiểm tra xem còn lượt dùng không
                        if (packageService.UsageCount.HasValue && packageService.UsageCount > 0)
                        {
                            // Trừ lượt sử dụng
                            packageService.UsageCount--;
                            packageService.SetUpdate(currentUserService.UserName);
                            databaseService.PackageServices.Update(packageService);

                            Log.Information("{LogPrefix} Used package service usage count: PackageId={PackageId}, RemainingCount={RemainingCount}", 
                                currentHospitalService.LogPrefix, packageService.Id, packageService.UsageCount);
                        }
                        else
                        {
                            Log.Warning("{LogPrefix} Package service usage count exhausted: PackageId={PackageId}", 
                                currentHospitalService.LogPrefix, packageService.Id);
                        }
                    }
                    // PackageType = 1 (discount) chỉ áp dụng cho từng dịch vụ riêng lẻ, không áp dụng cho tổng tiền
                    // Logic discount đã được xử lý trong vòng lặp service ở trên
                }

                // Áp dụng Membership discount sau PackageService
                MembershipHistory? membershipHistory = null;
                if (!string.IsNullOrEmpty(request.MembershipId))
                {
                    var membership = await databaseService.Memberships
                        .FindAsync([request.MembershipId], cancellationToken);

                    if (membership == null || membership.CustomerId != customer.Id)
                    {
                        result.Set(false, "Không tìm thấy thông tin thẻ thành viên", ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    if (membership.ExpirationDate.HasValue && membership.ExpirationDate < DateTime.UtcNow)
                    {
                        result.Set(false, "Thẻ thành viên đã hết hạn", ErrorTypeEnum.MediPayError);
                        return result;
                    }
                    var priceBeforeMembership = unitPriceTotal;
                    var oldBalance = membership.Balance;
                    var oldUsageCount = membership.UsageCount;


                    // Tính discount trước khi trừ balance
                    decimal discountAmount = 0;
                    switch (membership.DiscountUnit)
                    {
                        case MembershipConstant.DiscountTypePercent:
                            discountAmount = unitPriceTotal * (membership.DiscountValue / 100);
                            break;
                        case MembershipConstant.DiscountTypeMoney:
                            discountAmount = membership.DiscountValue;
                            break;
                    }

                    // Áp dụng discount vào unitPriceTotal trước khi trừ balance
                    unitPriceTotal = Math.Floor(Math.Max(unitPriceTotal - discountAmount, 0));

                    // Trừ balance/usage count theo loại membership (dựa trên giá đã discount)
                    switch (membership.Type)
                    {
                        case MembershipTypeEnum.ByUsageCount:
                            if (membership.UsageCount.HasValue && membership.UsageCount <= 0)
                            {
                                result.Set(false, $"Số lượt sử dụng thẻ thành viên không đủ. Số lượt còn lại: {membership.UsageCount}", ErrorTypeEnum.MediPayError);
                                return result;
                            }
                            membership.UsageCount--;
                            break;
                        case MembershipTypeEnum.ByBalance:
                            if (membership.Balance - membership.MinimumBalance < unitPriceTotal)
                            {
                                result.Set(false, $"Số dư thẻ thành viên không đủ. Số dư: {membership.Balance:N0}, Số tiền cần thanh toán: {unitPriceTotal:N0}", ErrorTypeEnum.MediPayError);
                                return result;
                            }
                            membership.Balance -= unitPriceTotal; // Trừ số tiền sau discount
                            break;
                    }

                    membership.SetUpdate(currentUserService.UserName);
                    databaseService.Memberships.Update(membership);

                    // Add MembershipHistory for service usage
                    membershipHistory = new MembershipHistory
                    {
                        Id = IdentityHelper.Guid(15),
                        MembershipId = membership.Id,
                        MembershipCode = membership.MemberCode,
                        OldBalance = oldBalance,
                        NewBalance = membership.Balance,
                        OldUsageCount = oldUsageCount,
                        NewUsageCount = membership.UsageCount,
                        Description = $"Sử dụng thẻ thành viên thanh toán dịch vụ. Số tiền: {(priceBeforeMembership - unitPriceTotal):N0}. Đăng ký: {register.Number}",
                        TransactionType = "USED",
                        RegisterNumber = register.Number,
                        ReceiptNumber = null,
                        RefNo = null,
                        CustomerId = customer.Id,
                        HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                        CreatedBy = currentUserService.UserName,
                        CreatedAt = DateTime.UtcNow
                    };
                    membershipHistory.SetCreate(currentUserService.UserName);

                    Log.Information("{LogPrefix} Updated membership {Id} - Type: {Type}, New Balance/Usage: {Value}",
                        currentHospitalService.LogPrefix, membership.Id, membership.Type,
                        membership.Type == MembershipTypeEnum.ByUsageCount ? membership.UsageCount.ToString() : membership.Balance.ToString("N0"));

                    Log.Information("{LogPrefix} Added membership history {HistoryId} for register {RegisterNumber} - Type: {TransactionType}, Amount Used: {AmountUsed:N0}",
                        currentHospitalService.LogPrefix, membershipHistory.Id, register.Number, membershipHistory.TransactionType, (priceBeforeMembership - unitPriceTotal));

                    Log.Information("{LogPrefix} Applied membership discount after PackageService: BeforeMembership={BeforeMembership:N0}, MembershipDiscount={DiscountAmount:N0}, Final={Final:N0}",
                        currentHospitalService.LogPrefix, priceBeforeMembership, discountAmount, unitPriceTotal);

                    // Log thông tin kết hợp giữa PackageService và Membership
                    if (packageService != null)
                    {
                        if (packageService.PackageType == 0)
                        {
                            Log.Information("{LogPrefix} Applied combined benefits - PackageService (Usage Count) + Membership: FinalPrice={Final:N0}",
                                currentHospitalService.LogPrefix, unitPriceTotal);
                        }
                        else
                        {
                            Log.Information("{LogPrefix} Applied combined discounts - PackageService (Individual Service Discount) + Membership: BeforeMembership={BeforeMembership:N0}, Final={Final:N0}",
                                currentHospitalService.LogPrefix, priceBeforeMembership, unitPriceTotal);
                        }
                    }
                }

                // Kiểm tra tổng tiền cuối cùng (sau khi áp dụng cả PackageService và Membership)
                if (request.TotalPrice.HasValue && Math.Floor(unitPriceTotal) != request.TotalPrice.Value)
                {
                    result.Set(false, $"Tổng tiền không khớp. Tổng tiền tính được: {unitPriceTotal:N0}, Tổng tiền yêu cầu: {request.TotalPrice.Value:N0}", ErrorTypeEnum.MediPayError);
                    
                    var discountInfo = "";
                    if (packageService != null && !string.IsNullOrEmpty(request.MembershipId))
                    {
                        discountInfo = " (có áp dụng cả PackageService và Membership)";
                    }
                    else if (packageService != null)
                    {
                        discountInfo = " (có áp dụng PackageService)";
                    }
                    else if (!string.IsNullOrEmpty(request.MembershipId))
                    {
                        discountInfo = " (có áp dụng Membership)";
                    }

                    Log.Warning("{LogPrefix} Price mismatch{DiscountInfo}. Calculated: {Calculated}, Requested: {Requested}",
                        currentHospitalService.LogPrefix, discountInfo, unitPriceTotal, request.TotalPrice.Value);
                    return result;
                }

                register.CustomerId = customer.Id;
                register.CustomerRelationId = customerRelationship.Id;
                register.MembershipId = request.MembershipId;
                register.HealthServiceId = string.Empty;
                var registerAt = DateTimeHelper.GetCurrentLocalDateTime();
                register.RegisterAt = registerAt.AddHours(-7);
                register.SetCreate(currentUserService.UserName);
                register.SubTotalAmount = unitPriceTotal;
                register.TotalAmount = unitPriceTotal;
                register.HealthInsurance = request.IsInsurance ? 1 : 0; //. Không dùng thẻ BHYT
                register.ClinicId = string.Empty;
                register.ClinicCode = string.Empty;
                register.Clinic = string.Empty;
                register.ClinicGroupId = string.Empty;
                register.ClinicGroupCode = string.Empty;
                register.ClinicGroup = string.Empty;
                register.SubClinicId = string.Empty;
                register.SubClinic = string.Empty;
                register.HospitalId = currentHospitalService.CurrentHospital.HospitalId;
                register.DeviceId = currentHospitalService.KioskId;
                register.ExameTypeId = string.Empty;
                register.ExameType = string.Empty;
                register.HealthcareServiceTierId = request.HealthcareServiceTierId;
                register.HealthcareServiceTypeId = request.HealthcareServiceTypeId;
                register.MedicalTreatmentCategoryId = request.MedicalTreatmentCategoryId;
                register.MedicalTreatmentCategoryHisId = request.MedicalTreatmentCategoryHisId;
                register.ReasonForVisit = request.ReasonForVisit ?? string.Empty;
                register.TransferReferralDocumentNumber = request.TransferReferralDocumentNumber ?? string.Empty;
                register.TransferReferralDiseaseCode = request.TransferReferralDiseaseCode ?? string.Empty;
                register.TransferReferralUnit = request.TransferReferralUnit ?? string.Empty;
                register.ReferralLevel = request.Insurance?.ReferralLevel ?? string.Empty;
                register.PaymentType = request.PaymentType ?? string.Empty;
                register.PatientNameRef = request.PatientNameRef ?? string.Empty;
                register.PatientDateOfBirthRef = request.PatientDateOfBirthRef ?? string.Empty;
                register.PatientPhoneRef = request.PatientPhoneRef ?? string.Empty;

                // Log thông tin PackageService nếu có
                if (packageService != null)
                {
                    var packageTypeName = packageService.PackageType == 0 ? "Usage Count" : "Discount";
                    Log.Information("{LogPrefix} Using PackageService: Id={PackageId}, Name={PackageName}, Type={PackageType}({PackageTypeName}), OriginalPrice={OriginalPrice:N0}, PromotionPrice={PromotionPrice:N0}, UsageCount={UsageCount}, ServiceCount={ServiceCount}", 
                        currentHospitalService.LogPrefix, packageService.Id, packageService.Name, packageService.PackageType, packageTypeName, 
                        packageService.OriginalPrice, packageService.PromotionPrice, packageService.UsageCount, packageDetails?.Count ?? 0);
                }

                (bool createResult, string createMessage, errorType, string registerNo, RegisterFormsResponseDto resRegisterFrom)
                    = await hisService.CreateRegisterFormWithMultiService(new RegisterFormsRequestDto
                    {
                        Customer = customer,
                        CustomerRelationship = customerRelationship,
                        CustomerHospital = customerHospital,
                        Service = healthServices,
                        IsInsurance = request.IsInsurance,
                        Insurance = request.Insurance,
                        HealthcareServiceTierId = request.HealthcareServiceTierId,
                        HealthcareServiceTypeId = request.HealthcareServiceTypeId,
                        MedicalTreatmentCategoryId = request.MedicalTreatmentCategoryId,
                        MedicalTreatmentCategoryHisId = request.MedicalTreatmentCategoryHisId,
                        ReasonForVisit = request.ReasonForVisit,
                        TransferReferralDocumentNumber = request.TransferReferralDocumentNumber,
                        TransferReferralDiseaseCode = request.TransferReferralDiseaseCode,
                        TransferReferralDiseaseCodeAndName = request.TransferReferralDiseaseCodeAndName,
                        TransferReferralUnit = request.TransferReferralUnit,
                        TransferReferralType = request.TransferReferralType,
                        TransferReferralReason = request.TransferReferralReason,
                        TransferReferralDate = request.TransferReferralDate,
                        TransferReferralDiagnosisInfo = request.TransferReferralDiagnosisInfo,
                        CustomerRelationshipName = customerRelationshipName,
                        CustomerRelationshipId = customerRelationshipId,
                        DeviceId = currentHospitalService.KioskId,
                        PaymentAmount = unitPriceTotal,
                        BloodPressure = request.BloodPressure,
                        HeartRate = request.HeartRate,
                        RespiratoryRate = request.RespiratoryRate,
                        BloodOxygen = request.BloodOxygen,
                        Height = request.Height,
                        Weight = request.Weight,
                        PulseRate = request.PulseRate,
                        Temperature = request.Temperature,
                        PaymentType = request.PaymentType,
                        Priority = request.UuTien,
                        RegisterTime = registerAt,
                    });

                register.IsHisCreateFormSuccess = createResult;
                register.HisMessageWhenCreateForm = createMessage;

                Log.Information("{LogPrefix} Get Health Services getResult: {createResult} - - - message: {createResult} - - - patientCode: {registerNo} ", currentHospitalService.LogPrefix, createResult, createResult, registerNo);
                if (!createResult)
                {
                    result.Set(false, createMessage, ErrorTypeEnum.HisError);
                    return result;
                }

                /// Nên tạo thành công. => tạo phiếu đăng ký + thanh toán ở GT luôn
                if (resRegisterFrom != null)
                {
                    register.QueueNumberPriority = resRegisterFrom.QueueNumberPriority == "1";
                    register.RefDocNo = resRegisterFrom.RefDocNo;
                    register.ExaminationLocation = resRegisterFrom.ExaminationLocation;
                    register.MedicalTreatmentCategoryName = resRegisterFrom.MedicalTreatmentCategoryName;
                    register.RateOfInsurance = resRegisterFrom.RateOfInsurance;
                    register.PatientCode = resRegisterFrom.PatientCode;
                    register.LinkCode = resRegisterFrom.LinkCode;
                    register.ExpectedAppointmentAt = resRegisterFrom.ExpectedAppointmentAt;

                    if (!string.IsNullOrEmpty(resRegisterFrom.HealthcareServiceTierId))
                    {
                        register.HealthcareServiceTierId = resRegisterFrom.HealthcareServiceTierId;
                    }

                    foreach (var registerDetail in registerDetails)
                    {
                        var resDetail = resRegisterFrom.RegisterDetails.FirstOrDefault(x => x.HealthServiceId == registerDetail.HealthServiceId);
                        registerDetail.RefNo = resDetail?.RegisterDetailId ?? string.Empty;
                        registerDetail.QueueNumber = resDetail?.QueueNumber ?? 0;
                        registerDetail.ExaminationLocation = resDetail?.ExaminationLocation ?? string.Empty;
                    }

                    var receipt = new Receipt
                    {
                        Number = IdentityHelper.Guid(15),
                        CustomerId = customer.Id,
                        TotalAmount = unitPriceTotal,
                        ReceiptDate = DateTime.UtcNow,
                        RegisterNumber = register.Number,
                        Status = unitPriceTotal > 0 ? ReceiptConstant.STATUS_NEW : ReceiptConstant.STATUS_PAID,
                        DeviceId = currentHospitalService.KioskId,
                        HospitalId = register.HospitalId,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.ReceiptRefNo,
                        CreatedAt = DateTime.UtcNow
                    };

                    var payment = new Payment
                    {
                        Id = receipt.Number,
                        QrCode = currentHospitalService.CurrentHospital.IsGenQR ? resRegisterFrom.QrCode : string.Empty,
                        RefNo = resRegisterFrom.PaymentRefNo,
                        PaymentDate = DateTime.UtcNow,
                        PaymentAmount = receipt.TotalAmount,
                        ReceiptNumber = receipt.Number,
                        CreatedAt = DateTime.UtcNow,
                        Status = unitPriceTotal > 0 ? PaymentConstant.WaitForPayment : PaymentConstant.Success,
                        InvoiceInfoRef = resRegisterFrom.RefDocNo,
                        IsHisGenQr = currentHospitalService.CurrentHospital.IsGenQR,
                        HospitalId = register.HospitalId
                    };

                    //tạo QR code khi tạo phiếu đăng ký nếu cấu hình bệnh viện cho phép
                    if (currentHospitalService.CurrentHospital.IsGenQRWhenCreateRegister && unitPriceTotal > 0)
                    {
                        //3. Call API Gen Qr
                        var config = new PaymentConfigModel()
                        {
                            Url = paymentConfig.PaymentUrl,
                            SecretKey = paymentConfig.SecretKey
                        };

                        //get hospital
                        var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
                        if (hospital == null)
                        {
                            result.Set(false, "Không tìm thấy bệnh viện", errorType);
                            return result;
                        }

                        string paymentDescription = !string.IsNullOrEmpty(receipt.RefNo) ? receipt.RefNo : receipt.Number;

                        //remove special characters include space, 0-9a-zA-Z, and -, .
                        paymentDescription = Regex.Replace(paymentDescription, @"[^\s0-9a-zA-Z\-\.]", "");
                        // limit to 45 characters
                        paymentDescription = paymentDescription.Length > 45 ? paymentDescription[..45] : paymentDescription;
                        var paymentRequest = new CreateQrRequest()
                        {
                            MerchantId = hospital.MerchantId,
                            InvoiceId = payment.Id,
                            Type = PaymentConstant.DefaultType,
                            TransactionAmount = (double)payment.PaymentAmount,
                            Ipn = paymentConfig.IpnUrl,
                            TransactionDescription = $"THANH TOAN PHIEU THU {paymentDescription}",
                        };

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Req: {@Request}", currentHospitalService.LogPrefix, paymentRequest);

                        (bool processResult, string processMessage, CreateQrResponse? res) = await
                            PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);

                        Log.Information("{LogPrefix} CreateQrPaymentHandler Res: Result {Result} - Message {Message} - Response {@Response}",
                            currentHospitalService.LogPrefix, processResult, processMessage, res);

                        config.Dispose();

                        if (processResult)
                        {
                            payment.QrCode = res!.QrCode;
                            payment.RefNo = res!.PaymentCode;
                            receipt.QrCode = payment.QrCode;
                            payment.RefDescReq = res!.TransactionDescription;
                        }
                    }

                    databaseService.Receipts.Add(receipt);
                    databaseService.Payments.Add(payment);
                    if (membershipHistory != null)
                    {
                        membershipHistory.ReceiptNumber = receipt.Number;
                        membershipHistory.RefNo = receipt.RefNo;
                        membershipHistory.RefNo = resRegisterFrom.ReceiptRefNo;
                        databaseService.MembershipHistories.Add(membershipHistory);
                    }

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientCode)
                        && resRegisterFrom.PatientCode != customerHospital.PatientCode)
                    {
                        customerHospital.PatientCode = resRegisterFrom.PatientCode;
                    }

                    if (!string.IsNullOrEmpty(resRegisterFrom.PatientId)
                        && resRegisterFrom.PatientId != customerHospital.PatientId)
                    {
                        customerHospital.PatientId = resRegisterFrom.PatientId;
                    }
                }

                // Save to database
                if (isNotFoundCustomerHospital)
                {
                    databaseService.CustomerHospitals.Add(customerHospital);
                }
                else
                {
                    databaseService.CustomerHospitals.Update(customerHospital);
                }

                register.ServiceName = string.Empty;
                register.RefNo = registerNo;
                register.RefRegisterNumber = request.RefRegisterNumber;
                register.RegisterType = "DANG_KY_N";

                databaseService.Registers.Add(register);
                databaseService.RegisterDetails.AddRange(registerDetails);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);
                Log.Information("{LogPrefix} saveResult: {saveResult} ", currentHospitalService.LogPrefix, saveResult);
                if (saveResult > 0)
                {
                    result.Set(true, RegisterConstant.SaveChangesSuccess, new CreateRegisterFormWithMultiServiceResponseDto
                    {
                        Number = register.Number,
                        RefNo = register.RefNo,
                        HealthInsurance = register.HealthInsurance,
                        UnitPrice = register.TotalAmount
                    });
                }
                else
                {
                    result.Set(false, RegisterConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Có lỗi xảy ra khi tạo phiếu tiếp nhận", currentHospitalService.LogPrefix);
                result.Set(false, RegisterConstant.CreateError, errorType);
            }

            return result;
        }
    }
}
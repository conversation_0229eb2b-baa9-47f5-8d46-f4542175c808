using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormRequestDto
    {
        public Customer Customer { get; set; } = new Customer();
        public Customer CustomerRelationship { get; set; } = new Customer();
        public CustomerHospital CustomerHospital { get; set; } = new CustomerHospital();
        public HealthService Service { get; set; } = new HealthService();
        public bool IsInsurance { get; set; }
        public CustomerHealthInsurance? Insurance { get; set; }

        /// <summary>
        /// Tuyến khám bệnh: <PERSON>úng tuyến, tr<PERSON>i tuyến, Đa tuyến đúng tuyến
        /// </summary>
        public string? HealthcareServiceTierId { get; set; }

        /// <summary>
        /// Loại hình khám bệnh: Khám bệnh, ngoại trú, Nội trú....
        /// </summary>
        public string? HealthcareServiceTypeId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh
        /// </summary>
        public string? MedicalTreatmentCategoryId { get; set; }

        /// <summary>
        /// Mã đối tượng khám chữa bệnh HIS: Bảo hiểm, tự nguyện, viện phí,..
        /// </summary>
        public string? MedicalTreatmentCategoryHisId { get; set; }

        /// <summary>
        /// Lý do khám bệnh
        /// </summary>
        public string? ReasonForVisit { get; set; }

        /// <summary>
        /// Mã bệnh viện chuyển tuyến
        /// </summary>
        public string? TransferReferralDocumentNumber { get; set; }
        /// <summary>
        /// Mã bệnh chuyển tuyến
        /// </summary>
        public string? TransferReferralDiseaseCode { get; set; }
        /// <summary>
        /// Mã bệnh chuyển tuyến (Kèm tên bệnh)
        /// </summary>
        public string? TransferReferralDiseaseCodeAndName { get; set; }
        /// <summary>
        /// Đơn vị chuyển tuyến
        /// </summary>
        public string? TransferReferralUnit { get; set; }
        /// <summary>
        /// Hình thức chuyển tuyến
        /// </summary>
        public string? TransferReferralType { get; set; }

        /// <summary>
        /// Lý do chuyển tuyến
        /// </summary>
        public string? TransferReferralReason { get; set; }

        /// <summary>
        /// Ngày chuyển tuyến (dd/MM/yyyy)
        /// </summary>
        public string? TransferReferralDate { get; set; }

        /// <summary>
        /// Thông tin chuẩn đoán tuyến dưới 
        /// </summary>
        public string? TransferReferralDiagnosisInfo { get; set; }

        /// <summary>
        /// Tên của mối quan hệ giữa khách hàng
        /// </summary>
        public string? CustomerRelationshipName { get; set; }
        /// <summary>
        /// Mã mối quan hệ giữa khách hàng
        /// </summary>
        public string? CustomerRelationshipId { get; set; }
        public string? DeviceId { get; set; }
        /// <summary>
        /// Giá khám cuối cùng
        /// </summary>
        public decimal? PaymentAmount { get; set; }
        //Chỉ số sinh tồn
        public string? BloodPressure { get; set; }
        public int? HeartRate { get; set; }
        public int? RespiratoryRate { get; set; }
        public double? BloodOxygen { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public int? PulseRate { get; set; }
        public double? Temperature { get; set; }

        //Loại hình thanh toán: Không thanh toán (free), Tiền mặt (cash), thẻ (card), chuyển khoản (qr),...
        public string? PaymentType { get; set; }
        //Có điều trị arv không
        public bool IsOnARVTreatment { get; set; }
        // Có ưu tiên khám không
        public int Priority { get; set; }
        // Thời gian đăng ký
        public DateTime RegisterTime { get; set; }
        // chuẩn đoán trước khi nhập viện
        public string? DiagnosisBeforeAdmission { get; set; }
        // Mã ICD của chuẩn đoán trước khi nhập viện
        public string? DiagnosisBeforeAdmissionICD { get; set; }
        // Mã tai nạn (nếu có)
        public string? AccidentCode { get; set; }
        // Nhóm máu (nếu có)
        public string? BloodType { get; set; }
    }
}

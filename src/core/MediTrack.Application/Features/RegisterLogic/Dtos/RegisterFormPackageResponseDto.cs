using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormPackageResponseDto
    {
        public string RegisterNumber { get; set; } = string.Empty;
        public string? PaymentRefNo { get; set; }
        public string? ReceiptRefNo { get; set; }
        public string QrCode { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string PatientId { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string QueueNumber { get; set; } = string.Empty;
    }
}
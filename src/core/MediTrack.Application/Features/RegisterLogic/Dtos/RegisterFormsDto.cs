namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormWithMultiServiceDto
    {
        public string CustomerKey { get; set; } = string.Empty;
        public string CustomerRelationKey { get; set; } = string.Empty;
        public decimal AdvancePayment { get; set; } = decimal.Zero;
        public List<HealthServiceDto> Service { get; set; } = [];
    }

    public class HealthServiceDto
    {
        public string HealthServiceId { get; set; } = string.Empty;
        public string ServiceCode { get; set; } = string.Empty;
        public string ExameTypeId { get; set; } = string.Empty;
        public string ClinicId { get; set; } = string.Empty;
        public string ClinicCode { get; set; } = string.Empty;
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormPackageRequestDto
    {
        public Customer Customer { get; set; } = new Customer();
        public CustomerHospital CustomerHospital { get; set; } = new CustomerHospital();
        public HealthPackage HealthPackage{ get; set; } = new HealthPackage();
        public List<TechnicalService> TechnicalServices { get; set; } = new List<TechnicalService>();
        public int Priority { get; set; }
    }
}
namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterDetailDto
    {
        public string RegisterDetailId { get; set; } = string.Empty;
        public string RegisterId { get; set; } = string.Empty;
        public string HealthServiceId { get; set; } = string.Empty;
        public string HealthServiceName { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string ServiceType { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public int? Quantity { get; set; }
        public string UnitOfMeasure { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public int? Status { get; set; }
        public int? QueueNumber { get; set; }
    }
}

namespace MediTrack.Application.Features.RegisterLogic.Dtos
{
    public class RegisterFormsResponseDto
    {
        public string RegisterNumber { get; set; } = string.Empty;
        public string? PaymentRefNo { get; set; }
        public string? ReceiptRefNo { get; set; }
        public string Clinic { get; set; } = string.Empty;
        public string QueueNumberPriority { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public string RefDocNo { get; set; } = string.Empty;
        public string ExaminationLocation { get; set; } = string.Empty;
        public string RateOfInsurance { get; set; } = string.Empty;
        public string PatientId { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string LinkCode { get; set; } = string.Empty;
        public string MedicalTreatmentCategoryName { get; set; } = string.Empty;
        public DateTime? ExpectedAppointmentAt { get; set; }
        public string? HealthcareServiceTierId { get; set; }
        
        /// <summary>
        /// Thong tin dang ky
        /// </summary>
        public string RegisterId { get; set; } = string.Empty;
        public string RegisterCode { get; set; } = string.Empty;
        public string RegisterName { get; set; } = string.Empty;
        public string RegisterDate { get; set; } = string.Empty;
        public int? RegisterStatus { get; set; }
        public decimal RegisterAmount { get; set; }
        public string RegisterDescription { get; set; } = string.Empty;

        /// <summary>
        /// Danh sách chi tiết đăng ký từ chi_tiet_dang_ky
        /// </summary>
        public List<RegisterDetailDto> RegisterDetails { get; set; } = [];
    }
}

﻿namespace MediTrack.Application.Features.WardLogic.Dtos
{
    public class WardDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? OtherName { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string ProvinceId { get; set; } = string.Empty;
        public string NewId { get; set; } = string.Empty;
        public string NewName { get; set; } = string.Empty;
        public string? NewOtherName { get; set; } = string.Empty;
        public string? NewLevel { get; set; } = string.Empty;
        public string NewProvinceId { get; set; } = string.Empty;
    }
}

﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediatR;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.WardLogic.Queries
{
    public class GetWard : IRequest<BaseCommandResultWithData<WardDto>>
    {
        public string Id { get; set; } = string.Empty;
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class GetWardHandler(IWardRepository wardRepository)
        : IRequestHandler<GetWard, BaseCommandResultWithData<WardDto>>
    {
        public async Task<BaseCommandResultWithData<WardDto>> Handle(
            GetWard request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<WardDto>();
            var allWards = await wardRepository.GetWardByParentIdAsync(null, false, cancellationToken);
            var ward = allWards.FirstOrDefault(d => (request.IsTwoLevelAddress.GetValueOrDefault() ? d.NewId : d.Id) == request.Id);
            if (ward == null)
            {
                result.Set(false, WardConstant.NotFound);
            }
            else
            {
                var mappedWard = new WardDto
                {
                    Id = ward.Id,
                    Name = ward.Name,
                    OtherName = ward.OtherName,
                    DistrictId = ward.DistrictId,
                    Level = ward.Level,
                    NewId = ward.NewId,
                    NewName = ward.NewName,
                    NewLevel = ward.NewLevel,
                    NewProvinceId = ward.NewProvinceId,
                };
                result.Set(false, WardConstant.Ok, mappedWard);
            }

            return result;
        }
    }
}

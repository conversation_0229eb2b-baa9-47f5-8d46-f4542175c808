using FluentValidation;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.MemberShipLogic.Commands
{
    public class CreateMembershipCommandRequest : IRequest<BaseCommandResult>
    {
        public string PackageServiceId { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string LogPrefix { get; set; } = string.Empty;

        public class Validation : AbstractValidator<CreateMembershipCommandRequest>
        {
            public Validation()
            {
                RuleFor(x => x.PackageServiceId).NotEmpty();
                RuleFor(x => x.CustomerId).NotEmpty();
            }
        }

        public class Handler(
           IDatabaseService databaseService) : IRequestHandler<CreateMembershipCommandRequest, BaseCommandResult>
        {

            public async Task<BaseCommandResult> Handle(CreateMembershipCommandRequest request, CancellationToken cancellationToken)
            {
                var result = new BaseCommandResult();
                try
                {
                    var packageService = await databaseService.PackageServices
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == request.PackageServiceId, cancellationToken: cancellationToken);

                    Log.Information("{LogPrefix} Create membership for package service {PackageServiceId}",
                        request.LogPrefix, request.PackageServiceId);

                    if (packageService == null)
                    {
                        result.Set(false, HealthServiceConstant.NotFound, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var customer = await databaseService.Customers
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == request.CustomerId, cancellationToken: cancellationToken);

                    if (customer == null)
                    {
                        result.Set(false, HealthServiceConstant.NotFound, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var prefix = packageService.MembershipPrefix ?? "";
                    var suffix = packageService.MembershipSuffix ?? "";

                    string memberCode;
                    bool isUnique = false;
                    int attempts = 0;
                    const int maxAttempts = 10; // Prevent infinite loop

                    do
                    {
                        var random = new Random();
                        var randomNumber = random.Next(1000, 9999); // 4-digit random number
                        memberCode = $"{prefix}{DateTime.Now:yyyyMMdd}{randomNumber}{suffix}";

                        // Check if the generated code already exists
                        var existingMembership = await databaseService.Memberships
                            .AsNoTracking()
                            .FirstOrDefaultAsync(x => x.MemberCode == memberCode, cancellationToken: cancellationToken);
                        
                        isUnique = existingMembership == null;
                        attempts++;
                    } while (!isUnique && attempts < maxAttempts);

                    if (!isUnique)
                    {
                        result.Set(false, "Unable to generate unique membership code after multiple attempts");
                        return result;
                    }

                    var membership = new Membership
                    {
                        Id = IdentityHelper.Guid(16),
                        MemberCode = memberCode,
                        CardName = customer.LastName + " " + customer.FirstName,
                        ContactPhone = customer.Phone ?? string.Empty,
                        UsageCount = packageService.UsageCount,
                        FromAge = packageService.FromAge,
                        ToAge = packageService.ToAge,
                        DiscountValue = packageService.DiscountValue,
                        DiscountUnit = packageService.DiscountUnit,
                        ExpirationDate = packageService.ExpirationDays.HasValue ? DateTime.UtcNow.AddDays(packageService.ExpirationDays.Value) : null,
                        MinimumBalance = packageService.MinimumBalance,
                        Balance = packageService.Balance,
                        CustomerId = customer.Id,
                        HospitalId = packageService.HospitalId ?? string.Empty,
                        PackageServiceId = request.PackageServiceId,
                        Type = (MembershipTypeEnum)packageService.PackageType,
                        CreatedAt = DateTime.UtcNow,
                    };

                    Log.Information("{LogPrefix} Create membership with code {MemberCode} for customer {CustomerId}",
                        request.LogPrefix, memberCode, request.CustomerId);

                    databaseService.Memberships.Add(membership);

                    // Save changes in the outer caller function
                    result.Set(true, PaymentConstant.CreateSuccessFully, ErrorTypeEnum.NoError);
                    return result;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "{LogPrefix} Error creating membership: {Message}", request.LogPrefix, ex.Message);
                    result.Set(false, PaymentConstant.CreateError, ErrorTypeEnum.MediPayError);
                    return result;
                }
            }
        }
    }
}
﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.ExameTypeLogic.Queries
{
    public class GetExameTypes
        : IRequest<BaseCommandResultWithData<List<ExameType>>>
    {

    }

    public class GetExameTypesHandler(ICachedService cachedService,
        IHospitalMetadataRepository hospitalMetadataRepository,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetExameTypes, BaseCommandResultWithData<List<ExameType>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<ExameType>>> Handle(
            GetExameTypes request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<ExameType>>();
            var errorType = ErrorTypeEnum.MediPayError;
            try
            {
                List<ExameType>? data = await cachedService.GetAsync<List<ExameType>>("ExameTypes" + currentHospitalService.CurrentHospital.HospitalId, cancellationToken);

                if (data is null || data.Count == 0)
                {
                    var hospitalMetadata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync
                    (currentHospitalService.CurrentHospital.HospitalId, "exame_types_default", cancellationToken: cancellationToken);
                    if (hospitalMetadata != null && !string.IsNullOrEmpty(hospitalMetadata.Value))
                    {
                        data = JsonConvert.DeserializeObject<List<ExameType>>(hospitalMetadata.Value);
                        if (data != null && data.Count > 0)
                        {
                            await cachedService.SetAsync("ExameTypes" + currentHospitalService.CurrentHospital.HospitalId, data, cancellationToken);
                        }
                    }
                    else
                    {
                        (bool success, _, errorType, data) = await hisService.GetExameTypes();

                        if (success && data != null && data.Count > 0)
                        {
                            await cachedService.SetAsync("ExameTypes" + currentHospitalService.CurrentHospital.HospitalId, data, cancellationToken);
                        }
                    }
                }

                if (currentHospitalService.CurrentHospital.IsSupportInsurance == false)
                {
                    data = data?.Where(x => x.IsInsurance == false).ToList();
                }

                if (currentHospitalService.CurrentHospital.IsBlockInsuranceOnWeekend)
                {
                    if (DateTimeHelper.GetCurrentLocalDateTime().IsWeekend())
                    {
                        data?.ForEach(e => e.IsDisabled = e.IsDisabled || e.IsInsurance);
                    }
                }

                result.Set(true, ExameTypeConstant.Ok, data ?? []);
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message, errorType);
                Log.Error(ex, "{LogPrefix} Error when get exame types", currentHospitalService.LogPrefix);
            }

            return result;
        }
    }
}

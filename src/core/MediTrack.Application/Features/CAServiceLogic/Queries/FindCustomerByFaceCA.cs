using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using CAService.Lib.Requests;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Queries
{
    public class FindCustomerByFaceCA : IRequest<BaseCommandResultWithData<FindCustomerByFaceResponse>>
    {
        public string FaceImgBase64 { get; set; } = string.Empty;
    }

    public class FindCustomerByFaceCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<FindCustomerByFaceCA, BaseCommandResultWithData<FindCustomerByFaceResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<FindCustomerByFaceResponse>> Handle(
            FindCustomerByFaceCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<FindCustomerByFaceResponse>();

            try
            {
                var caRequest = new FaceRecognitionRequest
                {
                    FaceImgBase64 = request.FaceImgBase64
                };

                var (success, message, errorType, data) = await caService.GetCustomersByFaceAsync(caRequest);

                var response = new FindCustomerByFaceResponse
                {
                    Customers = [.. data.Faces.Select(f => new CustomerFaceInfo
                    {
                        CustomerId = Guid.TryParse(f.CustomerId, out var id) ? id : Guid.Empty,
                        FullName = f.FullName,
                        Phone = f.Phone,
                        Email = f.Email,
                        IdentityNo = f.IdentityNo
                    })]
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when finding customer by face", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while finding customer by face");
            }

            return result;
        }
    }
}

using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Queries
{
    public class GetSignedPdfCA : IRequest<BaseCommandResultWithData<GetSignedPdfResponse>>
    {
        public Guid Id { get; set; }
    }

    public class GetSignedPdfCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<GetSignedPdfCA, BaseCommandResultWithData<GetSignedPdfResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<GetSignedPdfResponse>> Handle(
            GetSignedPdfCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<GetSignedPdfResponse>();

            try
            {
                var (success, message, errorType, data) = await caService.GetSignedPdfAsync(request.Id);

                var response = new GetSignedPdfResponse
                {
                    Id = data.Id,
                    CustomerId = data.CustomerId,
                    FileName = data.FileName,
                    DownloadUrl = data.DownloadUrl,
                    Status = data.Status,
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when getting signed PDF", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while getting signed PDF");
            }

            return result;
        }
    }
}

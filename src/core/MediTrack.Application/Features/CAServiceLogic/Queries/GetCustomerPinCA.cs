using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using CAService.Lib.Requests;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Queries
{
    public class GetCustomerPinCA : IRequest<BaseCommandResultWithData<GetCustomerPinResponse>>
    {
        public string FaceImgBase64 { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
    }

    public class GetCustomerPinCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<GetCustomerPinCA, BaseCommandResultWithData<GetCustomerPinResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<GetCustomerPinResponse>> Handle(
            GetCustomerPinCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<GetCustomerPinResponse>();

            try
            {
                var caRequest = new CustomerPinRequest
                {
                    FaceImgBase64 = request.FaceImgBase64,
                    CustomerId = request.CustomerId
                };

                var (success, message, errorType, data) = await caService.GetCustomerPinAsync(caRequest);

                var response = new GetCustomerPinResponse
                {
                    IsSuccess = success,
                    CustomerId = data.CustomerId,
                    Pin = data.Pin,
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when getting customer pin", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while getting customer pin");
            }

            return result;
        }
    }
}

namespace MediTrack.Application.Features.CAServiceLogic.Dtos
{
    public class FindCustomerByFaceResponse
    {
        public List<CustomerFaceInfo> Customers { get; set; } = new();
    }

    public class CustomerFaceInfo
    {
        public Guid CustomerId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string IdentityNo { get; set; } = string.Empty;
    }
}

using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using CAService.Lib.Requests;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Commands
{
    public class CreateSignRequestCA : IRequest<BaseCommandResultWithData<CreateSignRequestResponse>>
    {
        public string FileName { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
    }

    public class CreateSignRequestCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<CreateSignRequestCA, BaseCommandResultWithData<CreateSignRequestResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<CreateSignRequestResponse>> Handle(
            CreateSignRequestCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateSignRequestResponse>();

            try
            {
                var caRequest = new SignPdfRequestRequest
                {
                    FileName = request.FileName,
                    CustomerId = request.CustomerId
                };

                var (success, message, errorType, data) = await caService.CreateSignPdfRequestAsync(caRequest);

                var response = new CreateSignRequestResponse
                {
                    SignedPDFId = data.SignedPDFId,
                    FileName = request.FileName,
                    CustomerId = request.CustomerId
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when creating sign request", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while creating sign request");
            }

            return result;
        }
    }
}

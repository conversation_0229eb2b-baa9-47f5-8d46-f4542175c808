using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using CAService.Lib.Requests;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Commands
{
    public class CreateCustomerCA : IRequest<BaseCommandResultWithData<CreateCustomerResponse>>
    {
        public string IdentityNo { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string FaceImgBase64 { get; set; } = string.Empty;
    }

    public class CreateCustomerCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<CreateCustomerCA, BaseCommandResultWithData<CreateCustomerResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<CreateCustomerResponse>> Handle(
            CreateCustomerCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CreateCustomerResponse>();

            try
            {
                var caRequest = new CreateCustomerRequest
                {
                    IdentityNo = request.IdentityNo,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber,
                    FullName = request.FullName,
                    FaceImgBase64 = request.FaceImgBase64
                };

                var (success, message, errorType, data) = await caService.CreateCustomerAsync(caRequest);

                var response = new CreateCustomerResponse
                {
                    IsSuccess = success,
                    CustomerId = data.CustomerId,
                    Message = message
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when creating customer", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while creating customer");
            }

            return result;
        }
    }
}

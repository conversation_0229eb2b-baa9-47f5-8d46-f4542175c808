using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Features.CAServiceLogic.Dtos;
using CAService.Lib.Requests;
using Serilog;

namespace MediTrack.Application.Features.CAServiceLogic.Commands
{
    public class SignPdfCA : IRequest<BaseCommandResultWithData<SignPdfResponse>>
    {
        public Guid SignedPDFId { get; set; }
        public Guid MedicalRecordFileId { get; set; }
        public string KeyBase64 { get; set; } = string.Empty;
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int PageNumber { get; set; }
    }

    public class SignPdfCAHandler(
        ICurrentHospitalService currentHospitalService,
        ICAService caService)
        : IRequestHandler<SignPdfCA, BaseCommandResultWithData<SignPdfResponse>>
    {
        private readonly ICAService caService = caService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;

        public async Task<BaseCommandResultWithData<SignPdfResponse>> Handle(
            SignPdfCA request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<SignPdfResponse>();

            try
            {
                var caRequest = new SignPdfRequest
                {
                    SignedPDFId = request.SignedPDFId,
                    MedicalRecordFileId = request.MedicalRecordFileId,
                    KeyBase64 = request.KeyBase64,
                    X = request.X,
                    Y = request.Y,
                    Width = request.Width,
                    Height = request.Height,
                    PageNumber = request.PageNumber
                };

                var (success, message, errorType, data) = await caService.SignPdfAsync(caRequest);

                var response = new SignPdfResponse
                {
                    IsSuccess = success,
                    SignedPdfId = data.SignedPDFId
                };

                result.Set(success, message, response, errorType: errorType);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when signing PDF", currentHospitalService.LogPrefix);
                result.Set(false, "Error occurred while signing PDF");
            }

            return result;
        }
    }
}

using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.NotificationLogic.Queries
{
    public class GetNotifications : IRequest<BaseCommandResultWithData<Dictionary<int, string>>>
    {
    }

    public class GetNotificationsHandler(IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService)
    : IRequestHandler<GetNotifications, BaseCommandResultWithData<Dictionary<int, string>>>
    {
        public async Task<BaseCommandResultWithData<Dictionary<int, string>>> Handle(
            GetNotifications request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Dictionary<int, string>>();

            var notifications = await databaseService.HospitalNotifications
                        .Where(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId)
                        .SelectMany(n => n.NotiConfig ?? new List<NotificationConfig>(), 
                            (n, c) => new { c.DayOfWeek, n.Value })
                        .Where(x => !string.IsNullOrEmpty(x.Value))
                        .OrderBy(x => x.DayOfWeek)
                        .ToDictionaryAsync(x => x.DayOfWeek, x => x.Value);

            if (notifications is not null)
            {
                result.Set(true, string.Empty, notifications);
            }
            return result;
        }
    }
}
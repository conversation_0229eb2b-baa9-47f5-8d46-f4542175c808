using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ICDLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.ICDLogic.Queries
{
    public class GetLowerLevelDiagnoses : IRequest<BaseCommandResultWithData<List<DiseaseCode>>>
    {
    }

    public class GetLowerLevelDiagnosesListHandler(
        IHospitalMetadataRepository hospitalMetadataRepository,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetLowerLevelDiagnoses, BaseCommandResultWithData<List<DiseaseCode>>>
    {
        public async Task<BaseCommandResultWithData<List<DiseaseCode>>> Handle(
            GetLowerLevelDiagnoses request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<DiseaseCode>>();

            var hospitalMetadata = await hospitalMetadataRepository
                .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital?.HospitalId, "lower_level_diagnoses", cancellationToken: cancellationToken);

            if (hospitalMetadata is not null && !string.IsNullOrEmpty(hospitalMetadata.Value))
            {
                List<DiseaseCode> diseaseCodes = JsonConvert.DeserializeObject<List<DiseaseCode>>(hospitalMetadata.Value) ?? [];
                result.Set(true, "", diseaseCodes);
                return result;
            }
            else
            {
                result.Set(true, "", []);
            }
            return result;
        }
    }
}
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ICDLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.ICDLogic.Queries
{
    public class GetICDList : IRequest<BaseCommandResultWithData<List<DiseaseCode>>>
    {
    }

    public class GetICDListHandler(IHospitalMetadataRepository hospitalMetadataRepository,
        ISystemMetadataRepository systemMetadataRepository,
        ICurrentHospitalService currentHospitalService)
    : IR<PERSON><PERSON>Hand<PERSON><GetICDList, BaseCommandResultWithData<List<DiseaseCode>>>
    {
        public async Task<BaseCommandResultWithData<List<DiseaseCode>>> Handle(
            GetICDList request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<DiseaseCode>>();

            //get ICD list from HospitalMetadata
            var hospitalMetadata = await hospitalMetadataRepository
                .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital?.HospitalId, "icd_10_list", cancellationToken: cancellationToken);

            if (hospitalMetadata is not null && !string.IsNullOrEmpty(hospitalMetadata.Value))
            {
                List<DiseaseCode> diseaseCodes = JsonConvert.DeserializeObject<List<DiseaseCode>>(hospitalMetadata.Value) ?? [];
                result.Set(true, "", diseaseCodes);
                return result;
            }

            // get ICD list from SystemMetadata
            var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("icd_10_list", cancellationToken: cancellationToken);

            if (systemMetaData is not null && !string.IsNullOrEmpty(systemMetaData.Value))
            {
                List<DiseaseCode> diseaseCodes = JsonConvert.DeserializeObject<List<DiseaseCode>>(systemMetaData.Value) ?? [];
                result.Set(true, "", diseaseCodes);
            }
            else
            {
                result.Set(false, "Không tìm thấy dữ liệu", []);
            }
            return result;
        }
    }
}
﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries
{
    public class GetMedicalTreatmentCategory : IRequest<BaseCommandResultWithData<List<GetMedicalTreatmentCategoryDto>>>
    {
    }

    public class GetMedicalTreatmentCategoryHandler(ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetMedicalTreatmentCategory, BaseCommandResultWithData<List<GetMedicalTreatmentCategoryDto>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<GetMedicalTreatmentCategoryDto>>> Handle(
            GetMedicalTreatmentCategory request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetMedicalTreatmentCategoryDto>>();

            (bool resultRes, string message, ErrorTypeEnum errorType, List<GetMedicalTreatmentCategoryDto> medicalTreatmentCategory) = await hisService.GetMedicalTreatmentCategories();
            if (!resultRes)
            {
                result.Set(false, message, errorType);
                return result;
            }

            result.Set(true, message, medicalTreatmentCategory);
            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries
{
    public class GetHealthcareServiceTier : IRequest<BaseCommandResultWithData<List<GetHealthcareServiceTierDto>>>
    {
    }

    public class GetHealthcareServiceTierHandler(ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetHealthcareServiceTier, BaseCommandResultWithData<List<GetHealthcareServiceTierDto>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<GetHealthcareServiceTierDto>>> Handle(
            GetHealthcareServiceTier request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetHealthcareServiceTierDto>>();

            (bool resultRes, string message, ErrorTypeEnum errorType, List<GetHealthcareServiceTierDto> healthcareServiceTiers) = await hisService.HealthcareServiceTiers();
            if (!resultRes)
            {
                result.Set(false, message, errorType);
                return result;
            }

            result.Set(true, message, healthcareServiceTiers);
            return result;
        }
    }
}

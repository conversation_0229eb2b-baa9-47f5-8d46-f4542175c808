﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries
{
    public class GetHealthcareServiceType : IRequest<BaseCommandResultWithData<List<GetHealthcareServiceTypeDto>>>
    {
    }

    public class GetHealthcareServiceTypeHandler(ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetHealthcareServiceType, BaseCommandResultWithData<List<GetHealthcareServiceTypeDto>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<GetHealthcareServiceTypeDto>>> Handle(
            GetHealthcareServiceType request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetHealthcareServiceTypeDto>>();

            (bool resultRes, string message, ErrorTypeEnum errorType, List<GetHealthcareServiceTypeDto> healthcareServiceTypes) = await hisService.HealthcareServiceTypes();
            if (!resultRes)
            {
                result.Set(false, message, errorType);
                return result;
            }

            result.Set(true, message, healthcareServiceTypes);
            return result;
        }
    }
}

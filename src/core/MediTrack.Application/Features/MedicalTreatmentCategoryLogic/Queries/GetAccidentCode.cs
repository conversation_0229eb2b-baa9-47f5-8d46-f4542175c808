﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Enums;
using Newtonsoft.Json;


namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries
{
    public class GetAccidentCode : IRequest<BaseCommandResultWithData<List<GetAccidentCodeDto>>>
    {
    }

    public class GetAccidentCodeHandler(ICurrentHospitalService current,
        IHospitalMetadataRepository? hospitalMetadataRepository)
        : IRequestHandler<GetAccidentCode, BaseCommandResultWithData<List<GetAccidentCodeDto>>>
    {
        private readonly IHisService hisService = current.HisService;

        public async Task<BaseCommandResultWithData<List<GetAccidentCodeDto>>> Handle(
            GetAccidentCode request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetAccidentCodeDto>>();
            var hospitalMetaData = hospitalMetadataRepository != null
            ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.CurrentHospital.HospitalId, "accident_code", cancellationToken : cancellationToken)
            : null;
            
            if (hospitalMetaData == null || string.IsNullOrEmpty(hospitalMetaData.Value))
            {
                // Nếu không có dữ liệu trong hospital metadata, gọi qua HIS
                var (success, message, errorType, accidentCodesFromHis) = await hisService.GetAccidentCodes();
                if (!success)
                {
                    result.Set(false, message, errorType);
                    return result;
                }
                result.Set(true, message, accidentCodesFromHis, ErrorTypeEnum.NoError);
                return result;
            }
            
            var accidentCodes = JsonConvert.DeserializeObject<List<GetAccidentCodeDto>>(hospitalMetaData.Value) ?? [];
            result.Set(true, "Lấy danh mục mã tai nạn thành công", accidentCodes, ErrorTypeEnum.NoError);
            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Enums;
using Newtonsoft.Json;


namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Queries
{
    public class GetBloodType : IRequest<BaseCommandResultWithData<List<GetBloodTypeDto>>>
    {
    }

    public class GetBloodTypeHandler(ICurrentHospitalService current,
        IHospitalMetadataRepository? hospitalMetadataRepository)
        : IRequestHandler<GetBloodType, BaseCommandResultWithData<List<GetBloodTypeDto>>>
    {
        private readonly IHisService hisService = current.HisService;

        public async Task<BaseCommandResultWithData<List<GetBloodTypeDto>>> Handle(
            GetBloodType request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetBloodTypeDto>>();
            var hospitalMetaData = hospitalMetadataRepository != null
            ? await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(current.CurrentHospital.HospitalId, "blood_type", cancellationToken : cancellationToken)
            : null;
            
            if (hospitalMetaData == null || string.IsNullOrEmpty(hospitalMetaData.Value))
            {
                // Nếu không có dữ liệu trong hospital metadata, gọi qua HIS
                var (success, message, errorType, bloodTypesFromHis) = await hisService.GetBloodTypes();
                if (!success)
                {
                    result.Set(false, message, errorType);
                    return result;
                }
                result.Set(true, message, bloodTypesFromHis, ErrorTypeEnum.NoError);
                return result;
            }
            
            var bloodTypes = JsonConvert.DeserializeObject<List<GetBloodTypeDto>>(hospitalMetaData.Value) ?? [];
            result.Set(true, "Lấy danh mục nhóm máu thành công", bloodTypes, ErrorTypeEnum.NoError);
            return result;
        }
    }
}

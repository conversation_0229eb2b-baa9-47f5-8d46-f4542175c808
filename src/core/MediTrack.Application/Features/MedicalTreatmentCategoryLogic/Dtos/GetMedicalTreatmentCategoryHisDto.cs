namespace MediTrack.Application.Features.MedicalTreatmentCategoryLogic.Dtos
{
    public class GetMedicalTreatmentCategoryHisDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool IsInsurance { get; set; }

        public static List<GetMedicalTreatmentCategoryHisDto> Default()
        {
            return
            [
                new GetMedicalTreatmentCategoryHisDto
                {
                    Id = "",
                    Name = "Khám dịch vụ",
                    IsInsurance = false
                },
                new GetMedicalTreatmentCategoryHisDto
                {
                    Id = "",
                    Name = "Khám bảo hiểm",
                    IsInsurance = true
                }
            ];
        }
    }
}
﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.KioskLogic.Commands
{
    public class ClearKioskCache : IRequest<BaseCommandResult> 
    { 
        public string KioskId { get; set; } = string.Empty;
    }

    public class ClearKioskCacheHandler(IHisServiceHelper hisServiceHelper, ICachedService cachedService): IRequestHandler<ClearKioskCache, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(
            ClearKioskCache request, CancellationToken cancellationToken)
        {
            if(!string.IsNullOrEmpty(request.KioskId))
            {
                await hisServiceHelper.ResetKioskCache(request.KioskId);
                await cachedService.RemoveAsync("KioskMetadatas" + request.KioskId, cancellationToken);

                return new BaseCommandResult { Success = true };
            }

            return new BaseCommandResult { Success = false, Messages = "KioskId is required" };
        }
    }
}

using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.KioskLogic.Dto;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Constants;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.KioskLogic.Queries
{
    public class GetTurnOffInfoKiosk : IRequest<BaseCommandResultWithData<List<KioskTurnOffInfoDto>>> { }

    public class GetTurnOffInfoKioskHandler(
        ICurrentHospitalService currentHospitalService,
        IKioskMetadataRepository kioskMetadataRepository)
    : IRequestHandler<GetTurnOffInfoKiosk, BaseCommandResultWithData<List<KioskTurnOffInfoDto>>>
    {
        public async Task<BaseCommandResultWithData<List<KioskTurnOffInfoDto>>> Handle(GetTurnOffInfoKiosk request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<KioskTurnOffInfoDto>>();

            try
            {
                // Return turn off infos from KioskMetaData using repository
                var turnOffInfos = await kioskMetadataRepository.GetKioskMetadatasByKeyAsync(
                    currentHospitalService.CurrentHospital.HospitalId, currentHospitalService.KioskId, "kiosk_turn_off_infos" , cancellationToken: cancellationToken);

                if (turnOffInfos.Count == 0)
                {
                    result.Set(true, KioskConstant.Ok, []);
                    return result;
                }

                var turnOffInfoDtos = turnOffInfos.Select(x =>
                {
                    ShutdownConfigValue value = JsonConvert.DeserializeObject<ShutdownConfigValue>(x.Value ?? string.Empty) ?? new ShutdownConfigValue();

                    return new KioskTurnOffInfoDto
                    {
                        DayOfWeek = int.Parse(x.Code ?? string.Empty),
                        ShutdownTime = value.ShutdownTime,
                        AfterMinute = value.AfterMinute
                    };
                }).ToList();

                result.Set(true, KioskConstant.Ok, turnOffInfoDtos);
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
                Log.Error(ex, "Error in GetTurnOffInfoKioskHandler: {Message}", ex.Message);
            }

            return result;
        }
    }

}
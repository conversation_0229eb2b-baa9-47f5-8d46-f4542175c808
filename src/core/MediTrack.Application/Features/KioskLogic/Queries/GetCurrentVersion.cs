using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.KioskLogic.Dto;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;

namespace MediTrack.Application.Features.KioskLogic.Queries
{
    public class GetCurrentVersion : IRequest<BaseCommandResultWithData<KioskInfoDto>>
    {
        public string Id { get; set; } = string.Empty;
        public string? NetworkPing { get; set; }
        public int? MediPayAppStatus { get; set; }
        public int? ApiAppStatus { get; set; }
        public int? AdvertisingAppStatus { get; set; }
        public int? UltraViewAppStatus { get; set; }
        public string? UsedRamPercent { get; set; }
        public string? UsedCpuPercent { get; set; }
        public string? Uptime { get; set; }
    }

    public class GetCurrentVersionHandler(
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService,
        IHisServiceHelper hisServiceHelper,
        ICachedService cachedService)
        : IRequestHandler<GetCurrentVersion, BaseCommandResultWithData<KioskInfoDto>>
    {
        public async Task<BaseCommandResultWithData<KioskInfoDto>> Handle(
            GetCurrentVersion request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<KioskInfoDto>();

            if (string.IsNullOrEmpty(request.Id) || string.IsNullOrEmpty(currentHospitalService.CurrentHospital?.HospitalId))
            {
                result.Set(false, KioskConstant.NotFound);
                return result;
            }

            var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
            if (hospital is null)
            {
                result.Set(false, KioskConstant.NotFound);
                return result;
            }

            var kiosk = await hisServiceHelper.GetKiosk(request.Id, databaseService, cancellationToken);
            if (kiosk is null)
            {
                result.Set(false, KioskConstant.NotFound);
                return result;
            }

            var kioskVersion = DataStorageObject.GetKioskReleaseHistory(hospital.KioskVersion);
            if (kioskVersion is null)
            {
                kioskVersion = await databaseService.KioskReleaseHistories.FindAsync([hospital.KioskVersion], cancellationToken);

                if (kioskVersion is null)
                {
                    result.Set(false, KioskVersionHistoryConstant.NotFound);
                    return result;
                }

                DataStorageObject.AddOrUpdateKioskReleaseHistory(kioskVersion);
            }

            if(request.MediPayAppStatus.HasValue)
            {
                var kioskLog = new KioskStatusLog
                {
                    KioskId = kiosk.Id,
                    NetworkPing = request.NetworkPing,
                    MediPayAppStatus = request.MediPayAppStatus,
                    ApiAppStatus = request.ApiAppStatus,
                    AdvertisingAppStatus = request.AdvertisingAppStatus,
                    UltraViewAppStatus = request.UltraViewAppStatus,
                    UsedRamPercent = request.UsedRamPercent,
                    UsedCpuPercent = request.UsedCpuPercent,
                    Uptime = request.Uptime,
                    LogDate = DateTimeHelper.GetCurrentLocalDateTime(),
                    CreatedAt = DateTime.UtcNow
                };
                await databaseService.KioskStatusLogs.AddAsync(kioskLog, cancellationToken);
                await databaseService.SaveChangesAsync(cancellationToken);
                await cachedService.SetAsync($"Kiosk_{kiosk.Id}", kioskLog, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }
            
            var data = new KioskInfoDto
            {
                KioskVersion = hospital.KioskVersion,
                AutoUpdate = kiosk.IsAutoUpdate,
                IsAutoOpenUltraViewer = kiosk.IsAutoOpenUltraViewer,
                UpdateAuthenConfig = Environment.GetEnvironmentVariable("UpdateAuthen_Config") ?? string.Empty,
                DownloadUrl = kioskVersion.DownloadUrl,
                PasswordExtract = kioskVersion.PasswordExtract,
                Checksum = kiosk.Checksum
            };

            result.Set(true, KioskConstant.Ok, data);
            return result;
        }
    }
}
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.KioskLogic.Dto;
using MediTrack.Application.Features.KioskMetadataLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.KioskLogic.Queries
{
    public class GetTurnOffFeatureKiosk : IRequest<BaseCommandResultWithData<List<KioskFeatureDto>>> { }

    public class GetTurnOffFeatureKioskHandler(
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService)
    : IRequestHandler<GetTurnOffFeatureKiosk, BaseCommandResultWithData<List<KioskFeatureDto>>>
    {
        public async Task<BaseCommandResultWithData<List<KioskFeatureDto>>> Handle(GetTurnOffFeatureKiosk request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<KioskFeatureDto>>();

            try
            {
                // Return turn off infos from KioskMetaData using repository
                var kioskMetadatas = await databaseService.KioskMetaDatas
                    .Where(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId
                                && x.KioskId == currentHospitalService.KioskId
                                && x.GroupType == "kiosk_configs:features")
                    .Select(x => new KioskMetadataDto()
                    {
                        HospitalId = x.HospitalId,
                        KioskId = x.KioskId,
                        GroupType = x.GroupType,
                        GroupTypeDesc = x.GroupTypeDesc,
                        Code = x.Code,
                        Title = x.Title,
                        Value = x.Value
                    })
                    .ToListAsync(cancellationToken);

                if (kioskMetadatas.Count == 0)
                {
                    result.Set(true, KioskConstant.Ok, []);
                    return result;
                }

                var turnOffInfoDtos = kioskMetadatas.Select(x =>
                {
                    var value = JsonConvert.DeserializeObject<Feature>(x.Value ?? string.Empty) ?? new();
                    return value;

                }).ToList();
                var data = MapToKioskFeaturesParallel(turnOffInfoDtos);
                result.Set(true, KioskConstant.Ok, data);
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
                Log.Error(ex, "Error in GetFeatureKioskHandler: {Message}", ex.Message);
            }

            return result;
        }
        // chuyển đổi danh sách theo ngày
        private static List<KioskFeatureDto> MapToKioskFeaturesParallel(List<Feature> features) =>
                Enumerable.Range(0, 7)
                .AsParallel()
                .Select(day => new KioskFeatureDto
                {
                    DayOfWeek = day,
                    Features = features.Select(feature => new FeatureDto
                    {
                        FeatureId = feature.FeatureId,
                        FeatureName = feature.FeatureName,
                        Active = feature.OpenDates
                            .FirstOrDefault(od => DayOfWeekMapping.GetValueOrDefault(od.DayOfWeek) == day)
                            ?.Active ?? false,
                        OpenTimes = feature.OpenDates
                            .FirstOrDefault(od => DayOfWeekMapping.GetValueOrDefault(od.DayOfWeek) == day)
                            ?.OpenTimes ?? []
                    })
                    .Where(f => f.Active) // chỉ lấy những feature có Active = true (cờ ẩn)
                    .ToList()
                }).ToList();

        private static readonly Dictionary<string, int> DayOfWeekMapping = new()
            {
                {"Thứ 02", 1}, {"Thứ 03", 2}, {"Thứ 04", 3},
                {"Thứ 05", 4}, {"Thứ 06", 5}, {"Thứ bảy", 6},
                {"Chủ nhật", 0}
            };
    }



}
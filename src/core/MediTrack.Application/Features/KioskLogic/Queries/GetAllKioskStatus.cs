using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.KioskLogic.Dto;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.KioskLogic.Queries
{
    public class GetAllKioskStatus : IRequest<BaseCommandResultWithData<List<KioskStatusDto>>>
    {
    }

    public class GetAllKioskStatusHandler(
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService,
        ICachedService cachedService,
        IHisServiceHelper hisServiceHelper)
        : IRequestHandler<GetAllKioskStatus, BaseCommandResultWithData<List<KioskStatusDto>>>
    {
        public async Task<BaseCommandResultWithData<List<KioskStatusDto>>> Handle(
            GetAllKioskStatus request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<KioskStatusDto>>();

            var kiosks = await hisServiceHelper.GetKiosks(currentHospitalService.CurrentHospital.HospitalId, databaseService);

            var kioskStatuses = new List<KioskStatusDto>();
            if (kiosks != null)
                foreach (var kiosk in kiosks)
                {
                    var cacheKey = $"Kiosk_{kiosk.Id}";
                    var kioskLog = await cachedService.GetAsync<KioskStatusLog>(cacheKey, cancellationToken);
                    if (kioskLog == null)
                    {
                        kioskStatuses.Add(new KioskStatusDto
                        {
                            Id = kiosk.Id,
                            NetworkPing = null!,
                            MediPayAppStatus = null!,
                            ApiAppStatus = null!,
                            AdvertisingAppStatus = null!,
                            UltraViewAppStatus = null!,
                            UsedRamPercent = null!,
                            UsedCpuPercent = null!,
                            Uptime = null!
                        });
                    }
                    else
                    {
                        kioskStatuses.Add(new KioskStatusDto
                        {
                            Id = kiosk.Id,
                            NetworkPing = kioskLog.NetworkPing!,
                            MediPayAppStatus = kioskLog.MediPayAppStatus!,
                            ApiAppStatus = kioskLog.ApiAppStatus!,
                            AdvertisingAppStatus = kioskLog.AdvertisingAppStatus!,
                            UltraViewAppStatus = kioskLog.UltraViewAppStatus!,
                            UsedRamPercent = kioskLog.UsedRamPercent!,
                            UsedCpuPercent = kioskLog.UsedCpuPercent!,
                            Uptime = kioskLog.Uptime!
                        });
                    }
                }

            result.Set(true, KioskConstant.Ok, kioskStatuses);
            return result;
        }
    }
}
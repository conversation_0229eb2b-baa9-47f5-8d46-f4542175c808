﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.KioskLogic.Queries
{
    public class GetKiosk : IRequest<BaseCommandResultWithData<KioskDto>>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class GetKioskHandler(IDatabaseService databaseService, IHisServiceHelper hisServiceHelper)
        : IRequestHandler<GetKiosk, BaseCommandResultWithData<KioskDto>>
    {
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<KioskDto>> Handle(
            GetKiosk request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<KioskDto>();

            var kiosk = await hisServiceHelper.GetKiosk(request.Id, databaseService, cancellationToken);
            if (kiosk == null)
            {
                result.Set(false, KioskConstant.NotFound);
                return result;
            }

            var hospital = await hisServiceHelper.GetHospital(kiosk.HospitalId ?? string.Empty, databaseService, cancellationToken);
            if (hospital is not null)
            {
                kiosk.IsGenQueueNumber ??= hospital.IsGenQueueNumberDefault;
                kiosk.IsPaymentDocument ??= hospital.IsPaymentDocumentDefault;
                kiosk.IsRegisterDocument ??= hospital.IsRegisterDocumentDefault;
                kiosk.IsSupportInsurance ??= hospital.IsSupportInsuranceDefault;
                kiosk.IsRePrintRegisterDocument ??= hospital.IsRePrintRegisterDocumentDefault;
                kiosk.IsReExamination ??= hospital.IsReExaminationDefault;
                kiosk.IsReExaminationByDoctor ??= hospital.IsReExaminationByDoctorDefault;
                kiosk.IsRegisterSelf ??= hospital.IsRegisterSelfDefault;
                kiosk.IsRegisterRelative ??= hospital.IsRegisterRelativeDefault;
                kiosk.IsSupportVNeID ??= hospital.IsSupportVNeIDDefault;
                kiosk.IsParaclinicalExamination ??= hospital.IsParaclinicalExaminationDefault;
                kiosk.IsSupportInsuranceInServiceScreen ??= hospital.IsSupportInsuranceInServiceScreenDefault;
                kiosk.IsAdvancePayment ??= hospital.IsAdvancePayment;
                kiosk.IsInsuranceAdvancePayment ??= hospital.IsInsuranceAdvancePayment;
                kiosk.IsAppRegisterCheckInKiosk ??= hospital.IsAppRegisterCheckInKioskDefault;
                kiosk.IsHideQrCodePayment ??= hospital.IsHideQrCodePaymentDefault;
                kiosk.IsHealthCheckRegister ??= hospital.IsHealthCheckRegisterDefault;
                kiosk.IsAllowSelectMultipleServices ??= hospital.IsAllowSelectMultipleServicesDefault;
                kiosk.IsAllowPrintManual ??= hospital.IsAllowPrintManualDefault;
                kiosk.IsAllowShowHospitalMap ??= hospital.IsAllowShowHospitalMapDefault;
                kiosk.IsAllowSearchHealthService ??= hospital.IsAllowSearchHealthServiceDefault;
                if (string.IsNullOrEmpty(kiosk.Announcement))
                {
                    kiosk.Announcement = hospital.Announcement;
                }
            }

            var kioskDto = new KioskDto
            {
                Id = kiosk.Id,
                Name = kiosk.Name,
                HospitalId = kiosk.HospitalId,
                IsGenQueueNumber = kiosk.IsGenQueueNumber,
                IsPaymentDocument = kiosk.IsPaymentDocument,
                IsRegisterDocument = kiosk.IsRegisterDocument,
                IsSupportInsurance = kiosk.IsSupportInsurance,
                IsRePrintRegisterDocument = kiosk.IsRePrintRegisterDocument,
                IsReExamination = kiosk.IsReExamination,
                IsReExaminationByDoctor = kiosk.IsReExaminationByDoctor,
                IsRegisterSelf = kiosk.IsRegisterSelf,
                IsRegisterRelative = kiosk.IsRegisterRelative,
                IsSupportVNeID = kiosk.IsSupportVNeID,
                IsParaclinicalExamination = kiosk.IsParaclinicalExamination,
                IsAutoUpdate = kiosk.IsAutoUpdate,
                IsSupportInsuranceInServiceScreen = kiosk.IsSupportInsuranceInServiceScreen,
                IsAdvancePayment = kiosk.IsAdvancePayment,
                IsInsuranceAdvancePayment = kiosk.IsInsuranceAdvancePayment,
                Checksum = kiosk.Checksum,
                Announcement = kiosk.Announcement,
                IsAppRegisterCheckInKiosk = kiosk.IsAppRegisterCheckInKiosk,
                IsHideQrCodePayment = kiosk.IsHideQrCodePayment,
                IsHealthCheckRegister = kiosk.IsHealthCheckRegister,
                IsAllowSelectMultipleServices = kiosk.IsAllowSelectMultipleServices,
                IsAllowPrintManual = kiosk.IsAllowPrintManual,
                IsAllowShowHospitalMap = kiosk.IsAllowShowHospitalMap,
                IsAllowSearchHealthService = kiosk.IsAllowSearchHealthService,
            };

            result.Set(true, KioskConstant.Ok, kioskDto);
            return result;
        }
    }
}

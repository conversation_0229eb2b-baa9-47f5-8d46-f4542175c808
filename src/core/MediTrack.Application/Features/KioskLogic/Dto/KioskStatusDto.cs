namespace MediTrack.Application.Features.KioskLogic.Dto
{
    public class KioskStatusDto
    {
        public string Id { get; set; } = string.Empty;
        /// <summary>
        /// Tốc độ mạng
        /// </summary>
        public string? NetworkPing { get; set; } = string.Empty;
        /// <summary>
        /// Trạng thái bật/tắt app MediPay
        /// </summary>
        public int? MediPayAppStatus { get; set; } = 0;
        /// <summary>
        /// Trạng thái bật/tắt API local
        /// </summary>
        public int? ApiAppStatus { get; set; } = 0;
        /// <summary>
        /// Trạng thái bật/tắt app Quảng cáo
        /// </summary>
        public int? AdvertisingAppStatus { get; set; } = 0;
        /// <summary>
        /// Trạng thái bật/tắt app Ultraview
        /// </summary>
        public int? UltraViewAppStatus { get; set; } = 0;
        /// <summary>
        /// % Ram
        /// </summary>
        public string? UsedRamPercent { get; set; } = string.Empty;
        /// <summary>
        /// % CPU
        /// </summary>
        public string? UsedCpuPercent { get; set; } = string.Empty;
        /// <summary>
        /// Số giờ Uptime kiosk
        /// </summary>
        public string? Uptime { get; set; } = string.Empty;
    }
}
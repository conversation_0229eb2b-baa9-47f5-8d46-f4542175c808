using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.KioskLogic.Dto
{
    public class KioskFeatureDto
    {
        public int DayOfWeek { get; set; } // 0: Sunday, 1: Monday, ..., 6: Saturday
        public List<FeatureDto> Features { get; set; } = new();
    }

    public class Feature
    {
        public string FeatureId { get; set; } = string.Empty;
        public string FeatureName { get; set; } = string.Empty;
        public bool Active { get; set; }
        public bool RealTimeActive { get; set; }
        public List<OpenDate> OpenDates { get; set; } = [];
    }

    public class OpenDate
    {
        public bool Active { get; set; }
        public string DayOfWeek { get; set; } = string.Empty;
        public List<OpenTime> OpenTimes { get; set; } = [];
    }

    public class OpenTime
    {
        public bool IsDisable { get; set; }
        public bool Active { get; set; }
        public string TimeStart { get; set; } = string.Empty;
        public string TimeEnd { get; set; } = string.Empty;
    }

    public class FeatureDto
    {
        public string FeatureId { get; set; } = string.Empty;
        public string FeatureName { get; set; } = string.Empty;
        public bool Active { get; set; }
        public List<OpenTime> OpenTimes { get; set; } = [];
    }
}
namespace MediTrack.Application.Features.KioskLogic.Dto
{
    public class KioskInfoDto
    {
        public string KioskVersion { get; set; } = string.Empty;
        public bool AutoUpdate { get; set; } = false;
        public bool IsAutoOpenUltraViewer { get; set; } = false;
        public string? UpdateAuthenConfig { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string PasswordExtract { get; set; } = string.Empty;
        public string Checksum { get; set; } = string.Empty;
    }
}
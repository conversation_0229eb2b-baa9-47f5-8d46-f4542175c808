namespace MediTrack.Application.Features.KioskLogic
{
    public class KioskDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? HospitalId { get; set; } = string.Empty;
        public bool? IsGenQueueNumber { get; set; } = false;
        public bool? IsPaymentDocument { get; set; } = false;
        public bool? IsRegisterDocument { get; set; } = false;
        public bool? IsSupportInsurance { get; set; } = false;
        public bool? IsRePrintRegisterDocument { get; set; } = false;
        public bool? IsSupportInsuranceInServiceScreen { get; set; } = false;
        public bool? IsReExamination { get; set; } = false;
        public bool? IsReExaminationByDoctor { get; set; } = false;
        public bool? IsRegisterSelf { get; set; } = true;
        public bool? IsRegisterRelative { get; set; } = false;
        public bool? IsSupportVNeID { get; set; } = false;
        public bool? IsParaclinicalExamination { get; set; } = false;
        public bool IsAutoUpdate { get; set; } = false;
        public bool? IsAdvancePayment { get; set; } = false;
        public bool? IsInsuranceAdvancePayment { get; set; } = false;
        public string Checksum { get; set; } = string.Empty;
        public string? Announcement { get; set; } = string.Empty;
        public bool? IsAppRegisterCheckInKiosk { get; set; } = false;
        public bool? IsHideQrCodePayment { get; set; } = false;
        public bool? IsHealthCheckRegister { get; set; } = false;
        public bool? IsAllowSelectMultipleServices { get; set; } = false;
        public bool? IsAllowPrintManual { get; set; } = false;
        public bool? IsAllowShowHospitalMap { get; set; } = false;
        public bool? IsAllowSearchHealthService { get; set; } = false;
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ChatbotLogic.Dto;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.ChatbotLogic.Queries
{
    public class GetPromptSuggestion : IRequest<BaseCommandResultWithData<List<PromptDto>>>
    {
    }

    public class GetPromptSuggestionHandler(IHospitalMetadataRepository hospitalMetadataRepository,
            ICurrentHospitalService currentHospitalService) : IRequestHandler<GetPromptSuggestion, BaseCommandResultWithData<List<PromptDto>>>
    {
        public async Task<BaseCommandResultWithData<List<PromptDto>>> Handle(GetPromptSuggestion request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<PromptDto>>();

            var hospitalMetadata = await hospitalMetadataRepository
                .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "prompt_suggestion", cancellationToken: cancellationToken);
            var promptData = JsonConvert.DeserializeObject<List<PromptDto>>(hospitalMetadata?.Value ?? string.Empty);
            result.Set(true, "Prompt suggestion retrieved successfully.", promptData ?? new List<PromptDto>());
            return result;
        }
    }
}
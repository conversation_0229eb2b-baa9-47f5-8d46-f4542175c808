using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using MediatR;
using MediTrack.Application.Bases;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.ChatbotLogic.Queries
{
    public class GetUrl : IRequest<BaseCommandResultWithData<string>>
    {
        public string device_id { get; set; } = string.Empty;
        public string username { get; set; } = string.Empty;
        public string full_name { get; set; } = string.Empty;
    }

    public class GeUrlHandler(IHttpClientFactory httpClientFactory) : IRequestHandler<GetUrl, BaseCommandResultWithData<string>>
    {
        public async Task<BaseCommandResultWithData<string>> Handle(
            GetUrl request, CancellationToken cancellationToken)
        {
            bool flag = false;
            string message;
            // Đọc khóa riêng tư từ file
            string path = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "pem", "private.key");
            string clientPrivateKey = File.ReadAllText(path);

            string data = JsonConvert.SerializeObject(request);

            // Tạo timestamp
            string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

            // Chuỗi cần ký
            var url = Environment.GetEnvironmentVariable("Chatbot_Url");
            string stringToSign = $"{timestamp}{Regex.Replace(url ?? string.Empty, @"https:\/\/[^\s/]+", "POST")}{data}";

            // Tạo chữ ký
            string signature = SignData(stringToSign, clientPrivateKey);

            // Tạo nội dung request
            var content = new StringContent(data, Encoding.UTF8, "application/json");

            // Thêm headers
            content.Headers.Add("x-timestamp", timestamp);
            content.Headers.Add("x-signature", signature);

            // Gửi request POST
            HttpResponseMessage response = await httpClientFactory.CreateClient().PostAsync(url, content, cancellationToken);

            // Đọc kết quả từ response
            if (response.IsSuccessStatusCode)
            {
                flag = true;
                string responseContent = await response.Content.ReadAsStringAsync();
                Log.Information("Chatbot: Call GetUrl Res {message}", responseContent);
                var result = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseContent) ?? [];

                // Lấy giá trị của url
                data = result["url"];
                return new BaseCommandResultWithData<string>
                {
                    Data = data,
                    Success = flag,
                    Messages = "Success"
                };
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                Log.Information("Chat: Call GetUrl Res {message}", message);
                return new BaseCommandResultWithData<string>
                {
                    Data = null,
                    Success = flag,
                    Messages = message
                };
            }
        }
        static string SignData(string data, string privateKey)
        {
            // Tải khóa riêng tư
            using (RSA rsa = RSA.Create())
            {
                rsa.ImportFromPem(privateKey.ToCharArray());

                // Chuyển chuỗi sang byte array
                byte[] dataBytes = Encoding.UTF8.GetBytes(data);

                // Ký dữ liệu bằng SHA256
                byte[] signatureBytes = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                // Chuyển chữ ký sang base64
                return Convert.ToBase64String(signatureBytes);
            }
        }
    }
}
using Newtonsoft.Json;

namespace MediTrack.Application.Features.HisLogic.Dtos
{
    public class AutoDialerQueueAddNewRequest
    {
        public int? UuTien { get; set; } = 0;
        public string? MaPhong { get; set; } = string.Empty;
        public string? TenPhong { get; set; } = string.Empty;
        public string? SoThamChieu { get; set; } = string.Empty;
        public string? NgayDangKy { get; set; } = string.Empty;
        public string? HoTen { get; set; } = string.Empty;
        public int? GioiTinh { get; set; } = 0;
        public string? NgaySinh { get; set; } = string.Empty;
        public string? SoCCCD { get; set; } = string.Empty;
        
        public string? TuGio { get; set; } = string.Empty;
        public string? DenGio { get; set; } = string.Empty;
        public string? CustomerId { get; set; } = string.Empty;
    }

    public class AutoDialerQueueAddNewResponse
    {
        public string? Id { get; set; } = string.Empty;
        public int? SoThuTu { get; set; } = 0;
        public bool <PERSON>u<PERSON>ien { get; set; }
        public DateTime? NgayDangKy { get; set; }
        public bool <PERSON><PERSON>oi { get; set; }
        public bool DaHuy { get; set; }
        public string? SoThamChieu { get; set; } = string.Empty;
        public DateTime? NgayCapSo { get; set; }
        public string? NguoiTao { get; set; } = string.Empty;
        public string? TuGio { get; set; } = string.Empty;
        public string? DenGio { get; set; } = string.Empty;
        public string? HoTen { get; set; } = string.Empty;
        public string? SoCCCD { get; set; } = string.Empty;
        public int? GioiTinh { get; set; } = 0;
        public string? NgaySinh { get; set; } = string.Empty;
        public string? MaPhong { get; set; } = string.Empty;
        public string? TenPhong { get; set; } = string.Empty;
        public string? ThongTinPhong { get; set; } = string.Empty;
        public string? PrintingJson { get; set; } = string.Empty;
        public string? DuKienKham { get; set; } = string.Empty;
        public string? NoiDung { get; set; } = string.Empty;
        public string? HangDoi { get; set; } = string.Empty;

    }
}
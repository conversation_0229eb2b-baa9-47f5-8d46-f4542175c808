namespace MediTrack.Application.Features.HisLogic.Dtos
{
    public class CurrentHospitalDto
    {
        /// <summary>
        /// Id bệnh viện hiện tại
        /// </summary>
        public string HospitalId { get; set; } = string.Empty;
        /// <summary>
        /// C<PERSON> gen số thứ tự không
        /// </summary>
        public bool IsGenQueueNumber { get; set; }
        /// <summary>
        /// true: HIS genQR, false: Medi genQR
        /// </summary>
        public bool IsGenQR { get; set; }
        /// <summary>
        /// Có bỏ qua lấy danh sách dịch vụ của HIS hay không
        /// </summary>
        public bool IsSkipGetServices { get; set; }
        /// <summary>
        /// Có bỏ qua lấy danh sách dịch vụ bảo hiểm của HIS hay không
        /// </summary>
        public bool IsSkipGetInsuranceServices { get; set; }
        /// <summary>
        /// C<PERSON> bỏ qua thanh toán bảo hiểm hay không
        /// </summary>
        public bool IsIgnoreInsurancePayment { get; set; }
        /// <summary>
        /// C<PERSON> hiển thị nghề nghiệp không (4750)
        /// </summary>
        public bool IsShowCareer { get; set; }
        /// <summary>
        /// Có hiển thị nghề nghiệp xã hội không (nghề nghiệp phía HIS)
        /// </summary>
        public bool IsShowSocialCareer { get; set; }
        /// <summary>
        /// Version Client của HIS
        /// </summary>
        public string? HisVersion { get; set; }
        /// <summary>
        /// Cờ xác định có đang bảo trì hay không
        /// </summary>
        public bool IsMaintenanceMode { get; set; } = false;
        /// <summary>
        /// Có hỗ trợ đăng kí khám bảo hiểm không
        /// </summary>
        public bool IsSupportInsurance { get; set; } = false;
        /// <summary>
        /// Có đi luồng tạm ứng không
        /// </summary>
        public bool IsAdvancePayment { get; set; } = false;
        /// <summary>
        /// Có tạm ứng bảo hiểm không
        /// </summary>
        public bool IsInsuranceAdvancePayment { get; set; } = false;
        /// <summary>
        /// His gen STT hay Medi gen STT
        /// </summary>
        public bool IsGenQueueNumberByHis { get; set; } = false;

        /// <summary>
        /// Có gen QR khi tạo phiếu đăng kí tới quầy không
        /// </summary>
        public bool IsGenQRWhenCreateRegister { get; set; } = false;

        /// <summary>
        /// Sử dụng phí phụ thu làm giá bảo hiểm
        /// </summary>
        public bool IsUseExtraFeeAsInsurancePrice { get; set; } = false;

        /// <summary>
        /// Cờ chặn khám bệnh cho bệnh nhân BHYT nếu bệnh nhân đã khám tại bệnh viện khác trong ngày
        /// </summary>
        public bool IsBlockForDuplicatedVisitInsurance { get; set; } = false;

        /// <summary>
        /// Có cho phép thử lại lần hai băng mã số bệnh nhân không
        /// </summary>
        public bool IsAllowCustomerRetryByPatientCode { get; set; }
        /// <summary>
        /// Có cho phép thử lại lần hai băng mã số BHYT không
        /// </summary>
        public bool IsAllowCustomerRetryByInsuranceCode { get; set; }
        /// <summary>
        /// Cờ ẩn thanh toán QRCode (là cờ mặc định của kiosk)
        /// </summary>
        public bool IsHideQrCodePaymentDefault { get; set; } = false;
        /// <summary>
        /// tỉ lệ matching khung mặt của bệnh viện
        /// </summary>
        public double? FaceMatchingRateAccepted { get; set; }
        public bool IsNeedAuthenticateBeforeGetQueueNumber { get; set; } = false;
        /// <summary>
        /// Có cho phép đẩy số thứ tự qua HIS không
        /// </summary>
        public bool IsAllowPushQueueInfoToHis { get; set; }
        public bool IsBlockInsuranceOnWeekend { get; set; }
        public bool IsRegisterRelativeDefault { get; set; }
        public bool IsActiveConfigExaminationTime { get; set; }
        public bool IsAllowBypassAdvancePayment { get; set; }
        /// <summary>
        /// Cờ xác định bệnh viện này ẩn popup bảo hiểm khi bảo hiểm của bệnh nhân là thông tuyến
        /// </summary>
        public bool IsInsurancePopupHiddenOnCorrectReferral { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này cho phép thanh toán bảo hiểm (luồng đăng kí mobile, check-in kiosk)
        /// </summary>
        public bool IsInsurancePaymentInAppAllowed { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này cho phép thanh toán dịch vụ tại nhà trong luồng check-in
        /// </summary>
        public bool IsServicePaymentInAppAllowed { get; set; } = false;
        /// <summary>
        /// Cờ xác định bệnh viện này đã chuyển sang luồng hành chính 2 cấp
        /// </summary>
        public bool IsTwoLevelAddress { get; set; } = false;
        public bool IsShowAccidentCode { get; set; } = false;
        public bool IsShowBloodType { get; set; } = false;
        public bool IsInputDiagnosisVisible { get; set; } = false;
        public bool IsShowChatBot { get; set; } = false;

        /// <summary>
        /// Cờ ẩn màn hình nhập số điện thoại (không phải skip màn hình)
        /// </summary>
        public bool IsHideInputPhoneNumber { get; set; } = false;

        /// <summary>
        /// Cờ xác định bệnh viện này có cho bệnh nhân lấy STT 2 lần trong ngày
        /// </summary>
        public bool IsAllowOnlyOneQueuePerDay { get; set; } = false;
        /// <summary>
        /// Cấu hình HIS
        /// </summary>
        public Dictionary<string, string>? HisConfig { get; set; }
    }
}
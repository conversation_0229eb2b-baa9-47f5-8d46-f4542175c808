namespace MediTrack.Application.Features.HisLogic.Dtos
{
    public class UpdatePaymentStatusRequest
    {
        public string MerchantId { get; set; } = string.Empty;
        public string InvoiceId { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string KioskId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal TransactionAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string TransactionDescription { get; set; } = string.Empty;
        public string PaidDescription { get; set; } = string.Empty;
        public DateTime? PaidTime { get; set; }
        public string PaymentRefDocNo { get; set; } = string.Empty;
        public string RegisterNumber { get; set; } = string.Empty;
    }
    
    public class UpdateParaclinicalPaymentStatusRequest
    {
        public string MerchantId { get; set; } = string.Empty;
        public string InvoiceId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public string KioskId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal TransactionAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public string TransactionDescription { get; set; } = string.Empty;
        public string PaidDescription { get; set; } = string.Empty;
        public DateTime? PaidTime { get; set; }
    }
    public class UpdatePaymentStatusResponse
    {
        /// <summary>
        /// Trạng thái gửi API IPN qua his
        /// </summary>
        public bool IpnResult { get; set; } = false;
        /// <summary>
        /// Phản hồi từ IPN
        /// </summary>
        public string? IpnMessage { get; set; } = string.Empty;
        /// <summary>
        /// Ipn response
        /// </summary>
        public string? IpnResponse { get; set; } = string.Empty;
    }
    public class UpdateParaclinicalPaymentStatusResponse
    {
        /// <summary>
        /// Trạng thái gửi API IPN qua his
        /// </summary>
        public bool IpnResult { get; set; } = false;
        /// <summary>
        /// Phản hồi từ IPN
        /// </summary>
        public string? IpnMessage { get; set; } = string.Empty;
        /// <summary>
        /// Ipn response
        /// </summary>
        public string? IpnResponse { get; set; } = string.Empty;
        /// <summary>
        /// Ipn response
        /// </summary>
        public string? Url { get; set; } = string.Empty;

    }
}
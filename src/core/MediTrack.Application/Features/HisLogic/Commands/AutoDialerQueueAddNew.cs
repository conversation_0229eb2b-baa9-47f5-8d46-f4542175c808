using FluentValidation;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.HisLogic.Commands
{
    public class AutoDialerQueueAddNew : IRequest<BaseCommandResultWithData<AutoDialerQueueAddNewResponse>>
    {
        public string? KioskId { get; set; } = string.Empty;
        public string? MaPhong { get; set; } = string.Empty;
        public int? UuTien { get; set; }
        public string? CustomerId { get; set; } = string.Empty;
        public string? IdDoiTuong { get; set; } = string.Empty;
        public string? TenDoiTuong { get; set; } = string.Empty;
        public string? NhomUuTien { get; set; } = string.Empty;
        public string? SoGTTT { get; set; } = string.Empty;
        public string? HoTenBN { get; set; } = string.Empty;
        public string? NgaySinh { get; set; } = string.Empty;
        public int? GioiTinh { get; set; } = 0;
        public string? DiaChiDayDu { get; set; } = string.Empty;
    }

    public class ValidateAutoDialerQueueAddNew : AbstractValidator<AutoDialerQueueAddNew>
    {
        public ValidateAutoDialerQueueAddNew()
        {
            RuleFor(x => x.UuTien).NotNull();
        }
    }

    public class AutoDialerQueueAddNewHandler(ICurrentHospitalService currentHospitalService,
        IDatabaseService databaseService,
        IHisServiceHelper hisServiceHelper,
        IMediator mediator)
        : IRequestHandler<AutoDialerQueueAddNew, BaseCommandResultWithData<AutoDialerQueueAddNewResponse>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<AutoDialerQueueAddNewResponse>> Handle(
            AutoDialerQueueAddNew request, CancellationToken cancellationToken)
        {
            var queueDate = DateTimeHelper.GetCurrentLocalDateTime().ToString("yyyyMMdd");
            (bool success, string message, ErrorTypeEnum errorType, AutoDialerQueueAddNewResponse response)
                = await hisService.AutoDialerQueueAddNew(new AutoDialerQueueAddNewRequest
                {
                    DenGio = string.Empty,
                    GioiTinh = 3,
                    HoTen = string.Empty,
                    MaPhong = request.MaPhong,
                    NgayDangKy = queueDate,
                    NgaySinh = string.Empty,
                    SoCCCD = request.SoGTTT,
                    SoThamChieu = string.Empty,
                    TuGio = string.Empty,
                    UuTien = request.UuTien ?? 0,
                    CustomerId = request.CustomerId,
                    TenPhong = request.TenDoiTuong,
                });
            if (message != "QUEUE_EXISTS")
            {
                if (currentHospitalService.CurrentHospital.IsAllowPushQueueInfoToHis && success && response.SoThuTu > 0)
                {
                    await mediator.Send(new AutoDialerQueueCall()
                    {
                        STT = (response.SoThuTu ?? 0).ToString("D4"),
                        IdDoiTuong = request.IdDoiTuong,
                        NgayLaySo = DateTime.ParseExact(queueDate, "yyyyMMdd", null).ToString("yyyy-MM-dd"),
                        TenDoiTuong = request.TenDoiTuong,
                        CustomerId = request.CustomerId,
                        IsPriority = request.UuTien == 1,
                        SoGTTT = request.SoGTTT,
                        HoTenBN = request.HoTenBN,
                        NgaySinh = request.NgaySinh,
                        GioiTinh = request.GioiTinh,
                        DiaChiDayDu = request.DiaChiDayDu
                    }, cancellationToken);
                }
                await databaseService.SaveChangesAsync(cancellationToken);
            }
            if (currentHospitalService.CurrentHospital.IsGenQueueNumber)
            {
                var hospitalMetadata = await databaseService.HospitalMetaDatas
                            .FirstOrDefaultAsync(x => x.Code == HospitalMetadataConstant.PrinterQueue
                                                   && x.HospitalId == currentHospitalService.CurrentHospital.HospitalId,
                                                   cancellationToken: cancellationToken) ?? null;
                if (hospitalMetadata is not null)
                {
                    var dataPrint = hospitalMetadata.Value!;
                    var hospital = await hisServiceHelper.GetHospital(currentHospitalService.CurrentHospital.HospitalId, databaseService, cancellationToken);
                    dataPrint = dataPrint.Replace("[UrlLogo]", hospital!.LogoUrl ?? "-"); // logo bệnh viện
                    dataPrint = dataPrint.Replace("[HospitalName]", hospital!.Name ?? "-"); // Tên bệnh viện
                    dataPrint = dataPrint.Replace("[Clinic]", "PHÒNG TIẾP NHẬN"); // Tên bệnh viện
                    dataPrint = dataPrint.Replace("[Priority]", request.UuTien == 1 ? "ƯU TIÊN" : "THƯỜNG"); // Tên bệnh viện
                    dataPrint = dataPrint.Replace("[Number]", (response.SoThuTu ?? 0).ToString()); // Tên bệnh viện
                    dataPrint = dataPrint.Replace("[PrintedAt]", DateTimeHelper.GetCurrentLocalDateTime().ToString("dd/MM/yyyy HH:mm"));
                    dataPrint = dataPrint.Replace("[TaxCode]", hospital!.TaxCode ?? "-");
                    dataPrint = dataPrint.Replace("[DeviceId]", currentHospitalService.KioskId);
                    dataPrint = dataPrint.Replace("[IdDoiTuong]", request.IdDoiTuong ?? "-");
                    dataPrint = dataPrint.Replace("[TenDoiTuong]", request.TenDoiTuong ?? "-");
                    dataPrint = dataPrint.Replace("[NhomUuTien]", request.NhomUuTien ?? "-");

                    dataPrint = dataPrint.Replace("[HisTenBenhNhan]", response.HoTen);
                    dataPrint = dataPrint.Replace("[HisNoiDung]", response.NoiDung);
                    dataPrint = dataPrint.Replace("[HisHangDoi]", response.HangDoi);
                    dataPrint = dataPrint.Replace("[HisDuKienKham]", response.DuKienKham);


                    response.PrintingJson = dataPrint;
                }
            }
            return new BaseCommandResultWithData<AutoDialerQueueAddNewResponse>()
            {
                Success = success,
                Messages = message,
                Data = response,
                ErrorType = errorType
            };
        }
    }
}

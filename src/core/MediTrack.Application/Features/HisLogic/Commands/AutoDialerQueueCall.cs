using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;

namespace MediTrack.Application.Features.HisLogic.Commands
{
    public class AutoDialerQueueCall : IRequest<BaseCommandResult>
    {
        public string? STT { get; set; } = string.Empty;
        public string? IdDoiTuong { get; set; } = string.Empty;
        public string? NgayLaySo { get; set; } = string.Empty;
        public string? TenDoiTuong { get; set; } = string.Empty;
        public string? CustomerId { get; set; }
        public bool? IsPriority { get; set; } = false;
        public string? SoGTTT { get; set; } = string.Empty;
        public string? HoTenBN { get; set; } = string.Empty;
        public string? NgaySinh { get; set; } = string.Empty;
        public int? GioiTinh { get; set; } = 0;
        public string? DiaChiDayDu { get; set; } = string.Empty;
    }

    public class AutoDialerQueueCallHandler(ICurrentHospitalService currentHospitalService, IDatabaseService databaseService)
        : IRequestHandler<AutoDialerQueueCall, BaseCommandResult>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResult> Handle(
            AutoDialerQueueCall request, CancellationToken cancellationToken)
        {
            var requestToHis = new AutoDialerQueueCallRequest
            {
                STT = request.STT,
                IdDoiTuong = request.IdDoiTuong,
                NgayLaySo = request.NgayLaySo,
                TenDoiTuong = request.TenDoiTuong,
                UuTien = request.IsPriority ?? false,
                SoGTTT = request.SoGTTT ?? string.Empty,
                HoTenBN = request.HoTenBN ?? string.Empty,
                NgaySinh = request.NgaySinh ?? string.Empty,
                GioiTinh = request.GioiTinh ?? 0,
                DiaChiDayDu = request.DiaChiDayDu ?? string.Empty
            };

            // Nếu bệnh viện cần xác thực trước khi lấy số thứ tự
            if (currentHospitalService.CurrentHospital.IsNeedAuthenticateBeforeGetQueueNumber
                && !string.IsNullOrEmpty(request.CustomerId))
            {
                // 1. Lấy thông tin customer từ DB
                var customer = await databaseService.Customers.FindAsync([request.CustomerId], cancellationToken);

                if (customer == null)
                {
                    return new BaseCommandResult()
                    {
                        Success = false,
                        Messages = "Không tìm thấy thông tin khách hàng.",
                        ErrorType = ErrorTypeEnum.HisError
                    };
                }

                requestToHis.SoGTTT = customer.IdentityNo ?? string.Empty;
                requestToHis.HoTenBN = customer.GetFullName();
                requestToHis.NgaySinh = customer.DateOfBirth.GetValueOrDefault().ToString("yyyy-MM-dd");
                requestToHis.GioiTinh = customer.Sex == "Nam" ? 1 : 2;
                requestToHis.DiaChiDayDu = customer.Address ?? string.Empty;
            }

            (bool success, string message, ErrorTypeEnum errorType, object response) =
                await hisService.AutoDialerQueueCall(requestToHis);

            return new BaseCommandResult()
            {
                Success = success,
                Messages = message,
                ErrorType = errorType
            };
        }
    }
}

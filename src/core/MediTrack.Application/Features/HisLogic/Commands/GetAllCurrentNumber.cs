using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.HisLogic.Commands
{
    public class GetAllCurrentNumber : IRequest<BaseCommandResultWithData<List<AllCurrentNumberResponse>>>
    {
        public string? MaPhong { get; set; } = string.Empty;
    }

    public class GetAllCurrentNumberHandler(ICurrentHospitalService currentHospitalService,
        IMediator mediator)
        : IRequestHandler<GetAllCurrentNumber, BaseCommandResultWithData<List<AllCurrentNumberResponse>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly IMediator mediator = mediator;

        public async Task<BaseCommandResultWithData<List<AllCurrentNumberResponse>>> Handle(
            GetAllCurrentNumber request, CancellationToken cancellationToken)
        {
            (bool success, string message, ErrorTypeEnum errorType, List<AllCurrentNumberResponse> response)
                = await hisService.GetAllCurrentNumber(request.MaPhong ?? string.Empty);

            return new BaseCommandResultWithData<List<AllCurrentNumberResponse>>()
            {
                Success = success,
                Messages = message,
                Data = response,
                ErrorType = errorType
            };
        }
    }
}

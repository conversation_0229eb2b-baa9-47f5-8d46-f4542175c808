﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.FileStorageLogic.Dtos;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.FileStorageLogic.Commands
{
    public class UpdateKioskReleaseCache : IRequest<BaseCommandResult>
    {
    }

    public class UpdateKioskReleaseCacheHandler(IDatabaseService databaseService) : IRequestHandler<UpdateKioskReleaseCache, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(
            UpdateKioskReleaseCache request, CancellationToken cancellationToken)
        {
            var kioskVersionList = await databaseService.KioskReleaseHistories.ToListAsync();
            DataStorageObject.ClearKioskReleaseHistories(kioskVersionList);
            if (kioskVersionList is null)
            {
                return new BaseCommandResult { Success = false, Messages = "KioskReleaseHistory not found" };
            }
            DataStorageObject.AddRangeKioskReleaseHistories(kioskVersionList);

            return new BaseCommandResult { Success = true };
        }
    }
}

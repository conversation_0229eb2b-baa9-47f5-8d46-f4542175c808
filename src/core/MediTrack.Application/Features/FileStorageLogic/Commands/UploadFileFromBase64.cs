﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.FileStorageLogic.Dtos;
using MediTrack.Application.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.FileStorageLogic.Commands
{
    public class UploadFileFromBase64 
        : IRequest<BaseCommandResultWithData<FileUploadResultDto>>
    {
        public string Base64 { get; set; } = string.Empty;
        public string Bucket { get; set; } = string.Empty;
    }

    public class UploadFileFromBase64Handler(IFileStorageService fileStorageService)
        : IRequestHandler<UploadFileFromBase64, BaseCommandResultWithData<FileUploadResultDto>>
    {
        private readonly IFileStorageService fileStorageService = fileStorageService;

        public async Task<BaseCommandResultWithData<FileUploadResultDto>> Handle(UploadFileFromBase64 request, CancellationToken cancellationToken)
        {
            (bool result, string message, string url) = await fileStorageService.UploadFileAsBase64Async(request.Base64,
                request.Bucket, Guid.NewGuid().ToString());

            return new BaseCommandResultWithData<FileUploadResultDto>() 
            {
                Data = new FileUploadResultDto
                {
                    Url = url
                },
                Messages = message,
                Success = result
            };
        }
    }
}

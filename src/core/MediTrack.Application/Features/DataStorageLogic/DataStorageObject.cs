using System.Collections.Concurrent;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.DataStorageLogic
{
    public static class DataStorageObject
    {
        private static ConcurrentDictionary<string, Kiosk> Kiosks { get; } = new();
        private static ConcurrentDictionary<string, Hospital> Hospitals { get; } = new();
        private static ConcurrentDictionary<string, CurrentHospitalDto> CurrentHospitals { get; } = new();
        private static ConcurrentDictionary<string, KioskReleaseHistory> KioskReleaseHistories { get; } = new();

        public static Hospital? GetHospital(string? hospitalId)
        {
            if(hospitalId == null)
            {
                return null;
            }

            Hospitals.TryGetValue(hospitalId, out Hospital? hospital);

            return hospital;
        }

        public static Kiosk? GetKiosk(string? kioskId)
        {
            if(kioskId == null)
            {
                return null;
            }

            Kiosks.TryGetValue(kioskId, out Kiosk? kiosk);

            return kiosk;
        }

        public static KioskReleaseHistory? GetKioskReleaseHistory(string? id)
        {
            if (id == null)
            {
                return null;
            }

            KioskReleaseHistories.TryGetValue(id, out KioskReleaseHistory? kioskReleaseHistory);

            return kioskReleaseHistory;
        }

        public static List<Kiosk> GetKiosks(string hospitalId)
        {
            return [.. Kiosks.Values.Where(k => k.HospitalId == hospitalId)];
        }

        public static CurrentHospitalDto? GetCurrentHospital(string hospitalId)
        {
            CurrentHospitals.TryGetValue(hospitalId, out CurrentHospitalDto? current);
            return current;
        }

        public static void AddOrUpdateHospital(Hospital hospital)
        {
            Hospitals.AddOrUpdate(hospital.Id, hospital, (key, oldValue) => hospital);
        }

        public static void AddOrUpdateKiosk(Kiosk kiosk)
        {
            Kiosks.AddOrUpdate(kiosk.Id, kiosk, (key, oldValue) => kiosk);
        }

        public static void AddOrUpdateCurrentHospital(CurrentHospitalDto currentHospital)
        {
            CurrentHospitals.AddOrUpdate(currentHospital.HospitalId, currentHospital, (key, oldValue) => currentHospital);
        }

        public static void AddOrUpdateKioskReleaseHistory(KioskReleaseHistory kioskReleaseHistory)
        {
            KioskReleaseHistories.AddOrUpdate(kioskReleaseHistory.Id, kioskReleaseHistory, (key, oldValue) => kioskReleaseHistory);
        }
        public static void ClearKioskReleaseHistories(List<KioskReleaseHistory> newKioskReleaseHistories)
        {
            KioskReleaseHistories.Clear();
        }
        public static void AddRangeKioskReleaseHistories(List<KioskReleaseHistory> newKioskReleaseHistories)
        {
            foreach (var kioskReleaseHistory in newKioskReleaseHistories)
            {
                KioskReleaseHistories.TryAdd(kioskReleaseHistory.Id, kioskReleaseHistory);
            }
        }


        public static void RemoveHospital(string hospitalId)
        {
            Hospitals.TryRemove(hospitalId, out _);
            CurrentHospitals.TryRemove(hospitalId, out _);
        }

        public static void RemoveKiosk(string kioskId)
        {
            Kiosks.TryRemove(kioskId, out _);
        }
    }
}
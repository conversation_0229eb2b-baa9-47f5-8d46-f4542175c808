﻿namespace MediTrack.Application.Features.CustomerReviewLogic.Dtos
{
    public class GetCustomerReviewDataDto
    {
        public List<GetCustomerReviewTypeDto>? ReviewTypes { get; set; }
        public List<string>? ReviewHints { get; set; }
        public List<GetCustomerReviewHospitalServiceDto>? HospitalServices { get; set; }
    }
    public class GetCustomerReviewHospitalServiceDto
    {
        public string? ServiceId { get; set; }
        public string? ServiceName { get; set; }
    }
    public class GetCustomerReviewTypeDto
    {
        public string? TypeId { get; set; }
        public string? TypeName { get; set; }
        public string? TypeUrlActive { get; set; }
        public string? TypeUrlInActive { get; set; }
    }
}
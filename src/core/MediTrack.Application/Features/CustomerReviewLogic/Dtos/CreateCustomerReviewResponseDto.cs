﻿namespace MediTrack.Application.Features.CustomerReviewLogic.Dtos
{
    public class CreateCustomerReviewResponseDto
    {
        public string Id { get; set; } = string.Empty;
        public string Problem { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public string TypeId { get; set; } = string.Empty;
        public List<string>? FlagIds { get; set; }
    }
}
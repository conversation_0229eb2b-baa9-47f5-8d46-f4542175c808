﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerReviewLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.CustomerReviewLogic.Commands
{
    public class CreateCustomerReviewCommand : IRequest<BaseCommandResultWithData<CreateCustomerReviewResponseDto>>
    {
        public string? CustomerKey { get; set; }
        public string Comment { get; set; } = string.Empty;
        public string Problem { get; set; } = string.Empty;
        public string TypeId { get; set; } = string.Empty;
        public List<string>? ServiceIds { get; set; }
    }

    public class CreateCustomerReviewCommandHandler(IDatabaseService databaseService,
    ICurrentHospitalService currentHospitalService,
    ICachedService cachedService,
    ISystemMetadataRepository systemMetadataRepository) : IRequestHandler<CreateCustomerReviewCommand, BaseCommandResultWithData<CreateCustomerReviewResponseDto>>
    {
        public async Task<BaseCommandResultWithData<CreateCustomerReviewResponseDto>> Handle(CreateCustomerReviewCommand request, CancellationToken cancellationToken)
        {
            var logPrefix = currentHospitalService.LogPrefix;
            Log.Information("{LogPrefix} Handling CreateCustomerReviewCommand", logPrefix);
            var result = new BaseCommandResultWithData<CreateCustomerReviewResponseDto>();
            var customer = await databaseService.Customers
                .FindAsync([request.CustomerKey], cancellationToken);

            List<GetCustomerReviewTypeDto>? reviewTypes = await cachedService
                .GetAsync<List<GetCustomerReviewTypeDto>>("ReviewTypes", cancellationToken);

            if (reviewTypes is null)
            {
                Log.Information("{LogPrefix} Review types not found in cache, fetching from database", logPrefix);
                var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("review_types", cancellationToken: cancellationToken);
                reviewTypes = JsonConvert.DeserializeObject<List<GetCustomerReviewTypeDto>>(systemMetaData?.Value ?? "[]") ?? [];
                await cachedService.SetAsync("ReviewTypes", reviewTypes);
            }

            if (!reviewTypes.Any(x => x.TypeId == request.TypeId))
            {
                Log.Warning("{LogPrefix} Invalid review type {TypeId}", logPrefix, request.TypeId);
                result.Set(false, CustomerReviewConstant.InvalidReviewType);
                return result;
            }

            var review = new CustomerReview
            {
                Id = IdentityHelper.Guid(10),
                Comment = request.Comment,
                Problem = request.Problem,
                TypeId = request.TypeId,
                ServiceIds = request.ServiceIds,
                CustomerId = customer?.Id,
                HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                CreatedBy = currentHospitalService.CurrentHospital.HospitalId,
            };
            await databaseService.CustomerReviews.AddAsync(review, cancellationToken);
            await databaseService.SaveChangesAsync(cancellationToken);

            result.Set(true, CustomerReviewConstant.Ok, new CreateCustomerReviewResponseDto
            {
                Id = review.Id,
                Comment = request.Comment,
                Problem = request.Problem,
                TypeId = request.TypeId,
                FlagIds = request.ServiceIds,
            });
            Log.Information("{LogPrefix} Successfully handled CreateCustomerReviewCommand", logPrefix);
            return result;
        }
    }
}
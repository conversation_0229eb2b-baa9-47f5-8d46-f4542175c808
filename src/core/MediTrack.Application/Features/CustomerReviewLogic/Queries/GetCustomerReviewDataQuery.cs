﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerReviewLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.CustomerReviewLogic.Queries
{
    public class GetCustomerReviewDataQuery : IRequest<BaseCommandResultWithData<GetCustomerReviewDataDto>>
    {
    }

    public class GetCustomerReviewDataQueryHandler(IDatabaseService databaseService,
    ICurrentHospitalService currentHospitalService,
    IHisServiceHelper hisServiceHelpers,
    ICachedService cachedService,
    ISystemMetadataRepository systemMetadataRepository) : IRequestHandler<GetCustomerReviewDataQuery, BaseCommandResultWithData<GetCustomerReviewDataDto>>
    {
        public async Task<BaseCommandResultWithData<GetCustomerReviewDataDto>> Handle(GetCustomerReviewDataQuery request, CancellationToken cancellationToken)
        {
            var logPrefix = currentHospitalService.LogPrefix;
            Log.Information("{LogPrefix} Handling GetCustomerReviewDataQuery", logPrefix);
            var result = new BaseCommandResultWithData<GetCustomerReviewDataDto>();
            var hospital = await hisServiceHelpers.GetHospital(currentHospitalService.CurrentHospital.HospitalId ?? string.Empty, databaseService, cancellationToken);

            if (hospital is null)
            {
                Log.Warning("{LogPrefix} Hospital not found", logPrefix);
                result.Set(false, CustomerConstant.NotFound);
                return result;
            }
            List<GetCustomerReviewTypeDto>? reviewTypes = await cachedService
                .GetAsync<List<GetCustomerReviewTypeDto>>("ReviewTypes", cancellationToken);

            if (reviewTypes is null)
            {
                Log.Information("{LogPrefix} Review types not found in cache, fetching from database", logPrefix);
                var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("review_types", cancellationToken: cancellationToken);
                reviewTypes = JsonConvert.DeserializeObject<List<GetCustomerReviewTypeDto>>(systemMetaData?.Value ?? "[]") ?? [];
                await cachedService.SetAsync("ReviewTypes", reviewTypes);
            }
            List<string>? reviewHints = await cachedService
                .GetAsync<List<string>>("ReviewHints", cancellationToken);

            if (reviewHints is null)
            {
                Log.Information("{LogPrefix} Review hints not found in cache, fetching from database", logPrefix);
                var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("review_hints", cancellationToken: cancellationToken);
                reviewHints = JsonConvert.DeserializeObject<List<string>>(systemMetaData?.Value ?? "[]") ?? [];
                await cachedService.SetAsync("ReviewHints", reviewHints);
            }

            var services = new List<GetCustomerReviewHospitalServiceDto>();
            if (hospital.IsRegisterDocumentDefault)
            {
                services.Add(new GetCustomerReviewHospitalServiceDto
                {
                    ServiceId = CustomerReviewConstant.RegisterFlag,
                    ServiceName = CustomerReviewConstant.RegisterFlagName,
                });
            }
            if (hospital.IsReExaminationDefault)
            {
                services.Add(new GetCustomerReviewHospitalServiceDto
                {
                    ServiceId = CustomerReviewConstant.ReExaminationFlag,
                    ServiceName = CustomerReviewConstant.ReExaminationFlagName,
                });
            }
            if (hospital.IsRegisterRelativeDefault)
            {
                services.Add(new GetCustomerReviewHospitalServiceDto
                {
                    ServiceId = CustomerReviewConstant.RegisterRelativeFlag,
                    ServiceName = CustomerReviewConstant.RegisterRelativeFlagName,
                });
            }
            if (hospital.IsGenQueueNumberDefault)
            {
                services.Add(new GetCustomerReviewHospitalServiceDto
                {
                    ServiceId = CustomerReviewConstant.QueueNumberFlag,
                    ServiceName = CustomerReviewConstant.QueueNumberFlagName,
                });
            }
            if (hospital.IsPaymentDocumentDefault)
            {
                services.Add(new GetCustomerReviewHospitalServiceDto
                {
                    ServiceId = CustomerReviewConstant.PaymentFlag,
                    ServiceName = CustomerReviewConstant.PaymentFlagName,
                });
            }
            services.Add(new GetCustomerReviewHospitalServiceDto
            {
                ServiceId = CustomerReviewConstant.OtherFlag,
                ServiceName = CustomerReviewConstant.OtherFlagName,
            });
            result.Set(true, CustomerReviewConstant.Ok, new GetCustomerReviewDataDto
            {
                ReviewTypes = reviewTypes,
                ReviewHints = reviewHints,
                HospitalServices = services
            });
            Log.Information("{LogPrefix} Successfully handled GetCustomerReviewDataQuery", logPrefix);
            return result;
        }
    }
}
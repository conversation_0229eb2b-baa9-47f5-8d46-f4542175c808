using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Serilog;

namespace MediTrack.Application.Features.HealthPackageLogic.Queries
{
    public class GetHealthPackages : IRequest<BaseCommandResultWithData<List<HealthPackage>>>
    {
        public string? HealthPackageTypeId { get; set; }
    }
    public class GetHealthPackagesHandler(ICurrentHospitalService currentHospitalService,
        ICachedService cachedService) : IRequestHandler<GetHealthPackages, BaseCommandResultWithData<List<HealthPackage>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly ICachedService cachedService = cachedService;
        public async Task<BaseCommandResultWithData<List<HealthPackage>>> Handle(GetHealthPackages request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HealthPackage>>();
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                var list = await cachedService.GetAsync<List<HealthPackage>>("HealthPackage" + currentHospitalService.CurrentHospital.HospitalId + request.HealthPackageTypeId, cancellationToken);
                if (list != null && list.Count > 0)
                {
                    result.Set(true, string.Empty, list);
                    return result;
                }

                (bool res, string message, errorType, list) = await hisService.GetHealthPackages(request.HealthPackageTypeId);
                if (!res)
                {
                    result.Set(false, message, errorType);
                    return result;
                }
                if (list != null && list.Count > 0)
                {
                    await cachedService.SetAsync("HealthPackage" + currentHospitalService.CurrentHospital.HospitalId + request.HealthPackageTypeId, list, cancellationToken, 3600);
                }
                result.Set(true, string.Empty, list ?? []);
            }
            catch (Exception ex)
            {
                result.Set(false, "Lỗi hệ thống: " + ex.Message, errorType);
                Log.Error(ex, "Lỗi khi lấy danh sách loại hình khám chữa bệnh");
            }
            return result;
        }
    }
}
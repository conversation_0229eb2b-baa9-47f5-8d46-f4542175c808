using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.HealthPackageLogic.Queries
{
    public class GetTechnicalServices : IRequest<BaseCommandResultWithData<List<TechnicalService>>>
    {
    }
    public class GetTechnicalServicesHandler(IHospitalMetadataRepository hospitalMetadataRepository,
        ICachedService cachedService,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetTechnicalServices, BaseCommandResultWithData<List<TechnicalService>>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        private readonly ICachedService cachedService = cachedService;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;
        private readonly IHisService hisService = currentHospitalService.HisService;
        public async Task<BaseCommandResultWithData<List<TechnicalService>>> Handle(GetTechnicalServices request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<TechnicalService>>();
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                var data = await cachedService.GetAsync<List<TechnicalService>>("TechnicalServices" + currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
                if (data != null)
                {
                    result.Set(true, string.Empty, data);
                    return result;
                }

                var metaData = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, groupType: "hospital_technical_services", cancellationToken: cancellationToken);
                if (metaData != null && !string.IsNullOrEmpty(metaData.Value))
                {
                    data = JsonConvert.DeserializeObject<List<TechnicalService>>(metaData.Value);
                    if (data != null && data.Count > 0)
                    {
                        await cachedService.SetAsync("TechnicalServices" + currentHospitalService.CurrentHospital.HospitalId, data, cancellationToken);
                    }
                }
                else
                {
                    (bool res, string message, errorType, data) = await hisService.GetHospitalTechnicalServices();
                    if (res && data != null && data.Count > 0)
                    {
                        await cachedService.SetAsync("TechnicalServices" + currentHospitalService.CurrentHospital.HospitalId, data, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
                    }
                }
                result.Set(true, string.Empty, data ?? []);
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message, errorType);
            }
            return result;
        }
    }
}
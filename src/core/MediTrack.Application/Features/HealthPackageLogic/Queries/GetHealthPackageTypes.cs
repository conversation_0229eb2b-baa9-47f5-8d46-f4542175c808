using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.HealthPackageLogic.Queries
{
    public class GetHealthPackageTypes : IRequest<BaseCommandResultWithData<List<HealthPackageType>>>
    {

    }

    public class GetHealthPackageTypesHandler(ICurrentHospitalService currentHospitalService,
        IHospitalMetadataRepository hospitalMetadataRepository,
        ICachedService cachedService) : IRequestHandler<GetHealthPackageTypes, BaseCommandResultWithData<List<HealthPackageType>>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        private readonly ICachedService cachedService = cachedService;

        public async Task<BaseCommandResultWithData<List<HealthPackageType>>> Handle(GetHealthPackageTypes request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HealthPackageType>>();
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                var list = await cachedService.GetAsync<List<HealthPackageType>>("HealthPackageTypes" + currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
                if (list != null && list.Count > 0)
                {
                    result.Set(true, string.Empty, list);
                    return result;
                }

                var hospitalMetadata = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync
                    (currentHospitalService.CurrentHospital.HospitalId, "health_package_types_default", cancellationToken: cancellationToken);

                if (hospitalMetadata != null && !string.IsNullOrEmpty(hospitalMetadata.Value))
                {
                    list = JsonConvert.DeserializeObject<List<HealthPackageType>>(hospitalMetadata.Value);
                    if (list != null && list.Count > 0)
                    {
                        await cachedService.SetAsync("HealthPackageTypes" + currentHospitalService.CurrentHospital.HospitalId, list, cancellationToken);
                    }
                }
                else
                {
                    (bool res, string message, errorType, list) = await hisService.GetHealthPackageTypes();
                    if (res && list != null && list.Count > 0)
                    {
                        await cachedService.SetAsync("HealthPackageTypes" + currentHospitalService.CurrentHospital.HospitalId, list, cancellationToken);
                    }
                }
                result.Set(true, string.Empty, list ?? []);
            }
            catch (Exception ex)
            {
                result.Set(false, "Lỗi hệ thống: " + ex.Message, errorType);
                Log.Error(ex, "Lỗi khi lấy danh sách loại hình khám chữa bệnh");
            }
            return result;
        }
    }
}
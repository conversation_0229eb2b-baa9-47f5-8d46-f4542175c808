using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;

namespace MediTrack.Application.Features.HealthPackageLogic.Queries
{
    public class GetHealthPackageFilter : IRequest<BaseCommandResultWithData<List<string>>>
    {
        /// <summary>
        /// Filter code: tu<PERSON>i (age), giới tính (sex), lư<PERSON>t dùng (usage)
        /// </summary>
        public string code { get; set; } = string.Empty;
    }
    public class GetHealthPackageFilterHandler(IHospitalMetadataRepository hospitalMetadataRepository,
        ICachedService cachedService,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetHealthPackageFilter, BaseCommandResultWithData<List<string>>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        private readonly ICurrentHospitalService currentHospitalService = currentHospitalService;
        public async Task<BaseCommandResultWithData<List<string>>> Handle(GetHealthPackageFilter request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<string>>();
            try
            {
                var data = await cachedService.GetAsync<List<string>>("health_package_filter" + request.code + currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
                if (data != null)
                {
                    result.Set(true, string.Empty, data);
                    return result;
                }

                var filter = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, groupType: "health_package_filter", code: request.code);
                var list = filter?.Value?.Split(',').ToList();
                list ??= request.code switch
                {
                    "Age" => FilterConstant.AgeFilter,
                    "Sex" => FilterConstant.GenderFilter,
                    "Usage" => FilterConstant.UsageFilter,
                    _ => []
                };

                if (list == null || list.Count == 0)
                {
                    result.Set(false, "Không tìm thấy filter");
                    return result;
                }
                await cachedService.SetAsync("health_package_filter" + request.code + currentHospitalService.CurrentHospital.HospitalId, list, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
                result.Set(true, string.Empty, list);
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
            }
            return result;
        }
    }
}
﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.UserLogic.Commands
{
    public class AuthUser : IRequest<BaseCommandResultWithData<User>>
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class AuthUserHandler(UserManager<User> userManager, RoleManager<Role> roleManager)
        : IRequestHandler<AuthUser, BaseCommandResultWithData<User>>
    {
        private readonly UserManager<User> userManager = userManager;
        private readonly RoleManager<Role> roleManager = roleManager;

        public async Task<BaseCommandResultWithData<User>> Handle(
            AuthUser request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<User>();

            var user = await userManager.FindByNameAsync(request.UserName);
            user ??= await userManager.FindByEmailAsync(request.UserName);
            user ??= await userManager.Users
                .FirstOrDefaultAsync(u => u.PhoneNumber == request.UserName, cancellationToken);

            if (user != null &&
                user.IsActive == true)
            {
                var isValidPassword = await userManager.CheckPasswordAsync(user, request.Password);

                if (isValidPassword)
                {
                    // Get role names for the user
                    var roleNames = await userManager.GetRolesAsync(user);
                    
                    // Get Role objects based on role names
                    var roles = new List<Role>();
                    foreach (var roleName in roleNames)
                    {
                        var role = await roleManager.FindByNameAsync(roleName);
                        if (role != null)
                        {
                            roles.Add(role);
                        }
                    }
                    
                    // Populate the Roles property
                    user.Roles = roles;
                    
                    result.Set(true, UserConstant.AuthSuccess, user);
                }
                else
                {
                    result.Messages = UserConstant.InvalidPassword;
                }
            }
            else
            {
                result.Messages = UserConstant.InvalidUserName;
            }

            return result;
        }
    }
}

﻿using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Ultils.Helpers;

namespace MediTrack.Application.Features.CustomerLogic.Helpers
{
    public static class ApplicationCustomerHelper
    {
        /// <summary>
        /// Extracts the location information from the address string and matches it with the provided provinces, districts, and wards.
        /// Run 3 level address parsing.
        /// </summary>
        /// <param name="address"></param>
        /// <param name="provinces"></param>
        /// <param name="districts"></param>
        /// <param name="wards"></param>
        /// <returns></returns>
        public static (string? province, string? district, string? ward, string? street, bool isContainWard) GetLocation(
            string? address,
            List<ProvinceDto> provinces,
            List<DistrictDto> districts,
            List<WardDto> wards)
        {
            if (string.IsNullOrEmpty(address))
            {
                return (string.Empty, string.Empty, string.Empty, string.Empty, true);
            }

            var splitAddress = address.Split(",").Select(x => x.Trim()).ToList();

            var provinceNames = provinces.Where(x => !string.IsNullOrEmpty(x.OtherName))
                .Select(x => x.OtherName)
                .ToList();
            var provinceSearch = splitAddress[^1] ?? string.Empty;

            string province = provinceNames.FindMatchedString(StringHelper.RemoveAddressPrefix(provinceSearch));

            if (string.IsNullOrEmpty(province))
            {
                province = provinceNames.FindClosestString(provinceSearch);
            }

            // If the province is not found, return the address as the street
            if (string.IsNullOrEmpty(province))
            {
                return (string.Empty, string.Empty, string.Empty, address, true);
            }

            string? provinceId = provinces.Find(x => x.OtherName == province)?.Id;

            // If the address has only the province, return the province
            if (splitAddress.Count < 2)
            {
                return (provinceId, string.Empty, string.Empty, string.Empty, true);
            }

            var districtNames = districts
                    .Where(x => x.ProvinceId == provinceId && !string.IsNullOrEmpty(x.Name))
                    .Select(x => x.Name)
                    .ToList();
            var districtSearch = StringHelper.NormalizeNumberInAddress(splitAddress[^2] ?? string.Empty);
            var district = districtNames.FindMatchedString(districtSearch);

            if (string.IsNullOrEmpty(district))
            {
                districtNames = [.. districts
                        .Where(x => x.ProvinceId == provinceId && !string.IsNullOrEmpty(x.OtherName))
                        .Select(x => x.OtherName ?? string.Empty)];
                district = districtNames.FindMatchedString(StringHelper.RemoveAddressPrefix(districtSearch));
            }

            // If the address has only the province and street, return the province and street
            if (string.IsNullOrEmpty(district))
            {
                return (provinceId, string.Empty, string.Empty, districtSearch, true);
            }

            var districtId = districts.Find(x => (x.Name == district || x.OtherName == district) && x.ProvinceId == provinceId)?.Id
                ?? string.Empty;

            // If the address has only the province and district, return the province and district
            if (splitAddress.Count < 3)
            {
                return (provinceId, districtId, string.Empty, string.Empty, true);
            }

            var wardNames = wards.Where(x => x.ProvinceId == provinceId && x.DistrictId == districtId)
                .Select(x => x.Name)
                .ToList();
            var searchWard = StringHelper.NormalizeNumberInAddress(splitAddress[^3] ?? string.Empty);
            var ward = wardNames.FindMatchedString(searchWard);

            if (string.IsNullOrEmpty(ward))
            {
                wardNames = [.. wards.Where(x => x.ProvinceId == provinceId && x.DistrictId == districtId).Select(x => x.OtherName ?? string.Empty)];
                ward = wardNames.FindMatchedString(StringHelper.RemoveAddressPrefix(searchWard));
            }

            // If the address has only the province, district and street, return the province, district and street
            if (string.IsNullOrEmpty(ward))
            {
                return (provinceId, districtId, string.Empty, searchWard, wardNames.Count > 0);
            }

            string? wardId = wards.Find(x => (x.Name == ward || x.OtherName == ward) && x.DistrictId == districtId && x.ProvinceId == provinceId)?.Id;

            if (splitAddress.Count < 4)
            {
                return (provinceId, districtId, wardId, string.Empty, wardNames.Count > 0);
            }

            string street = string.Join(", ", splitAddress.Take(splitAddress.Count - 3));
            return (provinceId, districtId, wardId, street, wardNames.Count > 0);
        }

        public static (ProvinceDto? province, DistrictDto? district, WardDto? ward, string? street, bool isContainWard) GetLocationDto(
            string? address,
            List<ProvinceDto> provinces,
            List<DistrictDto> districts,
            List<WardDto> wards)
        {
            if (string.IsNullOrEmpty(address))
            {
                return (null, null, null, null, true);
            }

            var splitAddress = address.Split(",").Select(x => x.Trim()).ToList();

            var provinceNames = provinces.Where(x => !string.IsNullOrEmpty(x.OtherName))
                .Select(x => x.OtherName)
                .ToList();
            var provinceSearch = splitAddress[^1] ?? string.Empty;

            string province = provinceNames.FindMatchedString(StringHelper.RemoveAddressPrefix(provinceSearch));

            if (string.IsNullOrEmpty(province))
            {
                province = provinceNames.FindClosestString(provinceSearch);
            }

            // If the province is not found, return the address as the street
            if (string.IsNullOrEmpty(province))
            {
                return (null, null, null, address, true);
            }

            var provinceDto = provinces.Find(x => x.OtherName == province);
            string? provinceId = provinceDto?.Id;

            // If the address has only the province, return the province
            if (splitAddress.Count < 2)
            {
                return (provinceDto, null, null, null, true);
            }

            var districtNames = districts
                    .Where(x => x.ProvinceId == provinceId && !string.IsNullOrEmpty(x.Name))
                    .Select(x => x.Name)
                    .ToList();
            var districtSearch = StringHelper.NormalizeNumberInAddress(splitAddress[^2] ?? string.Empty);
            var district = districtNames.FindMatchedString(districtSearch);

            if (string.IsNullOrEmpty(district))
            {
                districtNames = [.. districts
                        .Where(x => x.ProvinceId == provinceId && !string.IsNullOrEmpty(x.OtherName))
                        .Select(x => x.OtherName ?? string.Empty)];
                district = districtNames.FindMatchedString(StringHelper.RemoveAddressPrefix(districtSearch));
            }

            // If the address has only the province and street, return the province and street
            if (string.IsNullOrEmpty(district))
            {
                return (provinceDto, null, null, districtSearch, true);
            }
            var districtDto = districts.Find(x => (x.Name == district || x.OtherName == district) && x.ProvinceId == provinceId);
            var districtId = districtDto?.Id
                ?? string.Empty;

            // If the address has only the province and district, return the province and district
            if (splitAddress.Count < 3)
            {
                return (provinceDto, districtDto, null, null, true);
            }

            var wardNames = wards.Where(x => x.ProvinceId == provinceId && x.DistrictId == districtId)
                .Select(x => x.Name)
                .ToList();
            var searchWard = StringHelper.NormalizeNumberInAddress(splitAddress[^3] ?? string.Empty);
            var ward = wardNames.FindMatchedString(searchWard);

            if (string.IsNullOrEmpty(ward))
            {
                wardNames = [.. wards.Where(x => x.ProvinceId == provinceId && x.DistrictId == districtId).Select(x => x.OtherName ?? string.Empty)];
                ward = wardNames.FindMatchedString(StringHelper.RemoveAddressPrefix(searchWard));
            }

            // If the address has only the province, district and street, return the province, district and street
            if (string.IsNullOrEmpty(ward))
            {
                return (provinceDto, districtDto, null, searchWard, wardNames.Count > 0);
            }

            var wardDto = wards.Find(x => (x.Name == ward || x.OtherName == ward) && x.DistrictId == districtId && x.ProvinceId == provinceId);

            if (splitAddress.Count < 4)
            {
                return (provinceDto, districtDto, wardDto, string.Empty, wardNames.Count > 0);
            }

            string street = string.Join(", ", splitAddress.Take(splitAddress.Count - 3));
            return (provinceDto, districtDto, wardDto, street, wardNames.Count > 0);
        }
    }
}

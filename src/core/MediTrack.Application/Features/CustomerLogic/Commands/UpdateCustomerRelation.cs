﻿using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Helpers;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.CustomerLogic.Commands
{
    public class UpdateCustomerRelation : CustomerDto, IRequest<BaseCommandResult>
    {
        public string Id { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
        public string RelationId { get; set; } = string.Empty;
        public bool IsScanCccd { get; set; }
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class UpdateCustomerRelationHandler(ICurrentUserService currentUserService,
        ICurrentHospitalService currentHospitalService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IDatabaseService databaseService,
        IHospitalMetadataRepository hospitalMetadata) : IRequestHandler<UpdateCustomerRelation, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(
            UpdateCustomerRelation request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            try
            {
                //log request
                Log.Information("{LogPrefix} Request UpdateCustomerRelation: {@request}", currentHospitalService.LogPrefix, request);

                var customer = await databaseService.Customers.FindAsync([request.Id], cancellationToken);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.NotFound);
                    return result;
                }

                //1. Map customer information chỉ dựa vào AccountStatus
                if (customer.AccountStatus == "S01")
                {
                    // Cho phép cập nhật các trường nhạy cảm khi là S01
                    customer.DateOfBirth = request.DateOfBirth;
                    customer.Sex = request.Sex;
                    // Cập nhật số căn cước nếu khác
                    if (!string.IsNullOrEmpty(request.IdentityNo) && request.IdentityNo != customer.IdentityNo)
                    {
                        var existingIdIdentityNo = await databaseService.Customers.AnyAsync(
                            x => x.IdentityNo == request.IdentityNo && x.Id != customer.Id,
                            cancellationToken);
                        if (existingIdIdentityNo)
                        {
                            result.Set(false, "Số CMND/CCCD đã tồn tại", ErrorTypeEnum.MediPayError);
                            return result;
                        }
                        customer.IdentityNo = request.IdentityNo;
                    }
                    // Cập nhật tên
                    (string firstName, string lastName) = CustomerHelper.GetVietnameseNameParts(request.FullName ?? string.Empty);
                    customer.FirstName = firstName;
                    customer.LastName = lastName;
                    // Nếu scan CCCD thì chuyển trạng thái sang S1
                    if (request.IsScanCccd)
                    {
                        customer.AccountStatus = "S1";
                    }
                }
                // Nếu khác S01 thì không update các trường trên
                customer.SetUpdate(currentUserService.UserName);

                customer.Phone = request.Phone;

                string? provinceId = request.ProvinceId;
                string? districtId = request.DistrictId;
                string? wardId = request.WardId;
                string? street = request.Street;
                string address = string.Empty;
                bool isContainWard = false;

                //Bệnh viện đi theo luồng địa chỉ 2 cấp
                var isHospitalTwoLevelAddress = currentHospitalService.CurrentHospital.IsTwoLevelAddress;

                // Nếu bệnh viện không đi theo luồng địa chỉ 2 cấp thì người dùng không thể gửi địa chỉ 2 cấp
                if (!isHospitalTwoLevelAddress)
                {
                    request.IsTwoLevelAddress = false;
                }

                // Nếu người dùng không gửi thông tin địa chỉ 2 cấp thì mặc định là false
                var isInputTwoLevelAddress = request.IsTwoLevelAddress ?? false;

                //2. Prepare data
                var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);

                var districts = await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId ?? string.Empty, cancellationToken);

                // Nếu bệnh nhân nhập địa chỉ 2 cấp thì lấy danh sách quận huyện theo tỉnh
                var wardParentId = isInputTwoLevelAddress ? request.ProvinceId : request.DistrictId;
                var wards = await wardRepository.GetWardByParentIdAsync(wardParentId, isInputTwoLevelAddress, cancellationToken);

                //3. Nếu người dùng không gửi id tỉnh, quận huyện, phường xã thì sẽ tự động phân tích địa chỉ theo luồng 3 cấp
                if (string.IsNullOrEmpty(provinceId))
                {
                    (string? provinceIdSplit, string? districtIdSplit, string? wardIdSplit, string? streetAddress, isContainWard) =
                        ApplicationCustomerHelper.GetLocation(request.Address, provinces, districts, wards);
                    Log.Information("{LogPrefix} ProvinceId: {provinceId}, DistrictId: {districtId}, WardId: {wardId}", currentHospitalService.LogPrefix, provinceIdSplit, districtIdSplit, wardIdSplit);

                    provinceId = provinceIdSplit;
                    districtId = districtIdSplit;
                    wardId = wardIdSplit;
                    street = streetAddress;
                }
                else
                {
                    isContainWard = wards != null && wards.Count > 0;
                }

                //nếu mã tỉnh không được nhập thì trả về lỗi
                if (string.IsNullOrEmpty(provinceId))
                {
                    result.Set(false, ProvinceConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                    return result;
                }

                // kiểm tra mã tỉnh tồn tại
                var province = provinces.FirstOrDefault(p => (isInputTwoLevelAddress ? p.NewId : p.Id) == provinceId);
                if (province == null)
                {
                    result.Set(false, ProvinceConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                    return result;
                }

                provinceId = isHospitalTwoLevelAddress ? province.NewId : province.Id;
                address = isHospitalTwoLevelAddress ? province.NewName : province.Name;

                //Nếu bệnh viện đi theo luồng địa chỉ 2 cấp thì không cần kiểm tra quận huyện
                if (isHospitalTwoLevelAddress)
                {
                    districtId = string.Empty;
                }
                // Nếu theo địa chỉ 3 cấp thì kiểm tra quận huyện
                else
                {
                    if (string.IsNullOrEmpty(districtId))
                    {
                        result.Set(false, DistrictConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var district = districts.FirstOrDefault(d => d.Id == districtId);
                    if (district == null)
                    {
                        result.Set(false, DistrictConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    address = district.Name + ", " + address;
                }

                // nếu mã phường xã không được nhập thì trả về lỗi
                if (isContainWard)
                {
                    if (string.IsNullOrEmpty(wardId))
                    {
                        result.Set(false, WardConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    // kiểm tra mã phường xã tồn tại
                    var ward = wards!.FirstOrDefault(w => (isInputTwoLevelAddress ? w.NewId : w.Id) == wardId);
                    if (ward == null)
                    {
                        result.Set(false, WardConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    wardId = isHospitalTwoLevelAddress ? ward.NewId : ward.Id;
                    address = (isHospitalTwoLevelAddress ? ward.NewName : ward.Name) + ", " + address;
                }

                if (!string.IsNullOrEmpty(street))
                {
                    address = street + ", " + address;
                }

                customer.IsSyncedTwoLevelAddress = isHospitalTwoLevelAddress;
                customer.ProvinceId = provinceId;
                customer.DistrictId = districtId;
                customer.WardId = wardId;
                customer.Street = street;
                customer.Nation = string.IsNullOrEmpty(request.Nation) ? "000" : request.Nation;
                customer.NationalityId = request.Nationality;
                customer.Address = address;
                customer.IdentityIssueDate = request.IdentityIssueDate;
                customer.IdentityIssuePlace = request.IdentityIssuePlace;
                customer.HealthInsuranceNo = request.HealthInsuranceNo;

                customer.BankTranId = request.BankTranId;

                customer.HospitalId = currentHospitalService.CurrentHospital.HospitalId;
                customer.EIDTransactionId = request.EIDTransactionId;
                customer.FaceMatchingTransactionId = request.FaceMatchingTransactionId;
                customer.LivenessTransactionId = request.LivenessTransactionId;
                customer.RegistrationType = request.RegistrationType;

                databaseService.Customers.Update(customer);

                //4. Map relationship
                Relationship? originReplationship = null;
                var metadata = await hospitalMetadata.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "id_quan_he_nt_map", cancellationToken: cancellationToken);
                if (metadata != null && !string.IsNullOrEmpty(metadata.Value))
                {
                    var listRelationship = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(metadata.Value);
                    string? originReplationshipId = listRelationship?.FirstOrDefault(x => x.Id == request.RelationId)?.OriginId;
                    if (string.IsNullOrEmpty(originReplationshipId))
                    {
                        result.Set(false, "Không tìm thấy mối quan hệ", ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    originReplationship = await databaseService.Relationships.FindAsync([originReplationshipId], cancellationToken);
                }
                else
                {
                    originReplationship = await databaseService.Relationships.FindAsync([request.RelationId], cancellationToken);
                }

                if (originReplationship == null)
                {
                    result.Set(false, "Không tìm thấy mối quan hệ", ErrorTypeEnum.MediPayError);
                    return result;
                }

                var found = await databaseService.CustomerRelationShips
                    .Where(x => (x.CustomerId == customer.Id && x.CustomerIdRefer == request.CustomerId)
                        || (x.CustomerId == request.CustomerId && x.CustomerIdRefer == customer.Id))
                    .ToListAsync(cancellationToken);
                if (found == null || found.Count == 0)
                {
                    result.Set(false, "Không tìm thấy mối quan hệ giữa khách hàng và người thân", ErrorTypeEnum.MediPayError);
                    return result;
                }
                // Update existing relationships (they always exist)
                var relativeToCustomer = found.First(x => x.CustomerId == customer.Id && x.CustomerIdRefer == request.CustomerId);
                var customerToRelative = found.First(x => x.CustomerId == request.CustomerId && x.CustomerIdRefer == customer.Id);
                // Update relationship IDs
                relativeToCustomer.RelationshipId = originReplationship.ReferId;
                customerToRelative.RelationshipId = originReplationship.Id;

                // Update both relationships
                databaseService.CustomerRelationShips.UpdateRange([relativeToCustomer, customerToRelative]);
                // Save changes to the database
                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (saveResult > 0)
                {
                    result.Set(true, CustomerConstant.SaveChangesSuccess);
                }
                else
                {
                    result.Set(false, CustomerConstant.SaveChangesError);
                }
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

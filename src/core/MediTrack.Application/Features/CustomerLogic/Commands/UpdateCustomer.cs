﻿using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Features.CustomerLogic.Helpers;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using MediTrack.Ultils.Helpers;
using Serilog;

namespace MediTrack.Application.Features.CustomerLogic.Commands
{
    public class UpdateCustomer : CustomerDto, IRequest<BaseCommandResult>
    {
        public string Id { get; set; } = string.Empty;
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class UpdateCustomerHandler(ICurrentUserService currentUserService,
        ICurrentHospitalService currentHospitalService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        IDatabaseService databaseService) : IRequestHandler<UpdateCustomer, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(
            UpdateCustomer request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            try
            {
                Log.Information("{LogPrefix} Update customer request: {FullName}", currentHospitalService.LogPrefix, request.FullName);

                var customer = await databaseService.Customers.FindAsync([request.Id], cancellationToken);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.NotFound);
                    return result;
                }

                if (customer.AccountStatus != "S01")
                {
                    Log.Error("{LogPrefix} Update customer failed because customer is not in a valid status. CustomerId: {CustomerId}, FullName: {FullName}",
                        currentHospitalService.LogPrefix, customer.Id, customer.GetFullName());
                    result.Set(false, "Customer is not in a valid status to update");
                    return result;
                }

                //1. Map customer information
                request.Adapt(customer);
                customer.SetCreate(currentUserService.UserName);

                if (string.IsNullOrEmpty(customer.Id))
                {
                    customer.Id = IdentityHelper.Guid(10);
                }

                //2. Map full name to first name, last name. Map address to province, district, ward
                (string firstName, string lastName) = CustomerHelper.GetVietnameseNameParts(request.FullName ?? string.Empty);
                customer.FirstName = firstName;
                customer.LastName = lastName;

                string? provinceId = request.ProvinceId;
                string? districtId = request.DistrictId;
                string? wardId = request.WardId;
                string? street = request.Street;
                string address = string.Empty;
                bool isContainWard = false;

                 //Bệnh viện đi theo luồng địa chỉ 2 cấp
                var isHospitalTwoLevelAddress = currentHospitalService.CurrentHospital.IsTwoLevelAddress;

                // Nếu bệnh viện không đi theo luồng địa chỉ 2 cấp thì người dùng không thể gửi địa chỉ 2 cấp
                if (!isHospitalTwoLevelAddress)
                {
                    request.IsTwoLevelAddress = false;
                }

                // Nếu người dùng không gửi thông tin địa chỉ 2 cấp thì mặc định là false
                var isInputTwoLevelAddress = request.IsTwoLevelAddress ?? false;

                //0. Prepare data
                var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);

                var districts = await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId ?? string.Empty, cancellationToken);

                // Nếu bệnh nhân nhập địa chỉ 2 cấp thì lấy danh sách quận huyện theo tỉnh
                var wardParentId = isInputTwoLevelAddress ? request.ProvinceId : request.DistrictId;
                var wards = await wardRepository.GetWardByParentIdAsync(wardParentId, isInputTwoLevelAddress, cancellationToken);

                //3. Nếu người dùng không gửi id tỉnh, quận huyện, phường xã thì sẽ tự động phân tích địa chỉ theo luồng 3 cấp
                if (string.IsNullOrEmpty(provinceId))
                {
                    (string? provinceIdSplit, string? districtIdSplit, string? wardIdSplit, string? streetAddress, isContainWard) =
                        ApplicationCustomerHelper.GetLocation(request.Address, provinces, districts, wards);
                    Log.Information("{LogPrefix} ProvinceId: {provinceId}, DistrictId: {districtId}, WardId: {wardId}", currentHospitalService.LogPrefix, provinceIdSplit, districtIdSplit, wardIdSplit);

                    provinceId = provinceIdSplit;
                    districtId = districtIdSplit;
                    wardId = wardIdSplit;
                    street = streetAddress;
                }
                else
                {
                    isContainWard = wards != null && wards.Count > 0;
                }

                //nếu mã tỉnh không được nhập thì trả về lỗi
                if (string.IsNullOrEmpty(provinceId))
                {
                    result.Set(false, ProvinceConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                    return result;
                }

                // kiểm tra mã tỉnh tồn tại
                var province = provinces.FirstOrDefault(p => (isInputTwoLevelAddress ? p.NewId : p.Id) == provinceId);
                if (province == null)
                {
                    result.Set(false, ProvinceConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                    return result;
                }

                provinceId = isHospitalTwoLevelAddress ? province.NewId : province.Id;
                address = isHospitalTwoLevelAddress ? province.NewName : province.Name;

                //Nếu bệnh viện đi theo luồng địa chỉ 2 cấp thì không cần kiểm tra quận huyện
                if (isHospitalTwoLevelAddress)
                {
                    districtId = string.Empty;
                }
                // Nếu theo địa chỉ 3 cấp thì kiểm tra quận huyện
                else
                {
                    if (string.IsNullOrEmpty(districtId))
                    {
                        result.Set(false, DistrictConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    var district = districts.FirstOrDefault(d => d.Id == districtId);
                    if (district == null)
                    {
                        result.Set(false, DistrictConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    address = district.Name + ", " + address;
                }

                // nếu mã phường xã không được nhập thì trả về lỗi
                if (isContainWard)
                {
                    if (string.IsNullOrEmpty(wardId))
                    {
                        result.Set(false, WardConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    // kiểm tra mã phường xã tồn tại
                    var ward = wards!.FirstOrDefault(w => (isInputTwoLevelAddress ? w.NewId : w.Id) == wardId);
                    if (ward == null)
                    {
                        result.Set(false, WardConstant.InvalidAddress, ErrorTypeEnum.MediPayError);
                        return result;
                    }

                    wardId = isHospitalTwoLevelAddress ? ward.NewId : ward.Id;
                    address = (isHospitalTwoLevelAddress ? ward.NewName : ward.Name) + ", " + address;
                }

                if(!string.IsNullOrEmpty(street))
                {
                    address = street + ", " + address;
                }

                customer.IsSyncedTwoLevelAddress = isHospitalTwoLevelAddress;
                customer.ProvinceId = provinceId;
                customer.DistrictId = districtId;
                customer.WardId = wardId;
                customer.Street = street;
                customer.Nation = string.IsNullOrEmpty(request.Nation) ? "000" : request.Nation;
                customer.NationalityId = request.Nationality;
                customer.Address = address;
                customer.IdentityIssueDate = request.IdentityIssueDate;
                customer.IdentityIssuePlace = request.IdentityIssuePlace;
                customer.HealthInsuranceNo = request.HealthInsuranceNo;

                customer.BankTranId = request.BankTranId;

                customer.AccountStatus = "S1";
                customer.HospitalId = currentHospitalService.CurrentHospital.HospitalId;
                customer.EIDTransactionId = request.EIDTransactionId;
                customer.FaceMatchingTransactionId = request.FaceMatchingTransactionId;
                customer.LivenessTransactionId = request.LivenessTransactionId;
                customer.RegistrationType = request.RegistrationType;

                databaseService.Customers.Update(customer);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (saveResult > 0)
                {
                    Log.Information("{LogPrefix} Update customer success. CustomerId: {CustomerId}, FullName: {FullName}",
                        currentHospitalService.LogPrefix, customer.Id, customer.GetFullName());
                    result.Set(true, CustomerConstant.SaveChangesSuccess);
                }
                else
                {
                    Log.Error("{LogPrefix} Update customer failed. CustomerId: {CustomerId}, FullName: {FullName}",
                        currentHospitalService.LogPrefix, customer.Id, customer.GetFullName());
                    result.Set(false, CustomerConstant.SaveChangesError);
                }
            }
            catch (Exception ex)
            {
                Log.Error("{LogPrefix} Update customer error. Error: {ErrorMessage}", currentHospitalService.LogPrefix, ex.Message);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

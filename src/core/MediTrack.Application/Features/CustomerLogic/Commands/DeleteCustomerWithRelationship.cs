﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.CustomerLogic.Commands
{
    public class DeleteCustomerWithRelationship: IRequest<BaseCommandResult>
    {
        public string CustomerId { get; set; } = string.Empty;
    }

    public class DeleteCustomerWithRelationshipHandler(
        IDatabaseService databaseService) : IRequestHandler<DeleteCustomerWithRelationship, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(
            DeleteCustomerWithRelationship request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            try
            {
                var customer = await databaseService
                    .Customers.FindAsync([request.CustomerId], cancellationToken);

                if (customer is null)
                {
                    result.Set(false, CustomerConstant.NotFound);
                    return result;
                }
                
                if (customer.AccountStatus == "S01")
                {
                    customer.IsDeleted = true;
                    databaseService.Customers.Update(customer);
                }

                var relationships = await databaseService
                    .CustomerRelationShips
                    .Where(x => x.CustomerId == request.CustomerId || x.CustomerIdRefer == request.CustomerId)
                    .ToListAsync(cancellationToken);

                databaseService.CustomerRelationShips.RemoveRange(relationships);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (saveResult > 0)
                {
                    result.Set(true, CustomerConstant.SaveChangesSuccess);
                }
                else
                {
                    result.Set(false, CustomerConstant.SaveChangesError);
                }
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

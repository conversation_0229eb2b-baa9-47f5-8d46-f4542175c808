﻿namespace MediTrack.Application.Features.CustomerLogic.Dtos
{
    public class CustomerDto
    {
        public string? Image { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string? IdentityNo { get; set; } = string.Empty;
        public string? IdentityIssuePlace { get; set; }
        public DateTime? IdentityIssueDate { get; set; }
        public string? FullName { get; set; } = string.Empty;
        /// <summary>
        /// Thường trú
        /// </summary>
        public string? PlaceOfResidence { get; set; }
        public string? PlaceOfOrigin { get; set; }
        public string? Sex { get; set; }
        public string? Nationality { get; set; }
        public string? Nation { get; set; }

        public string? Address { get; set; }
        public string? ProvinceId { get; set; }
        public string? DistrictId { get; set; }
        public string? WardId { get; set; }
        public string? Street { get; set; }

        /// <summary>
        /// Chức vụ
        /// </summary>
        public string? PositionId { get; set; }

        /// <summary>
        /// Mã nghề nghiệp
        /// </summary>
        public string? CareerId { get; set; }
        public string? BankTranId { get; set; } = string.Empty;

        public string? Gender { get; set; } = string.Empty;
        public string? OnBoardId { get; set; } = string.Empty;
        public string? Phone { get; set; } = string.Empty;

        /// <summary>
        /// Mã thẻ BHYT
        /// </summary>
        public string? HealthInsuranceNo { get; set; } = string.Empty;
        
        public string? EIDTransactionId { get; set; } = string.Empty;
        public string? FaceMatchingTransactionId { get; set; } = string.Empty;
        public string? LivenessTransactionId { get; set; } = string.Empty;
        public int RegistrationType { get; set; } = 0;
    }
}

﻿using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.CustomerLogic.Dtos
{
    public class GetCustomerDto
    {
        public string Id { get; set; } = string.Empty;
        public string? Image { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string? IdentityNo { get; set; } = string.Empty;
        public string? IdentityIssuePlace { get; set; }
        public DateTime? IdentityIssueDate { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PlaceOfResidence { get; set; }
        public string? PlaceOfOrigin { get; set; }
        public string? Sex { get; set; }
        public string? NationalityId { get; set; }
        public string? PatientId { get; set; }
        public string? PatientCode { get; set; }
        public string? Address { get; set; }
        public string? ProvinceId { get; set; }
        public string? DistrictId { get; set; }
        public string? WardId { get; set; }
        public string? Nation { get; set; }
        public string? Street { get; set; }
        public string? HealthInsuranceNo { get; set; }
        public string? HealthInsurancePlace { get; set; }
        public string? CareerId { get; set; }
        public string? SocialCareerId { get; set; }
        public string? PositionId { get; set; }
        public string? AccountStatus { get; set; }
        public string? BankTranId { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; } = string.Empty;
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? RejectReason { get; set; }
        public bool IsReject { get; set; }
        public bool IsSyncedTwoLevelAddress { get; set; }
        public List<MedicalHistory>? MedicalHistories { get; set; } = [];
    }
}

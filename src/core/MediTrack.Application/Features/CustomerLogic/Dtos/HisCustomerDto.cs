﻿using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.CustomerLogic.Dtos
{
    public class HisCustomerDto
    {
        public string PatientId { get; set; } = string.Empty;
        public string PatientCode { get; set; } = string.Empty;
        public string CareerId { get; set; } = string.Empty;
        public bool IsReject { get; set; }
        public string RejectReason { get; set; } = string.Empty;
        public List<MedicalHistory> MedicalHistory { get; set; } = new();
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Mapster;

namespace MediTrack.Application.Features.CustomerLogic.Queries
{
    public class GetCustomerHealthInsurance
        : IRequest<BaseCommandResultWithData<CustomerHealthInsurance>>
    {
        public string CustomerKey { get; set; } = string.Empty;
        public string? HealthInsuranceNo { get; set; }

    }

    public class GetCustomerHealthInsuranceHandler(ICurrentHospitalService currentHospitalService,
        IHospitalMetadataRepository hospitalMetadataRepository,
        IDatabaseService databaseService)
        : IRequestHandler<GetCustomerHealthInsurance, BaseCommandResultWithData<CustomerHealthInsurance>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<CustomerHealthInsurance>> Handle(GetCustomerHealthInsurance request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CustomerHealthInsurance>();

            var customer = await databaseService.Customers
                .FindAsync([request.CustomerKey], cancellationToken);

            customer ??= await databaseService.Customers
                .FirstOrDefaultAsync(x => x.IdentityNo == request.CustomerKey, cancellationToken);

            if (customer is null)
            {
                result.Set(false, CustomerConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            // Nếu FE yêu cầu kiểm tra bảo hiểm bằng mã bảo hiểm
            if (!string.IsNullOrEmpty(request.HealthInsuranceNo))
            {
                customer.HealthInsuranceNo = request.HealthInsuranceNo;
            }

            (bool getResult, string message, ErrorTypeEnum errorType, CustomerHealthInsurance data)
                = await hisService.GetCustomerHealthInsurance(customer, !string.IsNullOrEmpty(request.HealthInsuranceNo));

            if (getResult)
            {
                // Convert medical history codes to readable text
                if (data.MedicalHistories?.Count > 0)
                {
                    foreach (var history in data.MedicalHistories)
                    {
                        // Convert codes to readable text while keeping original codes as backup
                        if (!string.IsNullOrEmpty(history.TreatmentResult))
                        {
                            var treatmentText = GetTreatmentResultText(history.TreatmentResult);
                            history.TreatmentResult = treatmentText;
                        }

                        if (!string.IsNullOrEmpty(history.AdmissionReason))
                        {
                            var admissionText = GetAdmissionReasonText(history.AdmissionReason);
                            history.AdmissionReason = admissionText;
                        }

                        if (!string.IsNullOrEmpty(history.Condition))
                        {
                            var conditionText = GetConditionText(history.Condition);
                            history.Condition = conditionText;
                        }
                    }
                }
                // Lấy danh sách id mặc định các danh mục theo bảo hiểm y tế
                var hospitalMetaData = await hospitalMetadataRepository.GetHospitalMetadataInsuranceDataConfigDefaultAsync(
                    currentHospitalService.CurrentHospital.HospitalId,
                    data.RegisterPlaceID,
                    cancellationToken: cancellationToken);
                if (hospitalMetaData != null)
                {
                    data.InsuranceDataDefaultModel = hospitalMetaData.Adapt<InsuranceDataDefaultModel>();
                }

                result.Set(true, CustomerConstant.Ok, data);
            }
            else
            {
                result.Set(false, message, errorType);
            }

            return result;
        }

        // Chuyển đổi mã kết quả điều trị thành text
        private static string GetTreatmentResultText(string code)
        {
            return code switch
            {
                "1" => "Khỏi",
                "2" => "Đỡ",
                "3" => "Không thay đổi",
                "4" => "Nặng hơn",
                "5" => "Tử vong",
                _ => code
            };
        }

        // Chuyển đổi mã lý do đến khám bệnh thành text
        private static string GetAdmissionReasonText(string code)
        {
            return code switch
            {
                "1" => "Đúng tuyến",
                "2" => "Cấp cứu",
                "3" => "Trái tuyến",
                _ => code
            };
        }

        // Chuyển đổi mã tình trạng ra viện thành text
        private static string GetConditionText(string code)
        {
            return code switch
            {
                "1" => "Ra viện",
                "2" => "Chuyển viện",
                "3" => "Trốn viện",
                "4" => "Xin ra viện",
                _ => code
            };
        }
    }
}

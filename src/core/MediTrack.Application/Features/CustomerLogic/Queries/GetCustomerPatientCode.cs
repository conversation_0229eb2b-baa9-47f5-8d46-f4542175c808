﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using MediTrack.Domain.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.CustomerLogic.Queries
{
    public class GetCustomerPatientCode : IRequest<BaseCommandResultWithData<CustomerPatientCodeDto>>
    {
        public string Key { get; set; } = string.Empty;
    }

    public class GetCustomerPatientCodeHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        ICurrentHospitalService currentHospitalService)
        : IRequestHandler<GetCustomerPatientCode, BaseCommandResultWithData<CustomerPatientCodeDto>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<CustomerPatientCodeDto>> Handle(
            GetCustomerPatientCode request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<CustomerPatientCodeDto>();
            CustomerPatientCodeDto resultData = new();

            var customer = await databaseService.Customers
                .FirstOrDefaultAsync(x => x.Id == request.Key || x.IdentityNo == request.Key, cancellationToken);

            if (customer is null)
            {
                result.Set(false, CustomerConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var customerHospital = await databaseService.CustomerHospitals
                .FirstOrDefaultAsync(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId && x.CustomerId == customer.Id, cancellationToken);

            if (!string.IsNullOrEmpty(customerHospital?.PatientCode))
            {
                resultData.CustomerId = customer.Id;
                resultData.PatientCode = customerHospital.PatientCode;
                resultData.FullName = customer.GetFullName();

                result.Set(true, CustomerConstant.Ok, resultData);

                return result;
            }

            bool isExistCustomerHospital = customerHospital is not null;
            if (!isExistCustomerHospital)
            {
                customerHospital = new CustomerHospital { HospitalId = currentHospitalService.CurrentHospital.HospitalId, CustomerId = customer.Id };
            }

            //0. Call HIS to get patient code
            (bool getResult, string message, ErrorTypeEnum errorType, HisCustomerDto hisCustomerDto) = await hisService.GetCustomerHis(customer);
            if (!getResult || hisCustomerDto is null)
            {
                result.Set(false, message, errorType);
                return result;
            }

            //1. Save patient code
            customerHospital!.PatientId = hisCustomerDto.PatientId ?? string.Empty;
            customerHospital.PatientCode = hisCustomerDto.PatientCode ?? string.Empty;
            customerHospital.CareerId = hisCustomerDto.CareerId ?? string.Empty;

            if (isExistCustomerHospital)
            {
                customerHospital.SetUpdate(currentUserService.UserName);
                databaseService.CustomerHospitals.Update(customerHospital);
            }
            else
            {
                customerHospital.SetCreate(currentUserService.UserName);
                await databaseService.CustomerHospitals.AddAsync(customerHospital, cancellationToken);
            }

            var saveResult = await databaseService.SaveChangesAsync(cancellationToken);
            if (saveResult > 0)
            {
                resultData.CustomerId = customer.Id;
                resultData.PatientCode = customerHospital.PatientCode;
                resultData.FullName = customer.GetFullName();
                result.Set(true, CustomerConstant.Ok, resultData);
            }
            else
            {
                result.Set(false, CustomerConstant.SaveChangesError, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

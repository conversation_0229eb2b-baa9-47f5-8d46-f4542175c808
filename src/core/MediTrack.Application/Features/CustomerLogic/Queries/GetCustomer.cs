﻿using FluentValidation;
using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.CustomerLogic.Queries
{
    public class GetCustomer : IRequest<BaseCommandResultWithData<GetCustomerDto>>
    {
        public string Key { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;
        public string? PatientCodeType { get; set; } = "CCCD";

        //validate
        public class Validator : AbstractValidator<GetCustomer>
        {
            public Validator()
            {
                RuleFor(x => x.PatientCodeType)
                    .Must(x => x == "BHYT" || x == "MaYTe" || x == "CCCD")
                    .When(x => !string.IsNullOrEmpty(x.PatientCodeType));
                RuleFor(x => x.PatientCode)
                    .NotEmpty()
                    .When(x => x.PatientCodeType == "BHYT" || x.PatientCodeType == "MaYTe");
            }
        }
    }

    public class GetCustomerHandler(
        ICurrentHospitalService currentHospitalService,
        ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<GetCustomer, BaseCommandResultWithData<GetCustomerDto>>
    {
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<GetCustomerDto>> Handle(
            GetCustomer request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<GetCustomerDto>();
            var customer = await databaseService.Customers.FindAsync([request.Key], cancellationToken);

            customer ??= await databaseService.Customers
                .FirstOrDefaultAsync(x => x.IdentityNo == request.Key, cancellationToken);

            if (customer is null)
            {
                result.Set(false, CustomerConstant.NotFound, ErrorTypeEnum.MediPayError);
                return result;
            }

            var customerHospital = await databaseService.CustomerHospitals
                .FirstOrDefaultAsync(x => x.HospitalId == currentHospitalService.CurrentHospital.HospitalId && x.CustomerId == customer.Id, cancellationToken);

            bool isExistCustomerHospital = customerHospital is not null;
            if (!isExistCustomerHospital)
            {
                customerHospital = new CustomerHospital
                {
                    HospitalId = currentHospitalService.CurrentHospital.HospitalId,
                    CustomerId = customer.Id
                };
            }

            var customerDto = customer.Adapt<GetCustomerDto>();

            //nếu tìm kiếm theo mã BN mà chưa truyền type => gán lại bằng MaYTe
            if (!string.IsNullOrEmpty(request.PatientCode) && string.IsNullOrEmpty(request.PatientCodeType))
            {
                request.PatientCodeType = "MaYTe";
            }

            // Gọi API HIS
            (bool resultRes, _, _, HisCustomerDto hisCustomer)
                = await currentHospitalService.HisService.GetCustomerHis(customer, request.PatientCode, request.PatientCodeType ?? "CCCD");

            if (resultRes)
            {
                // Nếu thông tin khách hàng HIS khác với thông tin khách hàng hệ thống 
                // thì cập nhật thông tin khách hàng hệ thống
                if (customerHospital!.PatientCode != hisCustomer.PatientCode
                    || customerHospital.PatientId != hisCustomer.PatientId)
                {
                    customerHospital.PatientId = hisCustomer.PatientId ?? string.Empty;
                    customerHospital.PatientCode = hisCustomer.PatientCode ?? string.Empty;
                    customerHospital.CareerId = hisCustomer.CareerId ?? string.Empty;

                    if (isExistCustomerHospital)
                    {
                        customerHospital.SetUpdate(currentUserService.UserName);
                        databaseService.CustomerHospitals.Update(customerHospital);
                    }
                    else
                    {
                        customerHospital.SetCreate(currentUserService.UserName);
                        await databaseService.CustomerHospitals.AddAsync(customerHospital, cancellationToken);
                    }

                    await databaseService.SaveChangesAsync(cancellationToken);
                }
                customerDto.RejectReason = hisCustomer.RejectReason;
                customerDto.IsReject = hisCustomer.IsReject;
                customerDto.MedicalHistories = hisCustomer.MedicalHistory;
            }

            customerDto.PatientId = customerHospital!.PatientId;
            customerDto.PatientCode = customerHospital.PatientCode;
            customerDto.SocialCareerId = customerHospital.CareerId;

            result.Set(true, CustomerConstant.Ok, customerDto);

            return result;
        }
    }
}

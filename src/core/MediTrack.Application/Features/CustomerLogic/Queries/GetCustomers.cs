﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Domain.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.CustomerLogic.Queries
{
    public class GetCustomers : IRequest<BaseCommandResultWithData<IEnumerable<Customer>>>
    {
        public string Keywords { get; set; } = string.Empty;
    }

    public class GetCustomersHandler : IRequestHandler<GetCustomers, BaseCommandResultWithData<IEnumerable<Customer>>>
    {
        public Task<BaseCommandResultWithData<IEnumerable<Customer>>> Handle(GetCustomers request, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
    }
}

﻿using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.HospitalLogic.Dtos
{
    public static class HospitalExtensions
    {
        public static HospitalDto GetHospitalDto(this Hospital hospital,
            List<ProvinceDto>? provinces,
            List<DistrictDto>? districts,
            List<WardDto>? wards)
        => new()
        {
            AccountHolderName = hospital.AccountHolderName,
            ContactPhone = hospital.ContactPhone,
            ContactEmail = hospital.ContactEmail,
            BankTransferId = hospital.BankTransferId,
            AccountNumber = hospital.AccountNumber,
            Address = hospital.Address,
            BankBranchCode = hospital.BankBranchCode,
            BankName = hospital.BankName,
            BankTranPrefix = hospital.BankTranPrefix,
            District = districts?.FirstOrDefault(a => hospital.DistrictId == a.Id)?.Name ?? string.Empty,
            Name = hospital.Name,
            Id = hospital.Id,
            DistrictId = hospital.DistrictId,
            HisInsuranceKey = hospital.HisInsuranceKey,
            HisUrl = hospital.HisUrl,
            Images = hospital.Images,
            LogoUrl = hospital.LogoUrl,
            ParentId = hospital.ParentId,
            Provice = provinces?.FirstOrDefault(a => hospital.ProvinceId == a.Id)?.Name ?? string.Empty,
            ProvinceId = hospital.ProvinceId,
            TaxCode = hospital.TaxCode,
            Ward = wards?.FirstOrDefault(a => hospital.WardId == a.Id)?.Name ?? string.Empty,
            WardId = hospital.WardId,
            IsInsuranceAdvancePayment = hospital.IsInsuranceAdvancePayment,
            IsAdvancePayment = hospital.IsAdvancePayment,
            IsSkipGetInsuranceServices = hospital.IsSkipGetInsuranceServices,
            IsSkipGetServices = hospital.IsSkipGetServices,
            IsShowEducationLevel = hospital.IsShowEducationLevel,
            IsShowWorkPlace = hospital.IsShowWorkPlace,
            IsShowWorkAddress = hospital.IsShowWorkAddress,
            HisId = hospital.HisId,
            LivenessType = hospital.LivenessType,
            IsInputTransferReferralInfo = hospital.IsInputTransferReferralInfo,
            IsSkipInputPhoneNumber = hospital.IsSkipInputPhoneNumber,
            IsHideReferralInsurance = hospital.IsHideReferralInsurance,
            IsInputReasonForVisit = hospital.IsInputReasonForVisit,
            IsShowCareer = hospital.IsShowCareer,
            IsShowSocialCareer = hospital.IsShowSocialCareer,
            IsShowTransferReferralType = hospital.IsShowTransferReferralType,
            IsShowTransferReferralReason = hospital.IsShowTransferReferralReason,
            IsShowTransferReferralDate = hospital.IsShowTransferReferralDate,
            IsShowTransferReferralDiagnosisInfo = hospital.IsShowTransferReferralDiagnosisInfo,
            IsNeedAuthenticateBeforeGetQueueNumber = hospital.IsNeedAuthenticateBeforeGetQueueNumber,
            IsSearchPatientByInsuranceNo = hospital.IsSearchPatientByInsuranceNo,
            IsAllowSelectNewPatient = hospital.IsAllowSelectNewPatient,
            IsAutoCheckAndSelectInsuranceType = hospital.IsAutoCheckAndSelectInsuranceType,
            IsHideInsuranceToggleButton = hospital.IsHideInsuranceToggleButton,
            IsHidePriorityQueueToggleButton = hospital.IsHidePriorityQueueToggleButton,
            IsShowInputEInvoiceInfo = hospital.IsShowInputEInvoiceInfo,
            IsHideChangeAddressField = hospital.IsHideChangeAddressField,
            AutoLogoutTimeSeconds = hospital.AutoLogoutTimeSeconds,
            IsAutoSelectObject130 = hospital.IsAutoSelectObject130,
            IsShowArvTreatment = hospital.IsShowArvTreatment,
            IsShowBloodPressure = hospital.IsShowBloodPressure,
            IsShowHeartRate = hospital.IsShowHeartRate,
            IsShowRespiratoryRate = hospital.IsShowRespiratoryRate,
            IsShowBloodOxygen = hospital.IsShowBloodOxygen,
            IsShowHeight = hospital.IsShowHeight,
            IsShowWeight = hospital.IsShowWeight,
            IsShowPulseRate = hospital.IsShowPulseRate,
            IsShowTemperature = hospital.IsShowTemperature,
            IsBlockForDuplicatedVisitInsurance = hospital.IsBlockForDuplicatedVisitInsurance,
            IsAllowInputAdvancePaymentAmount = hospital.IsAllowInputAdvancePaymentAmount,
            AdvancePaymentMinAmount = hospital.AdvancePaymentMinAmount,
            AdvancePaymentMaxAmount = hospital.AdvancePaymentMaxAmount,
            IsAllowCustomerRetryByPatientCode = hospital.IsAllowCustomerRetryByPatientCode,
            IsAllowCustomerRetryByInsuranceCode = hospital.IsAllowCustomerRetryByInsuranceCode,
            RequireInputConfig = hospital.RequireInputConfig,
            FaceMatchingRateAccepted = hospital.FaceMatchingRateAccepted,
            IsBlockInsuranceOnWeekend = hospital.IsBlockInsuranceOnWeekend,
            IsIgnoreCheckInsurance = hospital.IsIgnoreCheckInsurance,
            IsBlockUnder16YearsOld = hospital.IsBlockUnder16YearsOld,
            IsShowDifferenceInsuranceAndServicePrice = hospital.IsShowDifferenceInsuranceAndServicePrice,
            IsActiveConfigExaminationTime = hospital.IsActiveConfigExaminationTime,
            IsAllowBypassAdvancePayment = hospital.IsAllowBypassAdvancePayment,
            IsTwoLevelAddress = hospital.IsTwoLevelAddress,
            IsShowAccidentCode = hospital.IsShowAccidentCode,
            IsShowBloodType = hospital.IsShowBloodType,
            IsInsurancePopupHiddenOnCorrectReferral = hospital.IsInsurancePopupHiddenOnCorrectReferral,
            IsInsurancePaymentInAppAllowed = hospital.IsInsurancePaymentInAppAllowed,
            IsServicePaymentInAppAllowed = hospital.IsServicePaymentInAppAllowed,
            IsInputDiagnosisVisible = hospital.IsInputDiagnosisVisible,
            IsAllowOnlyOneQueuePerDay = hospital.IsAllowOnlyOneQueuePerDay,
            IsShowChatBot = hospital.IsShowChatBot,
        };
    }
}

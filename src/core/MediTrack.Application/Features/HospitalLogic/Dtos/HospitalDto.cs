using MediTrack.Domain.Domain;

namespace MediTrack.Application.Features.HospitalLogic.Dtos
{
    public class HospitalDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<string>? Images { get; set; } = [];
        public string AccountHolderName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;
        public string BankTransferId { get; set; } = string.Empty;
        public string? BankBranchCode { get; set; }
        public string BankTranPrefix { get; set; } = string.Empty;
        public string HisInsuranceKey { get; set; } = string.Empty;
        public string HisUrl { get; set; } = string.Empty;
        public string ProvinceId { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string WardId { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;
        public string ContactPhone { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public float? Latitude { get; set; }
        public float? Longitude { get; set; }
        public string? ParentId { get; set; }
        public bool IsGenQR { get; set; } = false;
        public bool IsSkipGetServices { get; set; } = false;
        public bool IsSkipGetInsuranceServices { get; set; } = false;
        public bool IsAdvancePayment { get; set; } = false;
        public bool IsInsuranceAdvancePayment { get; set; } = false;
        public bool IsHisIgnoreCreateRegister { get; set; } = false;
        public bool IsGenQueueNumberDefault { get; set; } = false;
        public bool IsGenQueueNumberByHis { get; set; } = false;
        public bool IsNeedAuthenticateBeforeGetQueueNumber { get; set; } = false;
        public bool IsPaymentDocumentDefault { get; set; } = false;
        public bool IsRegisterDocumentDefault { get; set; } = false;
        public bool IsSupportInsuranceDefault { get; set; } = false;
        public bool IsRePrintRegisterDocumentDefault { get; set; } = false;
        public bool IsReExaminationDefault { get; set; } = false;
        public bool IsReExaminationByDoctorDefault { get; set; } = false;
        public bool IsRegisterRelativeDefault { get; set; } = false;
        public bool IsSupportVNeIDDefault { get; set; } = false;
        public bool IsParaclinicalExaminationDefault { get; set; } = false;
        public bool IsSupportInsuranceInServiceScreenDefault { get; set; } = false;
        public bool IsHideInsuranceToggleButton { get; set; } = false;
        public bool IsHidePriorityQueueToggleButton { get; set; } = false;
        public bool IsAutoCheckAndSelectInsuranceType { get; set; } = false;
        public bool IsIgnoreInsurancePayment { get; set; }
        public bool IsShowEducationLevel { get; set; }
        public bool IsShowWorkPlace { get; set; }
        public bool IsShowWorkAddress { get; set; }
        public int LivenessType { get; set; } = 3;
        public string KioskVersion { get; set; } = string.Empty;
        public bool IsInputTransferReferralInfo { get; set; }
        public bool IsInputReasonForVisit { get; set; }
        public bool IsMaintenanceMode { get; set; } = false;
        public bool IsShowCareer { get; set; }
        public bool IsShowSocialCareer { get; set; }
        public bool IsShowTransferReferralType { get; set; }
        public bool IsShowTransferReferralReason { get; set; }
        public bool IsShowTransferReferralDate { get; set; }
        public bool IsShowTransferReferralDiagnosisInfo { get; set; }
        public bool IsSearchPatientByInsuranceNo { get; set; }
        public bool IsAllowSelectNewPatient { get; set; }
        public bool IsGenQRWhenCreateRegister { get; set; }
        public bool IsSkipInputPhoneNumber { get; set; }
        public bool IsHideInputPhoneNumber { get; set; }
        public bool IsHideReferralInsurance { get; set; }
        public bool IsShowInputEInvoiceInfo { get; set; }
        public bool IsHideChangeAddressField { get; set; }
        public string? HisId { get; set; }
        public string Ward { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string Provice { get; set; } = string.Empty;
        public int AutoLogoutTimeSeconds { get; set; }
        public bool IsAutoSelectObject130 { get; set; }
        public bool IsShowArvTreatment { get; set; }
        public bool IsShowBloodPressure { get; set; }
        public bool IsShowHeartRate { get; set; }
        public bool IsShowRespiratoryRate { get; set; }
        public bool IsShowBloodOxygen { get; set; }
        public bool IsShowHeight { get; set; }
        public bool IsShowWeight { get; set; }
        public bool IsShowPulseRate { get; set; }
        public bool IsShowTemperature { get; set; }
        public bool IsBlockForDuplicatedVisitInsurance { get; set; }
        public bool IsAllowInputAdvancePaymentAmount { get; set; }
        public decimal AdvancePaymentMinAmount { get; set; }
        public decimal? AdvancePaymentMaxAmount { get; set; }
        public bool IsAllowCustomerRetryByPatientCode { get; set; }
        public bool IsAllowCustomerRetryByInsuranceCode { get; set; }
        public string[]? AdvanceMoney { get; set; } = [];
        public double FaceMatchingRateAccepted { get; set; }
        public bool IsBlockInsuranceOnWeekend { get; set; }
        public bool IsIgnoreCheckInsurance { get; set; }
        public bool IsBlockUnder16YearsOld { get; set; }
        public bool IsShowDifferenceInsuranceAndServicePrice { get; set; }
        public bool IsActiveConfigExaminationTime { get; set; }
        public bool IsAllowBypassAdvancePayment { get; set; }
        public bool IsTwoLevelAddress { get; set; }
        public bool IsShowAccidentCode { get; set; }
        public bool IsShowBloodType { get; set; }
        public bool IsInsurancePopupHiddenOnCorrectReferral { get; set; }
        public bool IsInsurancePaymentInAppAllowed { get; set; }
        public bool IsServicePaymentInAppAllowed { get; set; }
        public bool IsInputDiagnosisVisible { get; set; } = false;
        public bool IsAllowOnlyOneQueuePerDay { get; set; } = false;
        public bool IsShowChatBot { get; set; } = false;
        public Dictionary<string, bool>? RequireInputConfig { get; set; } = [];
    }
}

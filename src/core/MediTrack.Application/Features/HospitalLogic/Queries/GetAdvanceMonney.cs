﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.HospitalLogic.Queries
{
    public class GetAdvanceMonney : IRequest<BaseCommandResultWithData<string[]?>>
    {
        public string HospitalId { get; set; } = string.Empty;
    }

    public class GetAdvanceMonneyHandler(
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetAdvanceMonney, BaseCommandResultWithData<string[]?>>
    {
        public async Task<BaseCommandResultWithData<string[]?>> Handle(GetAdvanceMonney request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<string[]?>();
            (_, _, _, string[]? addvancedMoney) = await currentHospitalService.HisService.GetAdvanceMoney();
            result.Set(true, HospitalConstant.Ok, addvancedMoney);
            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.DistrictLogic.Queries;
using MediTrack.Application.Features.HospitalLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Queries;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Queries;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.HospitalLogic.Queries
{
    public class GetHospitals 
        : IRequest<BaseCommandResultWithData<IEnumerable<HospitalDto>>>
    {
        public string? Keywords { get; set; } = string.Empty;
        public string[]? ProvinceIds { get; set; } = [];
        public string[]? DistrictIds { get; set; } = [];
        public string[]? WardIds { get; set; } = [];
    }

    public class GetHospitalsHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IMediator mediator)
        : IRequestHandler<GetHospitals, BaseCommandResultWithData<IEnumerable<HospitalDto>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IMediator mediator = mediator;

        public async Task<BaseCommandResultWithData<IEnumerable<HospitalDto>>> Handle(
            GetHospitals request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<HospitalDto>>();
            
            if(string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, HospitalConstant.Forbidden);
                return result;
            }

            var hospitals = await databaseService.Hospitals
                .Where(x => (string.IsNullOrEmpty(request.Keywords) || (x.Name != null && x.Name.Contains(request.Keywords))
                                                                    || (x.Id != null && x.Id.Contains(request.Keywords)))
                            && (request.ProvinceIds!.Length == 0 || request.ProvinceIds.Contains(x.ProvinceId))
                            && (request.DistrictIds!.Length== 0 || request.DistrictIds.Contains(x.DistrictId))
                && (request.WardIds!.Length == 0 || request.WardIds.Contains(x.WardId)))
                .ToListAsync(cancellationToken: cancellationToken);

            List<ProvinceDto>? provinces = [];
            List<DistrictDto>? districts = [];
            List<WardDto>? wards = [];
            
            if (hospitals?.Count > 0)
            {
                provinces = (await mediator.Send(new GetProvinces(), cancellationToken))?.Data?.ToList();
                districts = (await mediator.Send(new GetDistricts(), cancellationToken))?.Data?.ToList();
                wards = (await mediator.Send(new GetWards(), cancellationToken))?.Data?.ToList();
            }

            var results = hospitals?.Select(x => x.GetHospitalDto(provinces: provinces,
                districts: districts,
                wards: wards)) ?? [];
            result.Set(true, HospitalConstant.Ok, results);

            return result;
        }
    }
}

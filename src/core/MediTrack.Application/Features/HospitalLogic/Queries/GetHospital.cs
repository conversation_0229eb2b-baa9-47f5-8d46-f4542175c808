using Amazon.Runtime;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DistrictLogic.Queries;
using MediTrack.Application.Features.HospitalLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Queries;
using MediTrack.Application.Features.WardLogic.Queries;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.HospitalLogic.Queries
{
    public class GetHospital : IRequest<BaseCommandResultWithData<HospitalDto>>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class GetHospitalHandler(
        IDatabaseService databaseService,
        IMediator mediator,
        IHisServiceHelper hisServiceHelper,
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetHospital, BaseCommandResultWithData<HospitalDto>>
    {
        public async Task<BaseCommandResultWithData<HospitalDto>> Handle(
            GetHospital request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<HospitalDto>();

            var hospital = await hisServiceHelper.GetHospital(request.Id, databaseService, cancellationToken);
            if (hospital is not null)
            {
                var provinces = (await mediator.Send(new GetProvinces(), cancellationToken))?.Data?.ToList();
                var districts = (await mediator.Send(new GetDistricts(), cancellationToken))?.Data?.ToList();
                var wards = (await mediator.Send(new GetWards(), cancellationToken))?.Data?.ToList();
                var hospitalDto = hospital.GetHospitalDto(provinces, districts, wards);

                //HIS Hòa Bình, YHCT Khánh Hòa (Minh Lộ)
                (_, _, _, string[]? addvancedMoney) = await currentHospitalService.HisService.GetAdvanceMoney();
                hospitalDto.AdvanceMoney = addvancedMoney;

                result.Set(true, HospitalConstant.Ok, hospitalDto);
            }
            else
            {
                result.Set(false, HospitalConstant.NotFound, ErrorTypeEnum.MediPayError);
            }

            return result;
        }
    }
}

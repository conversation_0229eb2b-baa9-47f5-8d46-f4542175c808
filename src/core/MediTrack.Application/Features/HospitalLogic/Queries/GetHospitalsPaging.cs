﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DistrictLogic.Dtos;
using MediTrack.Application.Features.DistrictLogic.Queries;
using MediTrack.Application.Features.HospitalLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Features.ProvinceLogic.Queries;
using MediTrack.Application.Features.WardLogic.Dtos;
using MediTrack.Application.Features.WardLogic.Queries;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.HospitalLogic.Queries
{
    public class GetHospitalsPaging : IRequest<BaseCommandResultWithData<BasePaging<HospitalDto>>>
    {
        public string? Keywords { get; set; } = string.Empty;
        public string[]? ProvinceIds { get; set; } = [];
        public string[]? DistrictIds { get; set; } = [];
        public string[]? WardIds { get; set; } = [];

        public int PageIndex { get; set; }
        public int PageSize { get; set; }
    }

    public class GetHospitalsPagingHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IMediator mediator) : IRequestHandler<GetHospitalsPaging,
        BaseCommandResultWithData<BasePaging<HospitalDto>>>
    {
        public async Task<BaseCommandResultWithData<BasePaging<HospitalDto>>> Handle(
            GetHospitalsPaging request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<BasePaging<HospitalDto>>();

            if (string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, HospitalConstant.Forbidden);
                return result;
            }

            var query = databaseService.Hospitals
                .Where(x => (string.IsNullOrEmpty(request.Keywords) || (x.Name != null && x.Name.Contains(request.Keywords))
                                                                    || (x.Id != null && x.Id.Contains(request.Keywords)))
                            && (request.ProvinceIds!.Length == 0 || request.ProvinceIds.Contains(x.ProvinceId))
                            && (request.DistrictIds!.Length == 0 || request.DistrictIds.Contains(x.DistrictId))
                            && (request.WardIds!.Length == 0 || request.WardIds.Contains(x.WardId)));

            var count = await query.CountAsync(cancellationToken: cancellationToken);
            List<ProvinceDto>? provinces = [];
            List<DistrictDto>? districts = [];
            List<WardDto>? wards = [];
            
            if (count > 0)
            {
                provinces = (await mediator.Send(new GetProvinces(), cancellationToken))?.Data?.ToList();
                districts = (await mediator.Send(new GetDistricts(), cancellationToken))?.Data?.ToList();
                wards = (await mediator.Send(new GetWards(), cancellationToken))?.Data?.ToList();

                var hospitals = await query.Skip(request.PageSize * request.PageIndex)
                        .Take(request.PageSize).ToListAsync(cancellationToken: cancellationToken);

                result.Set(true, HospitalConstant.Ok, new BasePaging<HospitalDto>()
                {
                    PageIndex = request.PageIndex,
                    PageSize = request.PageSize,
                    Items = hospitals?
                        .Select(a => a.GetHospitalDto(provinces: provinces, districts: districts, wards: wards))?.ToList() ?? [],
                    TotalItem = count,
                    TotalPage = (count + request.PageSize - 1) / request.PageSize,
                });

                return result;
            }

            result.Set(false, HospitalConstant.NotFound);
            return result;
        }
    }
}

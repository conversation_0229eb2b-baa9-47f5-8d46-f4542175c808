using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HospitalLogic.Dtos;
using MediTrack.Application.Repositories;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.HospitalLogic.Queries
{
    public class GetTransferReferralHospitals : IRequest<BaseCommandResultWithData<List<TransferReferralHospital>>>
    {
    }

    public class GetTransferReferralHospitalsHandler(ISystemMetadataRepository systemMetadataRepository)
    : IRequestHandler<GetTransferReferralHospitals, BaseCommandResultWithData<List<TransferReferralHospital>>>
    {
        public async Task<BaseCommandResultWithData<List<TransferReferralHospital>>> Handle(
            GetTransferReferralHospitals request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<TransferReferralHospital>>();

            // get hospital list from SystemMetadata
            var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("transfer_referral_hospitals", cancellationToken: cancellationToken);

            if (systemMetaData is not null && !string.IsNullOrEmpty(systemMetaData.Value))
            {
                List<TransferReferralHospital> transferReferralHospitals = JsonConvert.DeserializeObject<List<TransferReferralHospital>>(systemMetaData.Value) ?? [];
                result.Set(true, string.Empty, transferReferralHospitals);
            }
            else
            {
                result.Set(false, "Không tìm thấy dữ liệu", []);
            }
            return result;
        }
    }
}
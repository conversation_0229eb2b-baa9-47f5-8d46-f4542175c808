﻿using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class CreateHospital : IRequest<BaseCommandResultWithData<Hospital>>
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string MerchantId { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string IpnUrl { get; set; } = string.Empty;
        public List<string>? Images { get; set; } = [];

        public string AccountHolderName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;

        public string BankTransferId { get; set; } = string.Empty;
        public string BankBranchCode { get; set; } = string.Empty;
        public string BankTranPrefix { get; set; } = string.Empty;
        public string HisSecretKey { get; set; } = string.Empty;
        public string HisUrl { get; set; } = string.Empty;

        public string ProvinceId { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string WardId { get; set; } = string.Empty;

        public string Address { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;

        public string ContactPhone { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;

        public string? ParentId { get; set; }
    }

    public class CreateHospitalHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<CreateHospital, BaseCommandResultWithData<Hospital>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResultWithData<Hospital>> Handle(
            CreateHospital request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Hospital>();

            var hospital = request.Adapt<Hospital>();
            if(string.IsNullOrEmpty(hospital.Id))
            {
                hospital.Id = IdentityHelper.Guid(15);
            }

            hospital.SetCreate(currentUserService.UserName);
            databaseService.Hospitals.Add(hospital);
            var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

            if(saveResult > 0)
            {
                result.Set(true, HospitalConstant.SaveChangesSuccess, hospital);
            }
            else
            {
                result.Set(false, HospitalConstant.SaveChangesError);
            }
           
            return result;
        }
    }
}

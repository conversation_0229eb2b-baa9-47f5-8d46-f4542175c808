﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class UpdateHospital : IRequest<BaseCommandResult>
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string MerchantId { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public string IpnUrl { get; set; } = string.Empty;
        public List<string>? Images { get; set; } = [];

        public string AccountHolderName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string LogoUrl { get; set; } = string.Empty;

        public string BankTransferId { get; set; } = string.Empty;
        public string BankBranchCode { get; set; } = string.Empty;
        public string BankTranPrefix { get; set; } = string.Empty;
        public string HisSecretKey { get; set; } = string.Empty;
        public string HisUrl { get; set; } = string.Empty;

        public string ProvinceId { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string WardId { get; set; } = string.Empty;

        public string Address { get; set; } = string.Empty;
        public string TaxCode { get; set; } = string.Empty;

        public string ContactPhone { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;

        public string? ParentId { get; set; }
    }

    public class UpdateHospitalHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<UpdateHospital, BaseCommandResult>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResult> Handle(
            UpdateHospital request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            if (string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, HospitalConstant.Forbidden);
                return result;
            }

            databaseService.Hospitals
                .Where(x => x.Id == request.Id)
                .ExecuteUpdate(s => s.SetProperty(x => x.Name, request.Name)
                                    .SetProperty(x => x.MerchantId, request.MerchantId)
                                    .SetProperty(x => x.SecretKey, request.SecretKey)
                                    .SetProperty(x => x.IpnUrl, request.IpnUrl)
                                    .SetProperty(x => x.Images, request.Images)
                                    .SetProperty(x => x.AccountHolderName, request.AccountHolderName)
                                    .SetProperty(x => x.AccountNumber, request.AccountNumber)
                                    .SetProperty(x => x.BankName, request.BankName)
                                    .SetProperty(x => x.LogoUrl, request.LogoUrl)
                                    .SetProperty(x => x.BankTransferId, request.BankTransferId)
                                    .SetProperty(x => x.BankBranchCode, request.BankBranchCode)
                                    .SetProperty(x => x.BankTranPrefix, request.BankTranPrefix)
                                    .SetProperty(x => x.HisInsuranceKey, request.HisSecretKey)
                                    .SetProperty(x => x.HisUrl, request.HisUrl)
                                    .SetProperty(x => x.ProvinceId, request.ProvinceId)
                                    .SetProperty(x => x.DistrictId, request.DistrictId)
                                    .SetProperty(x => x.WardId, request.WardId)
                                    .SetProperty(x => x.Address, request.Address)
                                    .SetProperty(x => x.TaxCode, request.TaxCode)
                                    .SetProperty(x => x.ContactPhone, request.ContactPhone)
                                    .SetProperty(x => x.ContactPhone, request.ContactPhone)
                                    .SetProperty(x => x.ParentId, request.ParentId)
                                    .SetProperty(x => x.UpdatedAt, DateTime.UtcNow)
                                    .SetProperty(x => x.UpdatedBy, currentUserService.UserName));

            var updateResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (updateResult > 0)
            {
                result.Set(true, HospitalConstant.DeleteSuccessFully);
            }
            else
            {
                result.Set(false, HospitalConstant.DeleteError);
            }

            return result;
        }
    }
}

using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.KioskLogic.Commands;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class SetHospitalId : IRequest<BaseCommandResult>
    {
        public string HospitalId { get; set; } = string.Empty;
        public string KioskId { get; set; } = string.Empty;
    }

    public class SetHospitalIdHandler : IRequestHandler<SetHospitalId, BaseCommandResult>
    {
        private readonly IHisServiceHelper _hisServiceHelper;
        private readonly IMediator _mediator;
        private readonly IDatabaseService _databaseService;
        private readonly ICurrentUserService _currentUserService;

        public SetHospitalIdHandler(IHisServiceHelper hisServiceHelper, IMediator mediator, IDatabaseService databaseService, ICurrentUserService currentUserService)
        {
            _hisServiceHelper = hisServiceHelper;
            _mediator = mediator;
            _databaseService = databaseService;
            _currentUserService = currentUserService;
        }

        public async Task<BaseCommandResult> Handle(SetHospitalId request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();
            try
            {
                // 1. Check authentication
                if (string.IsNullOrEmpty(_currentUserService.UserName))
                {
                    result.Set(false, HospitalConstant.Forbidden);
                    return result;
                }

                // 2. Check if kioskId is allowed for setup
                var allowedKioskIds = new List<string> { "1245689", "TEST001", "TEST002" };
                if (!allowedKioskIds.Contains(request.KioskId))
                {
                    result.Set(false, $"KioskId {request.KioskId} không được phép setup hospital. Chỉ cho phép: {string.Join(", ", allowedKioskIds)}");
                    return result;
                }

                // 3. Check if hospital exists
                var hospital = await _databaseService.Hospitals.FindAsync(request.HospitalId);
                if (hospital == null)
                {
                    result.Set(false, $"Hospital {request.HospitalId} not found");
                    return result;
                }

                // 4. Find and update kiosk
                var kiosk = await _hisServiceHelper.GetKiosk(request.KioskId, _databaseService, cancellationToken);
                if (kiosk == null)
                {
                    result.Set(false, $"Kiosk {request.KioskId} not found");
                    return result;
                }
                kiosk.HospitalId = request.HospitalId;
                kiosk.SetUpdate(_currentUserService.UserName);
                _databaseService.Kiosks.Update(kiosk);
                await _databaseService.SaveChangesAsync(cancellationToken);

                // 5. Clear cache: first kiosk, then hospital
                var clearKioskCache = new ClearKioskCache { KioskId = request.KioskId };
                await _mediator.Send(clearKioskCache, cancellationToken);

                result.Set(true, $"Set HospitalId for kiosk successfully.");
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
            }
            return result;
        }
    }
}

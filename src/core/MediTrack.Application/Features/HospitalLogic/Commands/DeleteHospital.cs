﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class DeleteHospital : IRequest<BaseCommandResult>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class DeleteHospitalHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService) : IRequestHandler<DeleteHospital, BaseCommandResult>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;

        public async Task<BaseCommandResult> Handle(
            DeleteHospital request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            if (string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, HospitalConstant.Forbidden);
                return result;
            }

            databaseService.Hospitals
                .Where(x => x.Id == request.Id)
                .ExecuteUpdate(s => s.SetProperty(x => x.IsDeleted, true)
                                    .SetProperty(x => x.UpdatedAt, DateTime.Now)
                                    .SetProperty(x => x.UpdatedBy, currentUserService.UserName));

            var deleteResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (deleteResult > 0)
            {
                result.Set(true, HospitalConstant.DeleteSuccessFully);
            }
            else
            {
                result.Set(false, HospitalConstant.DeleteError);
            }
            return result;
        }
    }
}

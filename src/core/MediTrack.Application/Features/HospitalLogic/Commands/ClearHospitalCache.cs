﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class ClearHospitalCache : IRequest<BaseCommandResult>
    {
        public string HospitalId { get; set; } = string.Empty;
        public string PrefixKey { get; set; } = string.Empty;
    }

    public class ClearHospitalCacheHandler(IHisServiceHelper hisServiceHelper, ICachedService cachedService)
        : IRequestHandler<ClearHospitalCache, BaseCommandResult>
    {
        public async Task<BaseCommandResult> Handle(ClearHospitalCache request, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(request.HospitalId))
            {
                if (!string.IsNullOrEmpty(request.PrefixKey))
                {
                    await cachedService.RemoveByPrefixAsync(request.PrefixKey + request.HospitalId, cancellationToken);

                }
                else
                {
                    await hisServiceHelper.ResetHospitalCache(request.HospitalId);
                    await cachedService.RemoveAsync("HospitalMetadatas" + request.HospitalId, cancellationToken);
                    await cachedService.RemoveAsync("SocialCareers" + request.HospitalId, cancellationToken);
                    await cachedService.RemoveAsync("ExameTypes" + request.HospitalId, cancellationToken);
                    await cachedService.RemoveByPrefixAsync("HealthServices" + request.HospitalId, cancellationToken);
                    await cachedService.RemoveByPrefixAsync("Relationships" + request.HospitalId, cancellationToken);
                    await cachedService.RemoveByPrefixAsync("AccessToken_" + request.HospitalId, cancellationToken);
                    await hisServiceHelper.ResetKiosksCache(request.HospitalId);
                }
                return new BaseCommandResult { Success = true };
            }

            return new BaseCommandResult { Success = false, Messages = "HospitalId is required" };
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Reflection;
using System.Threading;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Constants;

namespace MediTrack.Application.Features.HospitalLogic.Commands
{
    public class ConfigHospitalByKey : IRequest<BaseCommandResult>
    {
        public string HospitalId { get; set; } = string.Empty;
        public Dictionary<string, string> Configs { get; set; } = new Dictionary<string, string>();
    }

    public class ConfigHospitalByKeyHandler : IRequestHandler<ConfigHospitalByKey, BaseCommandResult>
    {
        private readonly IMediator _mediator;
        private readonly IDatabaseService _databaseService;
        private readonly ICachedService _cachedService;
        private readonly ICurrentUserService _currentUserService;

        public ConfigHospitalByKeyHandler(IMediator mediator, IDatabaseService databaseService, ICachedService cachedService, ICurrentUserService currentUserService)
        {
            _mediator = mediator;
            _databaseService = databaseService;
            _cachedService = cachedService;
            _currentUserService = currentUserService;
        }

        public async Task<BaseCommandResult> Handle(ConfigHospitalByKey request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();
            try
            {
                // 1. Check authentication
                if (string.IsNullOrEmpty(_currentUserService.UserName))
                {
                    result.Set(false, HospitalConstant.Forbidden);
                    return result;
                }

                // 2. Find hospital
                var hospital = await _databaseService.Hospitals.FindAsync(request.HospitalId);
                if (hospital == null)
                {
                    result.Set(false, $"Hospital {request.HospitalId} not found");
                    return result;
                }

                // 3. Validate all configs first before any updates
                var allowList = GetAllowedHospitalConfigFields();
                var validationErrors = new List<string>();
                var validatedConfigs = new Dictionary<string, (PropertyInfo prop, object convertedValue)>();

                foreach (var config in request.Configs)
                {
                    var key = config.Key;
                    var value = config.Value;

                    // Check if key is in allow list
                    if (!allowList.Contains(key))
                    {
                        validationErrors.Add($"Key '{key}' không nằm trong danh sách cho phép cập nhật");
                        continue;
                    }

                    // Check if property exists
                    var prop = typeof(Hospital).GetProperty(key);
                    if (prop == null)
                    {
                        validationErrors.Add($"Key '{key}' không hợp lệ");
                        continue;
                    }

                    // Validate and convert value based on property type
                    object? convertedValue = null;
                    try
                    {
                        if (prop.PropertyType == typeof(bool))
                        {
                            if (!bool.TryParse(value, out var boolVal))
                            {
                                validationErrors.Add($"Giá trị '{value}' không hợp lệ cho key '{key}' (kiểu bool)");
                                continue;
                            }
                            convertedValue = boolVal;
                        }
                        else if (prop.PropertyType == typeof(int))
                        {
                            if (!int.TryParse(value, out var intVal))
                            {
                                validationErrors.Add($"Giá trị '{value}' không hợp lệ cho key '{key}' (kiểu int)");
                                continue;
                            }
                            convertedValue = intVal;
                        }
                        else if (prop.PropertyType == typeof(decimal))
                        {
                            if (!decimal.TryParse(value, out var decVal))
                            {
                                validationErrors.Add($"Giá trị '{value}' không hợp lệ cho key '{key}' (kiểu decimal)");
                                continue;
                            }
                            convertedValue = decVal;
                        }
                        else if (prop.PropertyType == typeof(string))
                        {
                            convertedValue = value;
                        }
                        else
                        {
                            validationErrors.Add($"Kiểu dữ liệu của key '{key}' chưa được hỗ trợ");
                            continue;
                        }
                    }
                    catch
                    {
                        validationErrors.Add($"Lỗi khi chuyển đổi giá trị cho key '{key}'");
                        continue;
                    }

                    // Store validated config
                    validatedConfigs[key] = (prop, convertedValue);
                }

                // 4. If there are validation errors, return them all
                if (validationErrors.Count != 0)
                {
                    result.Set(false, $"Validation errors: {string.Join("; ", validationErrors)}");
                    return result;
                }

                // 5. Apply all validated configs (only if all validations passed)
                foreach (var validatedConfig in validatedConfigs)
                {
                    var (prop, convertedValue) = validatedConfig.Value;
                    prop.SetValue(hospital, convertedValue);
                }

                hospital.SetUpdate(_currentUserService.UserName);
                _databaseService.Hospitals.Update(hospital);
                await _databaseService.SaveChangesAsync(cancellationToken);
                
                // 6. Clear hospital cache after update
                var clearHospitalCache = new ClearHospitalCache { HospitalId = request.HospitalId };
                await _mediator.Send(clearHospitalCache, cancellationToken);

                var updatedKeys = string.Join(", ", validatedConfigs.Keys);
                result.Set(true, $"Cập nhật thành công {validatedConfigs.Count} config(s) [{updatedKeys}] cho bệnh viện {request.HospitalId} bởi {_currentUserService.UserName}");
                return result;
            }
            catch (Exception ex)
            {
                result.Set(false, ex.Message);
                return result;
            }
        }
        private static List<string> GetAllowedHospitalConfigFields()
        {
            return [
                "IsActive",
                "IsMaintenanceMode",
                "IsGenQR",
                "IsSkipGetServices",
                "IsSkipGetInsuranceServices",
                "IsAdvancePayment",
                "IsInsuranceAdvancePayment",
                "IsHisIgnoreCreateRegister",
                "IsGenQueueNumberDefault",
                "IsGenQueueNumberByHis",
                "IsNeedAuthenticateBeforeGetQueueNumber",
                "IsPaymentDocumentDefault",
                "IsRegisterDocumentDefault",
                "IsSupportInsuranceDefault",
                "IsRePrintRegisterDocumentDefault",
                "IsReExaminationDefault",
                "IsReExaminationByDoctorDefault",
                "IsRegisterSelfDefault",
                "IsRegisterRelativeDefault",
                "IsAppRegisterCheckInKioskDefault",
                "IsSupportVNeIDDefault",
                "IsParaclinicalExaminationDefault",
                "IsSupportInsuranceInServiceScreenDefault",
                "IsHideInsuranceToggleButton",
                "IsHidePriorityQueueToggleButton",
                "IsAutoCheckAndSelectInsuranceType",
                "IsIgnoreInsurancePayment",
                "IsShowEducationLevel",
                "IsShowWorkPlace",
                "IsShowWorkAddress",
                "LivenessType",
                "KioskVersion",
                "IsSkipInputPhoneNumber",
                "IsHideReferralInsurance",
                "IsInputTransferReferralInfo",
                "IsInputReasonForVisit",
                "DeploymentStatus",
                "DeploymentMethod",
                "IsShowCareer",
                "IsShowSocialCareer",
                "IsShowTransferReferralType",
                "IsShowTransferReferralReason",
                "IsShowTransferReferralDate",
                "IsShowTransferReferralDiagnosisInfo",
                "IsSearchPatientByInsuranceNo",
                "IsAllowSelectNewPatient",
                "IsGenQRWhenCreateRegister",
                "IsShowInputEInvoiceInfo",
                "IsHideChangeAddressField",
                "IsUseExtraFeeAsInsurancePrice",
                "IsAutoSelectObject130",
                "IsShowArvTreatment",
                "AutoLogoutTimeSeconds",
                "Announcement",
                "IsShowBloodPressure",
                "IsShowHeartRate",
                "IsShowRespiratoryRate",
                "IsShowBloodOxygen",
                "IsShowHeight",
                "IsShowWeight",
                "IsShowPulseRate",
                "IsShowTemperature",
                "IsBlockForDuplicatedVisitInsurance",
                "IsAllowInputAdvancePaymentAmount",
                "AdvancePaymentMinAmount",
                "AdvancePaymentMaxAmount",
                "IsAllowCustomerRetryByPatientCode",
                "IsAllowCustomerRetryByInsuranceCode",
                "IsAllowPushQueueInfoToHis",
                "HisVersion",
                "IsBlockInsuranceOnWeekend",
                "IsAllowPaymentGuarantee",
                "IsAllowPaymentCheckIn",
                "IsNewFlow",
                "IsIgnoreCheckInsurance",
                "IsHideQrCodePaymentDefault",
                "IsBlockUnder16YearsOld",
                "IsHealthCheckRegisterDefault",
                "IsAllowSelectMultipleServicesDefault",
                "IsAllowPrintManualDefault",
                "IsAllowShowHospitalMapDefault",
                "IsShowDifferenceInsuranceAndServicePrice",
                "IsActiveConfigExaminationTime",
                "IsAllowBypassAdvancePayment",
                "IsTwoLevelAddress",
                "IsAllowSearchHealthServiceDefault",
                "IsShowAccidentCode",
                "IsShowBloodType",
                "IsInsurancePopupHiddenOnCorrectReferral",
                "IsInsurancePaymentInAppAllowed",
                "IsServicePaymentInAppAllowed",
                "IsInputDiagnosisVisible"
            ];
        }
    }
}
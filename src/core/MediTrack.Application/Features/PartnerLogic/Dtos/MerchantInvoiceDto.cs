﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.PartnerLogic.Dtos
{
    public class MerchantInvoiceDto
    {
        public string? invoiceId { get; set; }
        public string? transactionId { get; set; }
        public string? status { get; set; } 
        public long? paidAmount { get; set; }
        public long transactionAmount { get; set; }
        public string? transactionDescription { get; set; } 
        public string? paidDescription { get; set; } 
        public long? paidTime { get; set; }
        public object? additionalData { get; set; }
        public string patientCode { get; set; } = string.Empty;
    }
}
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.PartnerLogic.Dtos
{
    public class UpdateQRPayment
    {
        public string MerchantId { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public long Amount { get; set; }
        public long PaidAmount { get; set; }
        public string InvoiceId { get; set; } = string.Empty;
        public string PaymentCode { get; set; } = string.Empty;
        public string PaidDescription { get; set; } = string.Empty;
        public string TransactionDescription { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? PaidAt { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.PartnerLogic.Dtos
{
    public class PartnerTransactionDecrypt
    {
        public string merchantId { get; set; } = string.Empty;
        public List<string>? invoiceIds { get; set; }
        public long transactionTime { get; set; }
        public int page { get; set; }
        public int pageSize { get; set; }
    }
}
﻿namespace MediTrack.Application.Features.PartnerLogic.Dtos
{
    public class CreateQRPaymentQRRequest
    {
        public string? merchantId { get; set; }
        public string invoiceId { get; set; } = string.Empty;
        public string type { get; set; } = string.Empty;
        public long transactionAmount { get; set; }
        public object? additionalData { get; set; }
        public long transactionTime { get; set; }
        public int transactionTimeout { get; set; }
        public string ipn { get; set; } = string.Empty;
        public string patientCode { get; set; } = string.Empty;// bắt buộc
        public string serviceCode { get; set; } = string.Empty;//dịch vụ không bắt buộc
        public string? transactionDescription { get; set; }
        public string patientName { get; set; } = string.Empty;

        public string patientDateOfBirth { get; set; } = string.Empty;
        public string patientGender { get; set; } = string.Empty;
        public string clinicName { get; set; } = string.Empty;
        public string cashier { get; set; } = string.Empty;
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.HisLogic.Dtos;
using MediTrack.Application.Features.PartnerLogic.Dtos;
using MediTrack.Application.Features.PaymentLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MediTrack.Ultils.Helpers;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Response;
using PaymentClient.Lib.Request;

namespace MediTrack.Application.Features.PartnerLogic.Commands
{
    public class PartnerUpdateQRPayment : IRequest<BaseCommandResult>
    {
        public string Signature { get; set; } = string.Empty;
        public UpdateQRPayment Data { get; set; } = new();
    }

    public class PartnerUpdateQRPaymentHandler(IDatabaseService databaseService,
              IHttpClientFactory httpClientFactory,
                 IOptions<PaymentConfig> paymentConfig,
        IOptions<EnvironmentConfig> telegramConfig) : IRequestHandler<PartnerUpdateQRPayment, BaseCommandResult>

    {
        private readonly PaymentConfig paymentConfig = paymentConfig.Value;
        private readonly EnvironmentConfig telegramConfig = telegramConfig.Value;

        public async Task<BaseCommandResult> Handle(PartnerUpdateQRPayment request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();
            Log.Information("PartnerUpdateQRPayment - Start processing payment update for invoice " +
                "{InvoiceId} with code {@request}", request.Data.InvoiceId, request);
           
            var payment = await databaseService.Payments
                .Include(x => x.Receipt)
                .Include(x => x.Hospital)
                .FirstOrDefaultAsync(x => x.RefNo == request.Data.PaymentCode && x.Receipt!.RefNo == request.Data.InvoiceId
                && x.Hospital!.MerchantId == request.Data.MerchantId, cancellationToken: cancellationToken);
            if (payment is null)
            {
                Log.Information("{HospitalId} Not found payment on ipn for invoice {InvoiceId}", payment?.HospitalId, request.Data.InvoiceId);
                result.Set(false, PaymentConstant.NotFound);
                return result;
            }
            var hospital = payment.Hospital;

            //1. Check signature and merchant
            var plainText = JsonConvert.SerializeObject(request.Data, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
            });
            var signature = SignatureHelper.HmacSha256HashV2(plainText, paymentConfig.SecretKey);

            Log.Information("{HospitalId} Check signature plain text {PlainText} - Signature: {Signature}", hospital?.Id, plainText, signature);

            if (signature != request.Signature)
            {
                Log.Information("{HospitalId} Invalid signature", payment.Id);
                result.Set(false, signature);
                return result;
            }


            if (hospital?.Id is null)
            {
                Log.Information("{HospitalId} Not found hospital", hospital?.Id);
                result.Set(false, PaymentConstant.NotFound);
                return result;
            }


            string descriptionTele = $"{hospital?.Name} - {payment.Id} - {payment.Receipt!.CustomerId} thanh toán {request.Data.PaidAmount}";
            _ = httpClientFactory.CreateClient().SendAsync(telegramConfig.TelegramAPI, telegramConfig.TelegramChatId, descriptionTele);

            //2. Query and check payment
            if (payment.RefNo != request.Data.PaymentCode)
            {
                Log.Information("{HospitalId} Invalid RefNo", hospital?.Id);
                result.Set(false, PaymentConstant.InvalidPayment);
                return result;
            }

            if (request.Data.MerchantId != (hospital?.MerchantId ?? string.Empty))
            {
                Log.Information("{HospitalId} Invalid Merchant", hospital?.Id);
                result.Set(false, PaymentConstant.InvalidMerchantId);
                return result;
            }

            //3. Update payment
            payment.PaidAmount = request.Data.PaidAmount;

            if (request.Data.Status == "01")
            {
                payment.Status = PaymentConstant.FailPayment;
            }
            else if (request.Data.Status == "00")
            {
                payment.Status = PaymentConstant.Success;

                if (payment.Receipt != null)
                {
                    payment.Receipt.Status = ReceiptConstant.STATUS_PAID;
                }
            }
            else if (request.Data.Status == "10")
            {
                payment.Status = PaymentConstant.Partial;
            }

            payment.Message = request.Data.PaidDescription;
            payment.UpdatedAt = DateTime.UtcNow;
            payment.RefDesc = request.Data.TransactionDescription;
            payment.RefTran = request.Data.TransactionId;
          
            if (!string.IsNullOrEmpty(payment.IpnUrl))
            {
                var config = new PaymentConfigModel()
                {
                    Url = payment.IpnUrl,
                    SecretKey = payment.Hospital!.SecretKey,
                };

                var paymentRequest = new CreateIpnRequest
                {
                    InvoiceId = payment.Receipt?.RefNo ?? string.Empty,
                    MerchantId = payment.Hospital.MerchantId,
                    PaidAmount = payment.PaidAmount,
                    PaidDescription = payment.RefDesc,
                    Status = request.Data.Status,
                    TransactionAmount = payment.PaymentAmount,
                    TransactionId = request.Data.TransactionId,
                    PaidTime = request.Data.PaidAt.GetValueOrDefault().ToString("yyyyMMddHHmmss"),
                    TransactionDescription = request.Data.TransactionDescription
                };

                (bool processResult, string processMessage, string? res, string? logRequest) = await
                    PaymentClient.Lib.PaymentClient.CreateIpn(httpClientFactory.CreateClient(), paymentRequest, config);
                config.Dispose();

                payment.IpnMessage = processMessage;
                payment.IpnStatus = processResult ? "SUCCESS" : "FAIL";
                payment.RequestToPartner = logRequest;
                payment.ResponseFromPartner = res;

                Log.Information("{HospitalId} Call Partner Ipn Res {Result} - Message {Message} - Response {Response}", hospital?.Id, processResult, processMessage, res);

            }




            //5. Cập nhật trạng thái sau khi IPN


            databaseService.Payments.Update(payment);

            var updatePaymentResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (updatePaymentResult < 1)
            {
                Log.Information("{HospitalId} Update Partner Ipn Fail", hospital?.Id);
                result.Set(false, PaymentConstant.UpdateIpnFail);
                return result;
            }

            result.Set(true, PaymentConstant.Ok);

            return result;
        }
    }
}
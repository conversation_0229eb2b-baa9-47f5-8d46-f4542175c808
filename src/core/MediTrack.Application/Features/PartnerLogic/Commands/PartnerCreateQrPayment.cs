﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Configs;
using MediTrack.Application.Features.PartnerLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.Extensions.Options;
using PaymentClient.Lib.Config;
using PaymentClient.Lib.Request;
using PaymentClient.Lib.Response;
using Serilog;

namespace MediTrack.Application.Features.PartnerLogic.Commands
{
    public class PartnerCreateQrPayment : PaymentEncryptDataDto, IRequest<BaseResponseModel<CreateQRPaymentQRDto>>
    {
    }

    public class PartnerCreateQrPaymentHandler(IDatabaseService databaseService,
           IOptions<PaymentPartnerConfig> paymentConfig,
            IHttpClientFactory httpClientFactory) : IRequestHandler<PartnerCreateQrPayment, BaseResponseModel<CreateQRPaymentQRDto>>
    {
        private readonly PaymentPartnerConfig paymentConfig = paymentConfig.Value;

        public async Task<BaseResponseModel<CreateQRPaymentQRDto>> Handle(PartnerCreateQrPayment request, CancellationToken cancellationToken)
        {
            var decryptData = Base64Helper.Base64Decode(request.data);
            var paymentQRReq = Newtonsoft.Json.JsonConvert.DeserializeObject<CreateQRPaymentQRRequest>(decryptData);
            if (paymentQRReq == null)
            {
                return new BaseResponseModel<CreateQRPaymentQRDto>
                {
                    Message = "data không hợp lệ",
                    Code = ErrorConstant.INVALID_DATA
                };
            }
            var verify = PartnerHelper.VerifyRequestPayment(paymentQRReq);
            if (!verify.result)
                return new BaseResponseModel<CreateQRPaymentQRDto>
                {
                    Code = ErrorConstant.INVALID_DATA,
                    Message = verify.message
                };
            var hospital = await databaseService.Hospitals.FindAsync(paymentQRReq.merchantId);
            if (hospital == null)
                return new BaseResponseModel<CreateQRPaymentQRDto>
                {
                    Code = ErrorConstant.INVALID_CLIENT,
                    Message = "Không tìm thấy thông tin merchant"
                };

            if (!SignatureHelper.HmacSha256HashV2(request.data, hospital.SecretKey).Equals(request.sign))
                return new BaseResponseModel<CreateQRPaymentQRDto>
                {
                    Message = "sign không hợp lệ",
                    Code = ErrorConstant.INVALID_SIGN
                };

            var receipt = new Receipt
            {
                AdditionalData = paymentQRReq.additionalData,
                TotalAmount = paymentQRReq.transactionAmount,
                CreatedAt = DateTime.UtcNow,
                HospitalId = hospital.Id,
                RefNo = paymentQRReq.invoiceId,
                ServiceNameRef = paymentQRReq.serviceCode,
                PatientCodeRef = paymentQRReq.patientCode,
                PatientNameRef = paymentQRReq.patientName,
                PatientDateOfBirthRef = paymentQRReq.patientDateOfBirth,
                PatientGenderRef = paymentQRReq.patientGender,
                ClinicNameRef = paymentQRReq.clinicName,
                CashierRef = paymentQRReq.cashier,
                Number = IdentityHelper.Guid(15),
                CreatedFrom = ReceiptConstant.CREATED_FROM_PARTNER,
                ReceiptDate = DateTime.UtcNow,
                ReceiptType =  "PARTNER"
            };

            if (!string.IsNullOrEmpty(paymentQRReq.transactionDescription))
            {
                paymentQRReq.transactionDescription = paymentQRReq.transactionDescription.Length > paymentConfig.LenghtDescription
                              ? null : PartnerHelper.PaymentDecription(paymentQRReq.transactionDescription);
            }

            var paymentRequest = new CreateQrRequest()
            {
                MerchantId = hospital.MerchantId,
                InvoiceId = paymentQRReq.invoiceId,
                Type = PaymentConstant.DefaultType,
                TransactionAmount = paymentQRReq.transactionAmount,
                Ipn = paymentConfig.IpnUrl,
                TransactionDescription = paymentQRReq.transactionDescription ?? string.Empty,
            };

            var config = new PaymentConfigModel()
            {
                Url = paymentConfig.PaymentUrl,
                SecretKey = paymentConfig.SecretKey
            };
            Log.Information("{LogPrefix} PartnerCreateQrPaymentHandler Req: {@Request}", paymentQRReq.merchantId, paymentRequest);
            (bool processResult, string processMessage, CreateQrResponse? res) = await
                PaymentClient.Lib.PaymentClient.CreateQr(httpClientFactory.CreateClient(), paymentRequest, config);
            config.Dispose();

            if (processResult)
            {
                receipt.Status = ReceiptConstant.STATUS_NEW;
                var payment = new Payment()
                {
                    Id = IdentityHelper.Guid(10),
                    PaymentDate = DateTime.UtcNow,
                    PaymentAmount = receipt.TotalAmount,
                    ReceiptNumber = receipt.Number,
                    CreatedAt = DateTime.UtcNow,
                    HospitalId = hospital.Id,
                    QrCode = res!.QrCode,
                    RefNo = res!.PaymentCode,
                    Status = PaymentConstant.WaitForPayment,
                    RefDescReq = paymentQRReq.transactionDescription,
                    RefDesc = res.TransactionDescription,
                    IpnUrl = paymentQRReq.ipn,
                };

                receipt.QrCode = payment.QrCode;

                databaseService.Receipts.Add(receipt);
                databaseService.Payments.Add(payment);

                var insertResult = await databaseService.SaveChangesAsync(cancellationToken);

                return
                     new BaseResponseModel<CreateQRPaymentQRDto>
                     {
                         Code = insertResult > 0 ? ErrorConstant.SUCCESS : ErrorConstant.UNKNOWN,
                         Message = processMessage,
                         Data = new CreateQRPaymentQRDto
                         {
                             qrCode = res.QrCode,
                             transactionDescription = res.TransactionDescription,
                         }
                     };
            }

            return new BaseResponseModel<CreateQRPaymentQRDto>
            {
                Code = ErrorConstant.UNKNOWN,
                Message = processMessage
            };
        }
    }
}
﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.PartnerLogic.Dtos;
using MediTrack.Application.Helpers;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Web;

namespace MediTrack.Application.Features.PartnerLogic.Queries
{
    public class PartnerTransactionQuery : PaymentEncryptDataDto, IRequest<BaseResponseModel<List<MerchantInvoiceDto>>>
    {
    }

    public class PartnerTransactionQueryHandler(IDatabaseService databaseService) :
        IRequestHandler<PartnerTransactionQuery, BaseResponseModel<List<MerchantInvoiceDto>>>
    {
        public async Task<BaseResponseModel<List<MerchantInvoiceDto>>> Handle(PartnerTransactionQuery request, CancellationToken cancellationToken)
        {
            var data = HttpUtility.UrlDecode(request.data);
            var result = new List<MerchantInvoiceDto>();
            var base64Decode = Base64Helper.Base64Decode(data);
            var dataMerchantRequest = JsonConvert.DeserializeObject<PartnerTransactionDecrypt>(base64Decode);
            if (dataMerchantRequest == null)
            {
                return new BaseResponseModel<List<MerchantInvoiceDto>>
                {
                    Message = "data không hợp lệ",
                    Code = ErrorConstant.INVALID_DATA
                };
            }
            var verify = PartnerHelper.VerifyRequestSearchPayment(dataMerchantRequest);
            if (!verify.result)
                return new BaseResponseModel<List<MerchantInvoiceDto>>
                {
                    Code = ErrorConstant.INVALID_DATA,
                    Message = verify.message
                };
            var hospital = await databaseService.Hospitals.FindAsync([dataMerchantRequest.merchantId], cancellationToken);
            if (hospital == null)
                return new BaseResponseModel<List<MerchantInvoiceDto>>
                {
                    Code = ErrorConstant.INVALID_CLIENT,
                    Message = "Không tìm thấy thông tin merchant"
                };

            if (!SignatureHelper.HmacSha256HashV2(request.data, hospital.SecretKey).Equals(request.sign))
                return new BaseResponseModel<List<MerchantInvoiceDto>>
                {
                    Message = "sign không hợp lệ",
                    Code = ErrorConstant.INVALID_SIGN
                };

            var receipts = await databaseService.Receipts.Include(x => x.Payment)
                .Where(x => x.HospitalId == dataMerchantRequest.merchantId
                && dataMerchantRequest.invoiceIds!.Contains(x.RefNo ?? string.Empty)
                &&x.CreatedFrom == ReceiptConstant.CREATED_FROM_PARTNER
                ).ToListAsync(cancellationToken: cancellationToken);

            foreach (var receipt in receipts)
            {
                var paymentTransaction = receipt.Payment?.FirstOrDefault();

                result.Add(new MerchantInvoiceDto
                {
                    invoiceId = receipt.RefNo,
                    transactionAmount = (long)receipt.TotalAmount,
                    transactionId = receipt.Status?.Equals(ReceiptConstant.STATUS_PAID) == true ? receipt.Number.ToString() : null,
                    additionalData = receipt.AdditionalData,
                    status = PaymentConstant.StatusSwitchCode(receipt.Status ?? string.Empty),
                    paidAmount = paymentTransaction != null ? (long)paymentTransaction.PaidAmount : 0,
                    paidDescription = paymentTransaction?.RefDesc ?? string.Empty,
                    paidTime = paymentTransaction?.UpdatedAt.HasValue == true ? new DateTimeOffset(paymentTransaction.UpdatedAt.Value).ToUnixTimeMilliseconds() : null,
                    patientCode = receipt.PatientCodeRef,
                });
            }
            return new BaseResponseModel<List<MerchantInvoiceDto>>
            {
                Data = result,
                Code = ErrorConstant.SUCCESS
            };
        }
    }
}
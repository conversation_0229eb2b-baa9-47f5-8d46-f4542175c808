﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.NationalityLogic.Queries
{
    public class GetNationality : IRequest<BaseCommandResultWithData<Nationality>>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class GetNationalityHandler(IDatabaseService databaseService,
        ICachedService cachedService) : IRequestHandler<GetNationality, BaseCommandResultWithData<Nationality>>
    {
        private readonly IDatabaseService databaseService = databaseService;
        private readonly ICachedService cachedService = cachedService;

        public async Task<BaseCommandResultWithData<Nationality>> Handle(
            GetNationality request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Nationality>();

            List<Nationality>? nationalities = await cachedService.GetAsync<List<Nationality>>("Nationalities", cancellationToken);

            if(nationalities is null)
            {
                nationalities = await databaseService.Nationalities.ToListAsync(cancellationToken);
                await cachedService.SetAsync("Nationalities", nationalities, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            var nationality = nationalities.FirstOrDefault(x => x.Id == request.Id);
            if(nationality is not null)
            {
                result.Set(true, NationalityConstant.Ok, nationality);
            }
            else
            {
                result.Set(false, NationalityConstant.NotFound);
            }

            return result;
        }
    }
}

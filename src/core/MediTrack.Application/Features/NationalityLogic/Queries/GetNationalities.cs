﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.NationalityLogic.Queries
{
    public class GetNationalities : IRequest<BaseCommandResultWithData<IEnumerable<Nationality>>>
    {

    }

    public class GetNationalitiesHandler(
        IDatabaseService databaseService,
        ICachedService cachedService) : IRequestHandler<GetNationalities, BaseCommandResultWithData<IEnumerable<Nationality>>>
    {

        public async Task<BaseCommandResultWithData<IEnumerable<Nationality>>> Handle(
            GetNationalities request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<Nationality>>();

            List<Nationality>? nationalities = await cachedService.GetAsync<List<Nationality>>("Nationalities", cancellationToken);

            if (nationalities is null || nationalities.Count == 0)
            {
                nationalities = await databaseService.Nationalities.ToListAsync(cancellationToken: cancellationToken);
                await cachedService.SetAsync("Nationalities", nationalities, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            result.Set(true, NationalityConstant.Ok, nationalities);

            return result;
        }
    }
}

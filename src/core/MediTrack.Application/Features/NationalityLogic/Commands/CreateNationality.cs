﻿using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.NationalityLogic.Commands
{
    public class CreateNationality : IRequest<BaseCommandResultWithData<Nationality>>
    {
        public string Id { get; set; } = string.Empty;
        public string? Name { get; set; }
        public string? OtherName { get; set; }
    }

    public class CreateNationalityHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IIdentityService identityService,
        ICachedService cachedService) : IRequestHandler<CreateNationality, BaseCommandResultWithData<Nationality>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IIdentityService identityService = identityService;
        private readonly ICachedService cachedService = cachedService;

        public async Task<BaseCommandResultWithData<Nationality>> Handle(
            CreateNationality request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<Nationality>();

            if (string.IsNullOrEmpty(currentUserService.UserName))
            {
                result.Set(false, NationalityConstant.Forbidden);
                return result;
            }

            var nationality = request.Adapt<Nationality>();
            nationality.SetCreate(currentUserService.UserName);

            databaseService.Nationalities.Add(nationality);

            var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

            if (saveResult > 0)
            {
                result.Set(true, NationalityConstant.SaveChangesSuccess, nationality);
                await cachedService.RemoveByPrefixAsync("Nationality");
            }
            else
            {
                result.Set(false, NationalityConstant.SaveChangesError);
            }

            return result;
        }
    }
}

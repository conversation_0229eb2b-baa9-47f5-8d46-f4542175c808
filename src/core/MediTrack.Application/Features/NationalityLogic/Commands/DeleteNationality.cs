﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.NationalityLogic.Commands
{
    public class DeleteNationality : IRequest<BaseCommandResult>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class DeleteNationalityHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IIdentityService identityService,
        ICachedService cachedService) : IRequestHandler<DeleteNationality, BaseCommandResult>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IIdentityService identityService = identityService;
        private readonly ICachedService cachedService = cachedService;

        public async Task<BaseCommandResult> Handle(DeleteNationality request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResult();

            var nationality = await databaseService.Nationalities
                .FirstOrDefaultAsync(x => x.Id == request.Id);

            if(nationality is not null)
            {
                nationality.IsDeleted = true;
                nationality.SetUpdate(currentUserService.UserName);

                databaseService.Nationalities.Update(nationality);

                var saveResult = await databaseService.SaveChangesAsync(cancellationToken);

                if (saveResult > 0)
                {
                    result.Set(true, NationalityConstant.SaveChangesSuccess);
                    await cachedService.RemoveByPrefixAsync("Nationality");
                }
                else
                {
                    result.Set(false, NationalityConstant.SaveChangesError);
                }
            }
            else
            {
                result.Set(false, NationalityConstant.NotFound);
            }

            return result;
        }
    }
}

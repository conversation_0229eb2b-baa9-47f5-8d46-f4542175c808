namespace MediTrack.Application.Features.HealthServiceLogic.Dtos
{
    public class GetHealthServicesDto
    {
        public string? ExameTypeId { get; set; } = null;
        public string? ClinicId { get; set; } = null;
        public string? SubClinicId { get; set; } = null;
        public string? ClinicCode { get; set; } = null;
        public string? HealthInsuranceNo { get; set; } = null;
        public string? KioskId { get; set; } = null;
        public string? HospitalId { get; set; } = null;
    }
}
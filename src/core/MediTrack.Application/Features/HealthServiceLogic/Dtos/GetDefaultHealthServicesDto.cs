﻿namespace MediTrack.Application.Features.HealthServiceLogic.Dtos
{
    public class GetDefaultHealthServiceRequest
    {
        public string codeMetaData { get; set; } = string.Empty;
        public string clinicId { get; set; } = string.Empty;
        public string exameTypeId { get; set; } = string.Empty;
        public string subClinicId { get; set; } = string.Empty;
        public string clinicCode { get; set; } = string.Empty;
        public string kioskId { get; set; } = string.Empty;
        public bool isGetAll { get; set; } = false;
    }
}
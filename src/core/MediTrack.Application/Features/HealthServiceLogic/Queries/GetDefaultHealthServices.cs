﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HealthServiceLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Serilog;

namespace MediTrack.Application.Features.HealthServiceLogic.Queries
{
    public class GetDefaultHealthServices : IRequest<BaseCommandResultWithData<List<HealthService>>>
    {
        public bool IsInsurance { get; set; }
        public string ClinicId { get; set; } = string.Empty;
        public string ExameTypeId { get; set; } = string.Empty;
        public string SubClinicId { get; set; } = string.Empty;
        public string ClinicCode { get; set; } = string.Empty;
        public bool IsGetAll { get; set; } = false;
    }

    public class GetDefaultHealthServicesHandler(
            ICurrentHospitalService currentHospitalService

           ) : IRequestHandler<GetDefaultHealthServices, BaseCommandResultWithData<List<HealthService>>>

    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<List<HealthService>>> Handle(
            GetDefaultHealthServices request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HealthService>>();

            try
            {
                string code = "DEFAULT_SERVICES";
                if (request.IsInsurance)
                {
                    code = "DEFAULT_INSURANCE_SERVICES";
                }

                var healthServices = await hisService.GetDefaultHealthServices(
                new GetDefaultHealthServiceRequest
                {
                    codeMetaData = code,
                    exameTypeId = request.ExameTypeId,
                    clinicId = request.ClinicId,
                    subClinicId = request.SubClinicId,
                    kioskId = currentHospitalService.KioskId,
                    clinicCode = request.ClinicCode,
                    isGetAll = request.IsGetAll
                });


                healthServices.ForEach(x =>
                {
                    x.ClinicId = string.IsNullOrEmpty(x.ClinicId) ? request.ClinicId : x.ClinicId;
                    x.ExameTypeId = string.IsNullOrEmpty(x.ExameTypeId) ? request.ExameTypeId : x.ExameTypeId;
                    x.SubClinicId = string.IsNullOrEmpty(x.SubClinicId) ? request.SubClinicId : x.SubClinicId;
                    x.ClinicCode = string.IsNullOrEmpty(x.ClinicCode) ? request.ClinicCode : x.ClinicCode;
                    x.IsIgnoreInsurancePayment = currentHospitalService.CurrentHospital.IsIgnoreInsurancePayment;
                });

                result.Set(true, ClinicConstant.Ok, healthServices);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when parse default services", currentHospitalService.LogPrefix);
                result.Set(false, ClinicConstant.NotFound);
            }

            return result;
        }
    }
}
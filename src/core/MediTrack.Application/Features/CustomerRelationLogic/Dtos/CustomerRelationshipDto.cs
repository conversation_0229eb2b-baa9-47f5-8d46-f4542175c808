﻿namespace MediTrack.Application.Features.CustomerRelationLogic.Dtos
{
    public class CustomerRelationshipDto
    {
        public string? CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public string? RelationshipName { get; set; }
        public string? DateOfBirthday { get; set; }
        public string? Phone { get; set; } = string.Empty;
        public string? Gender { get; set; }
        public string? IdentityNo { get; set; }
        public string? HealthInsuranceNo { get; set; }
        public string? NationalityId { get; set; }
        public string? AccountStatus { get; set; }
        public string? Address { get; set; }
        public string? Street { get; set; }
        public string? WardId { get; set; }
        public string? DistrictId { get; set; }
        public string? ProvinceId { get; set; }
    }
}
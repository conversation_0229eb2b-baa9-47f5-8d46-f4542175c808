﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.CustomerRelationLogic.Queries
{
    public class CustomerRelationshipQuery : IRequest<BaseResponseModel<List<CustomerRelationshipDto>>>
    {
        public string CustomerId { get; set; } = string.Empty;
    }

    public class CustomerRelationshipCommandHandler(IDatabaseService databaseService) : IRequestHandler<CustomerRelationshipQuery, BaseResponseModel<List<CustomerRelationshipDto>>>
    {
        public async Task<BaseResponseModel<List<CustomerRelationshipDto>>> Handle(CustomerRelationshipQuery request, CancellationToken cancellationToken)
        {
            var customerRelation = await databaseService.CustomerRelationShips
                .Where(x => x.CustomerId == request.CustomerId)
                .Include(x => x.Customer)
                .Include (x => x.Relationship)
                .ToListAsync();

            if (customerRelation.Count == 0)
            {
                return new BaseResponseModel<List<CustomerRelationshipDto>>
                {
                    Code = ErrorConstant.SUCCESS,
                    Data = []
                };
            }

            var customerRelationIds = customerRelation.Select(x => x.CustomerIdRefer).ToList();
            var customerRelations = await databaseService.Customers
                .Where(x => customerRelationIds.Contains(x.Id)).ToListAsync();

            if (customerRelations.Count == 0)
            {
                return new BaseResponseModel<List<CustomerRelationshipDto>>
                {
                    Code = ErrorConstant.SUCCESS,
                    Data = []
                };
            }

            var result = customerRelations.Select(x => new CustomerRelationshipDto
            {
                CustomerId = x.Id,
                CustomerName = x.GetFullName(),
                DateOfBirthday = x.DateOfBirth?.ToString("dd/MM/yyyy") ?? string.Empty,
                Gender = x.Sex,
                Phone = x.Phone,
                IdentityNo = x.IdentityNo,
                AccountStatus = x.AccountStatus,
                HealthInsuranceNo = x.HealthInsuranceNo,
                RelationshipName = customerRelation.Where(y => y.CustomerIdRefer == x.Id).FirstOrDefault()?.Relationship?.Name,
                NationalityId = x.NationalityId,
                Address = x.Address ?? string.Empty,
                Street = x.Street ?? string.Empty,
                WardId = x.WardId,
                DistrictId = x.DistrictId,
                ProvinceId = x.ProvinceId
            }).ToList();

            return new BaseResponseModel<List<CustomerRelationshipDto>>
            {
                Code = ErrorConstant.SUCCESS,
                Data = result
            };
        }
    }
}
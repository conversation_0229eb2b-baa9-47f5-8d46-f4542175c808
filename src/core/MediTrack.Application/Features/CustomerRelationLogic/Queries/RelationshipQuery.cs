﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.CustomerRelationLogic.Queries
{
    public class RelationshipQuery : IRequest<BaseResponseModel<List<RelationshipDto>>>
    {
    }
    public class Handler(IDatabaseService databaseService, ICachedService cachedService, ICurrentHospitalService currentHospitalService, IHospitalMetadataRepository hospitalMetadata) : IRequestHandler<RelationshipQuery, BaseResponseModel<List<RelationshipDto>>>
    {
        public async Task<BaseResponseModel<List<RelationshipDto>>> Handle(RelationshipQuery request, CancellationToken cancellationToken)
        {
            var hospitalId = currentHospitalService.CurrentHospital.HospitalId;
            List<RelationshipDto>? result = await cachedService.GetAsync<List<RelationshipDto>>("Relationships" + hospitalId, cancellationToken);

            if (result is null || result.Count == 0)
            {
                var metadata = await hospitalMetadata.GetHospitalMetadataByKeyAsync(hospitalId, "id_quan_he_nt_map", cancellationToken: cancellationToken);
                if (metadata != null && !string.IsNullOrEmpty(metadata.Value))
                {
                    var listRelationship = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(metadata.Value);
                    result = listRelationship?.DistinctBy(x => x.Id).Select(x => new RelationshipDto
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Gender = x.Gender,
                    }).ToList() ?? [];
                }
                else
                {
                    result = await databaseService.Relationships.Select(x => new RelationshipDto
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Gender = x.Gender,
                    }).ToListAsync(cancellationToken);
                }

                await cachedService.SetAsync("Relationships" + hospitalId, result, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
            }

            return new BaseResponseModel<List<RelationshipDto>>
            {
                Code = ErrorConstant.SUCCESS,
                Data = result
            };
        }
    }
}

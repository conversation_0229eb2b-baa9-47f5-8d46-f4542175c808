﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CustomerRelationLogic.Dtos;
using MediTrack.Application.Features.DistrictLogic.Queries;
using MediTrack.Application.Features.ProvinceLogic.Queries;
using MediTrack.Application.Features.WardLogic.Queries;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediTrack.Domain.Helpers;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.CustomerRelationLogic.Commands
{
    public class CreateCustomerRelation : IRequest<BaseResponseModel<object>>
    {
        public string CustomerId { get; set; } = string.Empty;
        public string FullNameRelation { get; set; } = string.Empty;
        public string IdentityRelation { get; set; } = string.Empty;
        public string? IdentityIssuePlaceRelation { get; set; }
        public DateTime? IdentityIssueDateRelation { get; set; }
        public string HealthInsuranceNo { get; set; } = string.Empty;
        public string PhoneRelation { get; set; } = string.Empty;
        public string RelationId { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string Street { get; set; } = string.Empty;
        public string WardId { get; set; } = string.Empty;
        public string DistrictId { get; set; } = string.Empty;
        public string ProvinceId { get; set; } = string.Empty;
        public string NationalityId { get; set; } = string.Empty;
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class Handler(IDatabaseService databaseService,
        IProvinceRepository provinceRepository,
        IDistrictRepository districtRepository,
        IWardRepository wardRepository,
        ICurrentHospitalService currentHospitalService,
        IHospitalMetadataRepository hospitalMetadata) : IRequestHandler<CreateCustomerRelation, BaseResponseModel<object>>
    {
        public async Task<BaseResponseModel<object>> Handle(CreateCustomerRelation request, CancellationToken cancellationToken)
        {
            var customer = await databaseService.Customers.FindAsync([request.CustomerId], cancellationToken);
            if (customer == null)
                return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Không tìm thấy thông tin khách hàng" };

            Relationship? originReplationship = null;
            var metadata = await hospitalMetadata.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "id_quan_he_nt_map", cancellationToken: cancellationToken);
            if (metadata != null && !string.IsNullOrEmpty(metadata.Value))
            {
                var listRelationship = JsonConvert.DeserializeObject<List<HisRelationshipDto>>(metadata.Value);
                string? originReplationshipId = listRelationship?.FirstOrDefault(x => x.Id == request.RelationId)?.OriginId;
                if (string.IsNullOrEmpty(originReplationshipId))
                    return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Không tìm thấy mối quan hệ" };

                originReplationship = await databaseService.Relationships.FindAsync([originReplationshipId], cancellationToken);
            }
            else
            {
                originReplationship = await databaseService.Relationships.FindAsync([request.RelationId], cancellationToken);
            }

            if (originReplationship == null)
                return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Không tìm thấy mối quan hệ" };

            string customerId = IdentityHelper.Guid(10);
            (string firstName, string lastName) = CustomerHelper.GetVietnameseNameParts(request.FullNameRelation);

            string? provinceId = request.ProvinceId;
            string? districtId = request.DistrictId;
            string? wardId = request.WardId;
            string? street = request.Street;
            string address = string.Empty;

            //Bệnh viện đi theo luồng địa chỉ 2 cấp
            var isHospitalTwoLevelAddress = currentHospitalService.CurrentHospital.IsTwoLevelAddress;

            // Nếu bệnh viện không đi theo luồng địa chỉ 2 cấp thì người dùng không thể gửi địa chỉ 2 cấp
            if (!isHospitalTwoLevelAddress)
            {
                request.IsTwoLevelAddress = false;
            }

            // Nếu người dùng không gửi thông tin địa chỉ 2 cấp thì mặc định là false
            var isInputTwoLevelAddress = request.IsTwoLevelAddress ?? false;

            //nếu mã tỉnh được nhập thì kiểm tra mã tỉnh tồn tại
            if (!string.IsNullOrEmpty(provinceId))
            {
                //2. Prepare data
                var provinces = await provinceRepository.GetProvincedAsync(cancellationToken);

                // kiểm tra mã tỉnh tồn tại
                var province = provinces.FirstOrDefault(p => (isInputTwoLevelAddress ? p.NewId : p.Id) == provinceId);
                if (province == null)
                {
                    return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Mã tỉnh không tồn tại!" };
                }

                provinceId = isHospitalTwoLevelAddress ? province.NewId : province.Id;
                address = isHospitalTwoLevelAddress ? province.NewName : province.Name;
            }

            //Nếu bệnh viện đi theo luồng địa chỉ 2 cấp thì không cần kiểm tra quận huyện
            if (isHospitalTwoLevelAddress)
            {
                districtId = string.Empty;
            }
            // Nếu theo địa chỉ 3 cấp thì kiểm tra quận huyện
            else
            {
                var districts = await districtRepository.GetDistrictByParentIdAsync(request.ProvinceId ?? string.Empty, cancellationToken);

                var district = districts.FirstOrDefault(d => d.Id == districtId);
                if (district == null)
                {
                    return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Mã quận huyện không tồn tại!" };
                }

                address = district.Name + ", " + address;
            }

            // nếu mã phường xã không được nhập thì trả về lỗi
            if (!string.IsNullOrEmpty(wardId))
            {
                // Nếu bệnh nhân nhập địa chỉ 2 cấp thì lấy danh sách quận huyện theo tỉnh
                var wardParentId = isInputTwoLevelAddress ? request.ProvinceId : request.DistrictId;
                var wards = await wardRepository.GetWardByParentIdAsync(wardParentId, isInputTwoLevelAddress, cancellationToken);

                if (wards != null && wards.Count > 0)
                {
                    // kiểm tra mã phường xã tồn tại
                    var ward = wards!.FirstOrDefault(w => (isInputTwoLevelAddress ? w.NewId : w.Id) == wardId);
                    if (ward == null)
                    {
                        return new BaseResponseModel<object> { Code = ErrorConstant.INVALID_DATA, Message = "Mã phường xã không tồn tại!" };
                    }

                    wardId = isHospitalTwoLevelAddress ? ward.NewId : ward.Id;
                    address = ward.Name + ", " + address;
                }
            }

            if (!string.IsNullOrEmpty(street))
            {
                address = street + ", " + address;
            }

            // Nếu không truyền căn cước công dân khi tạo người thân
            if (string.IsNullOrEmpty(request.IdentityRelation))
            {
                var createCustomerRelationship = new Customer
                {
                    FirstName = firstName,
                    LastName = lastName,
                    Phone = request.PhoneRelation,
                    AccountStatus = "S01",
                    DateOfBirth = request.DateOfBirth,
                    IdentityNo = request.IdentityRelation,
                    HealthInsuranceNo = request.HealthInsuranceNo,
                    Nation = "000", // Việt Nam
                    CareerId = "00000", // Không nghề nghiệp
                    NationalityId = request.NationalityId,
                    Sex = request.Gender,
                    Id = customerId,
                    Street = request.Street,
                    WardId = wardId,
                    DistrictId = districtId,
                    ProvinceId = provinceId,
                    Address = address,
                    IsSyncedTwoLevelAddress = isHospitalTwoLevelAddress
                };
                databaseService.Customers.Add(createCustomerRelationship);
            }
            else // Nếu truyền căn cước công dân khi tạo người thân
            {
                var customerIdentity = await databaseService.Customers.FirstOrDefaultAsync(x => x.IdentityNo == request.IdentityRelation, cancellationToken);
                // Nếu người thân đã tồn tại
                if (customerIdentity != null)
                {
                    customerId = customerIdentity.Id;
                }
                else // Nếu người thân chưa tồn tại
                {
                    var createCustomerRelationship = new Customer
                    {
                        FirstName = firstName,
                        LastName = lastName,
                        Phone = request.PhoneRelation,
                        AccountStatus = "S01",
                        DateOfBirth = request.DateOfBirth,
                        IdentityNo = request.IdentityRelation,
                        IdentityIssuePlace = request.IdentityIssuePlaceRelation,
                        IdentityIssueDate = request.IdentityIssueDateRelation,
                        HealthInsuranceNo = request.HealthInsuranceNo,
                        Nation = "000", // Việt Nam
                        CareerId = "00000", // Không nghề nghiệp
                        NationalityId = request.NationalityId,
                        Sex = request.Gender,
                        Id = customerId,
                        Street = request.Street,
                        WardId = wardId,
                        DistrictId = districtId,
                        ProvinceId = provinceId,
                        Address = address,
                        IsSyncedTwoLevelAddress = isHospitalTwoLevelAddress
                    };

                    databaseService.Customers.Add(createCustomerRelationship);
                }
            }

            // check quan hệ đã tồn tại
            var found = await databaseService.CustomerRelationShips
                .FirstOrDefaultAsync(x => (x.CustomerId == customer.Id && x.CustomerIdRefer == customerId)
                    || (x.CustomerId == customerId && x.CustomerIdRefer == customer.Id), cancellationToken);
            if (found != null)
            {
                return new BaseResponseModel<object> { Code = ErrorConstant.UNKNOW, Message = "Quan hệ đã tồn tại" };
            }
            ;

            databaseService.CustomerRelationShips.AddRange(new List<CustomerRelationShip>
            {
                new() {
                    CustomerId = customer.Id,
                    CustomerIdRefer = customerId,
                    RelationshipId = originReplationship.Id,
                },
                new() {
                    CustomerId = customerId,
                    CustomerIdRefer = customer.Id,
                    RelationshipId = originReplationship.ReferId,
                },
            });

            var saveResultCustomerRelationship = await databaseService.SaveChangesAsync(cancellationToken);

            if (saveResultCustomerRelationship == 0)
                return new BaseResponseModel<object> { Code = ErrorConstant.UNKNOW, Message = "Save db thất bại" };

            return new BaseResponseModel<object>
            {
                Code = ErrorConstant.SUCCESS,
                Message = "Thêm thành viên thành công"
            };
        }
    }
}
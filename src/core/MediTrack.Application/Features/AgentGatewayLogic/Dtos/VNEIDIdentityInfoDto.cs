﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MediTrack.Application.Features.AgentGatewayLogic.Dtos
{
    public class VNEIDIdentityInfo
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = ObjectId.GenerateNewId().ToString();
        public string? Session { get; set; }
        public int? Code { get; set; }
        public string? FirstFace { get; set; }
        public string? LastFace { get; set; }
        public string? FirstFaceCropped { get; set; }
        public string? LastFaceCropped { get; set; }
        public string? Signature { get; set; }
        public string? FaceImages { get; set; }
    }
    public class SaveIdentityRequest
    {
        public string? Session { get; set; }
        public int? Code { get; set; }
        public string? FirstFace { get; set; }
        public string? LastFace { get; set; }
        public string? FirstFaceCropped { get; set; }
        public string? LastFaceCropped { get; set; }
        public string? Signature { get; set; }
        public string? FaceImages { get; set; }
    }
    public class GetIdentityRequest
    {
        public string? Id { get; set; }
        public string? Session { get; set; }
        public string? FaceImages { get; set; }
    }
}

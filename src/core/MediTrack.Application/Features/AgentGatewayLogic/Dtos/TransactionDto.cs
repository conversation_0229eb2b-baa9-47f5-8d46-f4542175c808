﻿namespace MediTrack.Application.Features.AgentGatewayLogic.Dtos
{
    public class InitTransactionResponseDto
    {
        public string? TxnId { get; set; }
        public string? RequestId { get; set; }
    }

    public class GetTransactionResponseDto
    {
        public string? Message { get; set; }
        public string? BiometricResult { get; set; }
    }
    public class UserShareInfoDataResponseDto
    {
        public string? livingPlaceCityText { get; set; }
        public string? livingPlaceDistrictText { get; set; }
        public string? permanentCityText { get; set; }
        public string? permanentDistrictText { get; set; }
        public string? permanentVillageText { get; set; }
        public string? livingPlaceVillageText { get; set; }
        public string? citizenPid { get; set; }
        public string? fullName { get; set; }
        public string? birthDate { get; set; }
        public string? nationalityCode { get; set; }
        public string? permanentCityCode { get; set; }
        public string? permanentDistrictCode { get; set; }
        public string? permanentVillageCode { get; set; }
        public string? permanentAddress { get; set; }
        public string? livingPlaceCityCode { get; set; }
        public string? livingPlaceDistrictCode { get; set; }
        public string? livingPlaceVillageCode { get; set; }
        public string? livingPlaceAddress { get; set; }
        public string? dateOfIssue { get; set; }
        public string? idCardExpireDate { get; set; }
        public string? issuingAuthority { get; set; }
        public string? faceImage { get; set; }
    }
    public class UserSharedResponseDto
    {
        public string? ResultCode { get; set; }
        public string? ResultDesc { get; set; }
    }

    public class UserShareInfoResponseDto
    {
        public string Id { get; set; } = string.Empty;
        public string? TxnId { get; set; }
        public string? IndentityNo { get; set; }
        public string? Result { get; set; }
        public DateTime? ConsentGivenAt { get; set; }
        public DateTime? ConsentRevokedAt { get; set; }
    }
}

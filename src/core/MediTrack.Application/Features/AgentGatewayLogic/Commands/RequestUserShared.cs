﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MongoDB.Driver;
using MediTrack.Application.Services;
using AgentGateway.Lib.Requests;
using Microsoft.EntityFrameworkCore;
using MediTrack.Ultils.Helpers;
using Amazon.Runtime;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class RequestUserShared : IRequest<BaseCommandResultWithData<UserSharedResponseDto>>
    {
        public string? RequestId { get; set; }
        public string? ResultCode { get; set; }
        public string? ResultDesc { get; set; }
        public string? TxnId { get; set; }
    }

    public class RequestUserSharedHandler(IDatabaseService databaseService) : IRequestHandler<RequestUserShared, BaseCommandResultWithData<UserSharedResponseDto>>
    {
        private readonly IDatabaseService _databaseService = databaseService;

        public async Task<BaseCommandResultWithData<UserSharedResponseDto>> Handle(
            RequestUserShared request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<UserSharedResponseDto>();

            var userShareInfo = await _databaseService.UserShareInfors.FirstOrDefaultAsync(x => x.TxnId == request.TxnId, cancellationToken);
            if (userShareInfo != null)
            {
                userShareInfo.result = request.ResultCode;
                userShareInfo.ConsentGivenAt = DateTimeHelper.GetCurrentLocalDateTime();
                _databaseService.UserShareInfors.Update(userShareInfo);
                await _databaseService.SaveChangesAsync(cancellationToken);
            }
            result.Set(true,"Thành Công");

            return result;
        }

      
        }
}

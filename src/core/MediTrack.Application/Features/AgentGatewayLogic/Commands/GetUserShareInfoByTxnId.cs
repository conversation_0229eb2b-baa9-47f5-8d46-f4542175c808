﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class GetUserShareInfoByTxnId : IRequest<BaseCommandResultWithData<UserShareInfoResponseDto>>
    {
        public string TxnId { get; set; } = string.Empty;
    }

    public class GetUserShareInfoByTxnIdHandler(IDatabaseService databaseService) : IRequestHandler<GetUserShareInfoByTxnId, BaseCommandResultWithData<UserShareInfoResponseDto>>
    {
        private readonly IDatabaseService _databaseService = databaseService;

        public async Task<BaseCommandResultWithData<UserShareInfoResponseDto>> Handle(
            GetUserShareInfoByTxnId request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<UserShareInfoResponseDto>();

            var userShareInfo = await _databaseService.UserShareInfors
                .FirstOrDefaultAsync(x => x.TxnId == request.TxnId, cancellationToken);

            if (userShareInfo == null)
            {
                result.Set(false, "User share info not found");
                return result;
            }

            var response = new UserShareInfoResponseDto
            {
                Id = userShareInfo.Id,
                TxnId = userShareInfo.TxnId,
                IndentityNo = userShareInfo.IndentityNo,
                Result = userShareInfo.result,
                ConsentGivenAt = userShareInfo.ConsentGivenAt,
                ConsentRevokedAt = userShareInfo.ConsentRevokedAt
            };

            result.Set(true, "Success", response);
            return result;
        }
    }
}

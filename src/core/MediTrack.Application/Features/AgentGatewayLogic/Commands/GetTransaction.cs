﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MediTrack.Application.Services;
using AgentGateway.Lib.Requests;
using System.Text;
using MongoDB.Driver;
using MediTrack.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class GetTransaction : IRequest<BaseCommandResultWithData<UserShareInfoDataResponseDto>>
    {
        public string? TransactionId { get; set; }
        public string? FaceImage { get; set; }
        public string? Session { get; set; } 
    }

    public class GetTransactionHandler(IAgentGatewayService agentGatewayService, IMongoService mongoService,
        IDatabaseService databaseService) : IRequestHandler<GetTransaction, BaseCommandResultWithData<UserShareInfoDataResponseDto>>
    {
        private readonly IAgentGatewayService _agentGatewayService = agentGatewayService;
        private readonly IMongoService _mongoService = mongoService;
        private readonly IDatabaseService _databaseService = databaseService;

        public async Task<BaseCommandResultWithData<UserShareInfoDataResponseDto>> Handle(
            GetTransaction request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<UserShareInfoDataResponseDto>();


            var checkSession = await _mongoService.VNEIDIdentityInfos.Find( x => x.Session == request.Session)
                .FirstOrDefaultAsync(cancellationToken);
            if (checkSession == null)
            {
                result.Set(false, "Session not found");
                return result;
            }


            var checkTransaction = await _databaseService.UserShareInfors.Where(x => x.TxnId == request.TransactionId).FirstOrDefaultAsync(cancellationToken);
            if (checkTransaction == null)
            {
                result.Set(false, "Transaction doesn't exist");
                return result;
            }

            //if (!checkTransaction.result.Equals("1"))
            //{
            //    result.Set(false, "User has not provided any informatio ");
            //    return result;
            //}

            var serviceRequest = new GetTransactionRequest
            {
                transactionId = request.TransactionId,
                faceImage = request.FaceImage
            };

            var (success, message, errorType, response) = await _agentGatewayService.GetTransaction(serviceRequest);
            if (!success)
            {
                result.Set(false, message, errorType);
                return result;
            }

            var decodeData = DecodePayloadWithoutValidation<UserShareInfoDataResponseDto>(response.UserInfo ?? string.Empty);
            decodeData.faceImage = checkSession.FirstFaceCropped;
            result.Set(success, message, decodeData, errorType);

            return result;
        }
        private static T DecodePayloadWithoutValidation<T>(string token)
        {
            var parts = token.Split('.');
            if (parts.Length != 3)
            {
                throw new ArgumentException("Invalid JWT format. Must have 3 parts.");
            }

            string payloadJson = Base64UrlDecode(parts[1]);
            payloadJson = Encoding.UTF8.GetString(Convert.FromBase64String(payloadJson));
            return System.Text.Json.JsonSerializer.Deserialize<T>(payloadJson)
                   ?? throw new InvalidOperationException("Deserialization returned null.");
        }
        private static string Base64UrlDecode(string input)
        {
            // Convert Base64Url string to Base64 string
            string base64 = input.Replace('-', '+').Replace('_', '/');
            switch (base64.Length % 4)
            {
                case 2: base64 += "=="; break;
                case 3: base64 += "="; break;
            }

            return base64;
        }


    }
}

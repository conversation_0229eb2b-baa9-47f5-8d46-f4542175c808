﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MediTrack.Application.Services;
using AgentGateway.Lib.Requests;
using MediTrack.Domain.Domain;
using MediTrack.Ultils.Helpers;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class InitTransaction : IRequest<BaseCommandResultWithData<InitTransactionResponseDto>>
    {
        public string CitizenPid { get; set; } = string.Empty;
    }

    public class InitTransactionHandler(IAgentGatewayService agentGatewayService, IDatabaseService databaseService) : IRequestHandler<InitTransaction, BaseCommandResultWithData<InitTransactionResponseDto>>
    {
        private readonly IAgentGatewayService _agentGatewayService = agentGatewayService;
        private readonly IDatabaseService _databaseService = databaseService;

        public async Task<BaseCommandResultWithData<InitTransactionResponseDto>> Handle(
            InitTransaction request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<InitTransactionResponseDto>();

            var serviceRequest = new InitTransactionRequest
            {
                citizenPid = request.CitizenPid
            };

            var (success, message, errorType, response) = await _agentGatewayService.InitTransaction(serviceRequest);
            if (success)
            {
                var txnId = response?.TxnId;
                if (!string.IsNullOrEmpty(txnId))
                {
                    var transaction = new UserShareInfo
                    {
                        Id = IdentityHelper.Guid(15),
                        TxnId = txnId,
                        IndentityNo = request.CitizenPid,
                        CreatedAt = DateTime.UtcNow,
                        result = "0",
                    };
                    await _databaseService.UserShareInfors.AddAsync(transaction, cancellationToken);
                    await _databaseService.SaveChangesAsync(cancellationToken);
                }
            }

            result.Set(success, message, response!, errorType);

            return result;
        }
    }
}

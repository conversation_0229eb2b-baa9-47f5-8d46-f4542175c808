﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class RevokedUserInfo : IRequest<BaseCommandResultWithData<object>>
    {
        public string? TxnId { get; set; }

        public class RevokedUserInfoHandler(IDatabaseService databaseService) : IRequestHandler<RevokedUserInfo, BaseCommandResultWithData<object>>
        {
            private readonly IDatabaseService _databaseService = databaseService;

            public async Task<BaseCommandResultWithData<object>> Handle(RevokedUserInfo request, CancellationToken cancellationToken)
            {
                var result = new BaseCommandResultWithData<object>();

                var userShareInfo = await _databaseService.UserShareInfors
                    .FirstOrDefaultAsync(x => x.TxnId == request.TxnId, cancellationToken);

                if (userShareInfo == null)
                {
                    result.Set(false, "transaction not found");
                    return result;
                }

                if (userShareInfo.result != "1")
                {
                    result.Set(false, "User has not provided any information");
                    return result;
                }

                var customer = await _databaseService.Customers
                    .FirstOrDefaultAsync(x => x.IdentityNo == userShareInfo.IndentityNo, cancellationToken);

                if (customer == null)
                {
                    result.Set(false, "Customer not found");
                    return result;
                }
                customer.IsDeleted = true;
                userShareInfo.ConsentRevokedAt = DateTime.UtcNow;
                userShareInfo.result = "3"; // 3 - Revoke consent
                _databaseService.UserShareInfors.Update(userShareInfo);
                _databaseService.Customers.Update(customer);
                await _databaseService.SaveChangesAsync(cancellationToken);

                // Logic to recall user information based on TxtId
                // This is a placeholder for the actual implementation
                result.Set(true, "User information recalled successfully");
                return result;
            }
        }
    }
}
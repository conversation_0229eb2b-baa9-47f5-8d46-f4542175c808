﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.AgentGatewayLogic.Commands
{
    public class SaveIdentity : SaveIdentityRequest, IRequest<BaseCommandResultWithData<VNEIDIdentityInfo>> { }

    public class SaveIdentityHandler(IMongoService mongoService) : IRequestHandler<SaveIdentity, BaseCommandResultWithData<VNEIDIdentityInfo>>
    {
        private readonly IMongoService _mongoService = mongoService;

        public async Task<BaseCommandResultWithData<VNEIDIdentityInfo>> Handle(
            SaveIdentity request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<VNEIDIdentityInfo>();

            if (request == null)
            {
                result.Success = false;
                result.Messages = "Invalid request";
                return result;
            }

            var newIdentity = new VNEIDIdentityInfo
            {
                Session = request.Session,
                Code = request.Code,
                FirstFace = request.FirstFace,
                LastFace = request.LastFace,
                FirstFaceCropped = request.FirstFaceCropped,
                LastFaceCropped = request.LastFaceCropped,
                Signature = request.Signature,
                FaceImages = request.FaceImages
            };

            await _mongoService.VNEIDIdentityInfos.InsertOneAsync(newIdentity, cancellationToken: cancellationToken);

            result.Success = true;
            result.Messages = "Success";
            result.Data = newIdentity;

            return result;
        }
    }
}

﻿using MediTrack.Application.Bases;
using MediTrack.Application.Features.AgentGatewayLogic.Dtos;
using MediatR;
using MongoDB.Driver;
using MediTrack.Application.Services;

namespace MediTrack.Application.Features.AgentGatewayLogic.Queries
{
    public class GetIdentityBySession : IRequest<BaseCommandResultWithData<GetIdentityRequest>>
    {
        public string Session { get; set; } = string.Empty;
    }

    public class GetIdentityBySessionHandler(IMongoService mongoService) : IRequestHandler<GetIdentityBySession, BaseCommandResultWithData<GetIdentityRequest>>
    {
        private readonly IMongoService _mongoService = mongoService;

        public async Task<BaseCommandResultWithData<GetIdentityRequest>> Handle(
            GetIdentityBySession request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<GetIdentityRequest>();



            var identity = await _mongoService.VNEIDIdentityInfos
                .Find(x => x.Session == request.Session)
                .Project(x => new GetIdentityRequest
                {
                    Id = x.Id,
                    Session = x.Session,
                    FaceImages = x.FaceImages
                })
                .FirstOrDefaultAsync(cancellationToken);

            if (identity != null)
            {
                result.Success = true;
                result.Messages = "Success";
                result.Data = identity;
            }
            else
            {
                result.Success = false;
                result.Messages = "Identity not found";
            }

            return result;
        }
    }
}

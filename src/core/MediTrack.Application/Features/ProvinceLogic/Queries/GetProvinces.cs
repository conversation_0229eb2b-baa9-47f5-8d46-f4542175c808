﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediatR;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.ProvinceLogic.Queries
{
    public class GetProvinces
         : IRequest<BaseCommandResultWithData<IEnumerable<ProvinceDto>>>
    {
    }

    public class GetProvincesHandler(IProvinceRepository provinceRepository) : IRequestHandler<GetProvinces, BaseCommandResultWithData<IEnumerable<ProvinceDto>>>
    {
        public async Task<BaseCommandResultWithData<IEnumerable<ProvinceDto>>> Handle(
            GetProvinces request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<ProvinceDto>>();
            var allProvinces = await provinceRepository.GetProvincedAsync(cancellationToken);

            result.Set(true, ProvinceConstant.Ok, allProvinces);

            return result;
        }
    }
}

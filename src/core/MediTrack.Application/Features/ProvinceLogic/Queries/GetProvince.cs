﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediatR;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.ProvinceLogic.Dtos;
using MediTrack.Application.Integrate;

namespace MediTrack.Application.Features.ProvinceLogic.Queries
{
    public class GetProvince
        : IRequest<BaseCommandResultWithData<ProvinceDto>>
    {
        public string Id { get; set; } = string.Empty;
        public bool? IsTwoLevelAddress { get; set; } = false;
    }

    public class GetProvinceHandler(IProvinceRepository provinceRepository)
        : IRequestHandler<GetProvince, BaseCommandResultWithData<ProvinceDto>>
    {
        public async Task<BaseCommandResultWithData<ProvinceDto>> Handle(
            GetProvince request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<ProvinceDto>();
            var allProvinces = await provinceRepository.GetProvincedAsync(cancellationToken);

            var province = allProvinces.FirstOrDefault(d => (request.IsTwoLevelAddress.GetValueOrDefault() ? d.NewId : d.Id) == request.Id);

            if (province == null)
            {
                result.Set(false, ProvinceConstant.NotFound);
            }
            else
            {
                var mappedProvince = new ProvinceDto
                {
                    Id = province.Id,
                    Name = province.Name,
                    OtherName = province.OtherName,
                    Level = province.Level,
                    NewId = province.NewId,
                    NewName = province.NewName,
                    NewOtherName = province.NewOtherName,
                    NewLevel = province.NewLevel,
                };
                result.Set(false, ProvinceConstant.Ok, mappedProvince);
            }

            return result;
        }
    }
}

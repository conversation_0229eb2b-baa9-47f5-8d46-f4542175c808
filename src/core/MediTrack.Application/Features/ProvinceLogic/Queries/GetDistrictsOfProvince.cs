﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.ProvinceLogic.Queries
{
    public class GetDistrictsOfProvince
        : IRequest<BaseCommandResultWithData<IEnumerable<District>>>
    {
        public string? Id { get; set; }
    }

    public class GetDistrictsOfProvinceHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IIdentityService identityService)
        : IRequestHandler<GetDistrictsOfProvince, BaseCommandResultWithData<IEnumerable<District>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResultWithData<IEnumerable<District>>> Handle(GetDistrictsOfProvince request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<District>>();

            var data = await databaseService.Districts
                .Where(x => x.ProvinceId == request.Id)
                .ToListAsync();
            result.Set(true, DistrictConstant.Ok, data);

            return result;
        }
    }
}

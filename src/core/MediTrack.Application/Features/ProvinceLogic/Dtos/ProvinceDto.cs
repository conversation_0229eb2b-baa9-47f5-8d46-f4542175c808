﻿namespace MediTrack.Application.Features.ProvinceLogic.Dtos
{
    public class ProvinceDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string OtherName { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
        public string NewId { get; set; } = string.Empty;
        public string NewName { get; set; } = string.Empty;
        public string NewOtherName { get; set; } = string.Empty;
        public string? NewLevel { get; set; } = string.Empty;
    }
}

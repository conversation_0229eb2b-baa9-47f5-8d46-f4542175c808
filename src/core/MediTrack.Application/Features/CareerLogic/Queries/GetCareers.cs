﻿using System.Text.Json;
using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.CareerLogic.Queries
{
    public class GetCareers : IRequest<BaseCommandResultWithData<List<GetCareerDto>>>
    {
    }

    public class GetGetCareersHandler(IDatabaseService databaseService, ICurrentHospitalService currentHospitalService,
    IHospitalMetadataRepository hospitalMetadataRepository) : IRequestHandler<GetCareers, BaseCommandResultWithData<List<GetCareerDto>>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        public async Task<BaseCommandResultWithData<List<GetCareerDto>>> Handle(
            GetCareers request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetCareerDto>>();
            try
            {
                if (!currentHospitalService.CurrentHospital.IsShowCareer)
                {
                    result.Set(true, CareerConstant.Ok, []);
                    return result;
                }
                
                var hospitalMetadatasCache = await hospitalMetadataRepository
                    .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "careers_list", cancellationToken: cancellationToken);

                if (hospitalMetadatasCache != null && !string.IsNullOrWhiteSpace(hospitalMetadatasCache.Value))
                {
                    var careerMetadata = JsonSerializer.Deserialize<List<GetCareerDto>>(hospitalMetadatasCache.Value);

                    if (careerMetadata != null)
                    {
                        result.Set(true, CareerConstant.Ok, careerMetadata);
                        return result;
                    }
                }
                var careers = await databaseService.Careers
                    .OrderBy(x => x.Order)
                    .ToListAsync(cancellationToken);
                result.Set(true, CareerConstant.Ok, careers.Adapt<List<GetCareerDto>>());
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get careers", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

﻿using System.Text.Json;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Constants;
using Serilog;

namespace MediTrack.Application.Features.CareerLogic.Queries
{
    public class GetBankCareers : IRequest<BaseCommandResultWithData<List<GetBankCareerDto>>>
    {
    }

    public class GetGetBankCareersHandler(ICurrentHospitalService currentHospitalService,
    ISystemMetadataRepository systemMetadataRepository) : IRequestHandler<GetBankCareers, BaseCommandResultWithData<List<GetBankCareerDto>>>
    {
        public async Task<BaseCommandResultWithData<List<GetBankCareerDto>>> Handle(
            GetBankCareers request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetBankCareerDto>>();
            try
            {
                if (!currentHospitalService.CurrentHospital.IsShowCareer)
                {
                    result.Set(true, CareerConstant.Ok, []);
                    return result;
                }

                var systemMetaData = await systemMetadataRepository.GetSystemMetadataByKeyAsync("career_4750_map_hdb", cancellationToken: cancellationToken);

                if (systemMetaData is null || string.IsNullOrEmpty(systemMetaData.Value))
                {
                    result.Set(false, CareerConstant.NotFound);
                    return result;
                }

                var bankCareer = JsonSerializer.Deserialize<List<GetBankCareerDto>>(systemMetaData.Value);
                result.Set(true, CareerConstant.Ok, bankCareer ?? []);
                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get careers", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

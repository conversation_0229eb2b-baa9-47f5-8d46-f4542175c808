﻿using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;
using MediTrack.Ultils.Helpers;
using Serilog;

namespace MediTrack.Application.Features.CareerLogic.Queries
{
    public class GetSocialCareers : IRequest<BaseCommandResultWithData<List<GetSocialCareerDto>>>
    {
    }

    public class GetSocialCareersHandler(ICurrentHospitalService currentHospitalService, ICachedService cachedService
        ) : IRequestHandler<GetSocialCareers, BaseCommandResultWithData<List<GetSocialCareerDto>>>
    {
        public async Task<BaseCommandResultWithData<List<GetSocialCareerDto>>> Handle(
            GetSocialCareers request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<GetSocialCareerDto>>();
            ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError;
            try
            {
                if (!currentHospitalService.CurrentHospital.IsShowSocialCareer)
                {
                    result.Set(true, CareerConstant.Ok, []);
                    return result;
                }

                List<GetSocialCareerDto>? careers = await cachedService.GetAsync<List<GetSocialCareerDto>>("SocialCareers" + currentHospitalService.CurrentHospital.HospitalId, cancellationToken);
                if (careers is null || careers.Count == 0)
                {
                    (_, _, errorType, careers) = await currentHospitalService.HisService!.GetSocialCareers();
                    if (careers.Count > 0)
                    {
                        await cachedService.SetAsync("SocialCareers" + currentHospitalService.CurrentHospital.HospitalId, careers, cancellationToken, GlobalHelper.GLOBAL_EXPIRE_SECOND);
                    }
                }
                result.Set(true, CareerConstant.Ok, careers.Adapt<List<GetSocialCareerDto>>());
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get careers", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message, errorType);
            }

            return result;
        }
    }
}

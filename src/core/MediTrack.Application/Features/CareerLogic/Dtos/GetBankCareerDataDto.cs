﻿using System.Text.Json.Serialization;

namespace MediTrack.Application.Features.CareerLogic.Dtos
{
    public class GetBankCareerDto
    {
        [JsonPropertyName("careerId")]
        public string CareerId { get; set; } = string.Empty;
        
        [JsonPropertyName("careerName")]
        public string CareerName { get; set; } = string.Empty;
        
        [JsonPropertyName("code4750")]
        public string Code4750 { get; set; } = string.Empty;
        
        [JsonPropertyName("position")]
        public List<GetBankCareerPositionDto> Position { get; set; } = new List<GetBankCareerPositionDto>();
    }
    public class GetBankCareerPositionDto
    {
        [JsonPropertyName("careerId")]
        public string CareerId { get; set; } = string.Empty;
        
        [JsonPropertyName("positionName")]
        public string PositionName { get; set; } = string.Empty;
        
        [JsonPropertyName("positionId")]
        public string PositionId { get; set; } = string.Empty;
    }
}

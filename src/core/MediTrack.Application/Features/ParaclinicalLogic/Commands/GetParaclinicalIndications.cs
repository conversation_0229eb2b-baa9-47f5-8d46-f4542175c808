using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Features.ParaclinicalLogic.Commands
{
    public class GetParaclinicalIndications : IRequest<BaseCommandResultWithData<IndicationSearchResponse>>
    {
        public string SearchText { get; set; } = string.Empty;
        /// <summary>
        /// SearchType: 1: Thẻ CCCD, 2: mã bệnh nhân, 3: mã hồ sơ
        /// </summary>
        public int SearchType { get; set; }
    }

    public class GetParaclinicalIndicationsHandler(
        ICurrentHospitalService currentHospitalService) : IRequestHandler<GetParaclinicalIndications, BaseCommandResultWithData<IndicationSearchResponse>>
    {
        private readonly IHisService hisService = currentHospitalService.HisService;

        public async Task<BaseCommandResultWithData<IndicationSearchResponse>> Handle(GetParaclinicalIndications request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IndicationSearchResponse>();

            var req = new IndicationSearchRequest
            {
                Code = request.SearchText,
                Option = request.SearchType
            };

            (bool resultRes, string message, ErrorTypeEnum errorType, IndicationSearchResponse data) = await hisService.GetParaclinicalIndications(req);

            if (!resultRes)
            {
                result.Set(resultRes, message, new IndicationSearchResponse(), errorType);
                return result;
            }

            result.Set(resultRes, message, data);
            return result;
        }
    }
}
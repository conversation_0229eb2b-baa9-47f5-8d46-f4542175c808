namespace MediTrack.Application.Features.ParaclinicalLogic.Dtos
{
    public class IndicationSearchRequest
    {
        public string Code { get; set; } = string.Empty;
        /// <summary>
        /// 1: Thẻ CCCD, 2: mã bệnh nhân, 3: mã bệnh án
        /// </summary>
        public int Option { get; set; }
    }

    public class IndicationSearchResponse
    {
        public string? HealthInsuranceNo { get; set; } = string.Empty;
        public string? PatientCode { get; set; } = string.Empty;
        public string? FullName { get; set; } = string.Empty;
        public string? Phone { get; set; } = string.Empty;
        public string? Address { get; set; } = string.Empty;
        public string? Sex { get; set; } = string.Empty;

        public DateTime? DateOfBirth { get; set; }
        public string? RefNo { get; set; }
        public List<Indication>? Indications { get; set; }
        public decimal? TotalAmount { get; set; }
    }

    public class Indication
    {
        public string? IndicationId { get; set; }
        public string? IndicationName { get; set; }
        public string? IndicationType { get; set; }
        public string? ClinicName { get; set; }
        public decimal? PaymentTotalAmount { get; set; }
        public decimal? TotalAmount { get; set; }
        public string? PaymentStatus { get; set; }
        public List<Service>? Services { get; set; }
    }
    
    public class Service
    {        
        public string? ServiceId { get; set; }
        public string? ServiceCode { get; set; }
        public string? ServiceName { get; set; }
        public long? Quantity { get; set; }
        public decimal? UnitPrice { get; set; }
        public decimal? PaymentAmount { get; set; }
        public decimal? Amount { get; set; }
    }
}
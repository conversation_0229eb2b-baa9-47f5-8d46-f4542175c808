using System.Text;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.DataStorageLogic;
using MediTrack.Application.Features.ParaclinicalLogic.Commands;
using MediTrack.Application.Features.ParaclinicalLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;
using Microsoft.EntityFrameworkCore;

namespace MediTrack.Application.Features.ParaclinicalLogic.Queries;

public class GetParaclinicalPaymentJson : IRequest<BaseCommandResultWithData<string>>
{
  public string? Number
  {
    get;
    set;
  }
}

public class GetParaclinicalIndicationsJsonHandler(
  IMediator mediator,
  IDatabaseService databaseService,
  ICachedService cachedService,
  ICurrentHospitalService currentHospitalService,
  IHospitalMetadataRepository hospitalMetadataRepository
) : IRequestHandler<GetParaclinicalPaymentJson, BaseCommandResultWithData<string>>
{
  public async Task<BaseCommandResultWithData<string>> Handle(GetParaclinicalPaymentJson request, CancellationToken cancellationToken)
  {
    var result = new BaseCommandResultWithData<string>();

    //1. Lấy thông tin phiếu từ Receipts
    var receipt = await databaseService.Receipts.FindAsync([request.Number], cancellationToken);

    if (receipt == null)
    {
      result.Set(false, ErrorConstant.NOT_FOUND_DATA, ReceiptConstant.NotFound);
      return result;
    }
    
    var hospitalId = currentHospitalService.CurrentHospital.HospitalId; 
    var hospital = DataStorageObject.GetHospital(hospitalId);
    if (hospital == null)
    {
      result.Set(false, ErrorConstant.NOT_FOUND_DATA, HospitalConstant.NotFound);
      return result;
    }   
    //1. Get info from GetParaclinicalIndications with search text and search type
    var indicationsData = await cachedService.GetAsync<IndicationSearchResponse>($"PARACLINICALINDICATIONS_{hospitalId}{receipt.RefNo}3", cancellationToken);

    if (indicationsData == null)
    {
      var indications = await mediator.Send(new GetParaclinicalIndications
      {
        SearchText = receipt.RefNo ?? string.Empty,
        SearchType = 3
      }, cancellationToken);

      if (!indications.Success)
      {
        result.Set(indications.Success, indications.Messages, string.Empty);
        return result;
      }

      //2. Get info from indications variable
      indicationsData = indications.Data;

      if (indicationsData == null)
      {
        result.Set(false, indications.Messages, string.Empty);
        return result;
      }
    }

    //3. Retrieve data from HospitalMetadatas table with current hospital id
    var hospitalMetadata =
      await hospitalMetadataRepository.GetHospitalMetadatasByHospitalAsync(hospitalId);

    if (hospitalMetadata.Count == 0)
    {
      result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu");
      return result;
    }

    var hospitalMetadataData =
      hospitalMetadata.FirstOrDefault(x => x.Code == HospitalMetadataConstant.Paraclinical);

    if (hospitalMetadataData == null)
    {
      result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy mẫu phiếu cận lâm sàng");
      return result;
    }

    // Lấy cặp json để in phiếu cận lâm sàng
    var paraclinicalIndications =
      hospitalMetadata.FirstOrDefault(x => x.Code == HospitalMetadataConstant.ParaclinicalIndications);

    if (paraclinicalIndications == null)
    {
      result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy cặp json để in phiếu cận lâm sàng");
      return result;
    }

    // Lấy cặp json hiển thị dịch vụ
    var paraclinicalService =
      hospitalMetadata.FirstOrDefault(x => x.Code == HospitalMetadataConstant.ParaclinicalService);

    if (paraclinicalService == null)
    {
      result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy cặp json hiển thị dịch vụ của phiếu cận lâm sàng");
      return result;
    }

    //4. Get dataPrint from hospitalMetadataData variable
    var dataPrint = hospitalMetadataData.Value ?? string.Empty;
    var paraclinicalIndicationsDataPrint = paraclinicalIndications.Value ?? string.Empty;
    var paraclinicalServicesDataPrint = paraclinicalService.Value ?? string.Empty;

    // Check template retrieved from hospitalMetadataData is null or empty
    if (string.IsNullOrEmpty(dataPrint) || string.IsNullOrEmpty(paraclinicalIndicationsDataPrint) || string.IsNullOrEmpty(paraclinicalServicesDataPrint))
    {
      result.Set(false, ErrorConstant.INVALID_DATA, "Không tìm thấy template phiếu cận lâm sàng");
      return result;
    }

    //5. Replace dataPrint with indicationsData
    dataPrint = dataPrint.Replace("[HospitalName]", hospital.Name ?? "-"); // Tên bệnh viện
    dataPrint = dataPrint.Replace("[RefNo]", receipt.RefNo ?? "-"); // Số tham chiếu
    dataPrint = dataPrint.Replace("[PaymentStatus]", "Thành công"); // Trạng thái thanh toán
    dataPrint = dataPrint.Replace("[HealthInsuranceNo]", indicationsData.HealthInsuranceNo ?? "-"); // Số BHYT
    dataPrint = dataPrint.Replace("[PatientCode]", indicationsData.PatientCode ?? "-"); // Mã bệnh nhân
    dataPrint = dataPrint.Replace("[CustomerName]", indicationsData.FullName ?? "-"); // Họ tên bệnh nhân
    dataPrint = dataPrint.Replace("[Phone]", indicationsData.Phone ?? "-"); // Số điện thoại
    dataPrint = dataPrint.Replace("[Address]", indicationsData.Address ?? "-"); // Địa chỉ
    dataPrint = dataPrint.Replace("[Gender]", indicationsData.Sex ?? "-"); // Giới tính
    dataPrint = dataPrint.Replace("[YearOfBirth]",
      indicationsData.DateOfBirth.GetValueOrDefault().ToString("yyyy") ?? "-"); // Ngày sinh
    dataPrint = dataPrint.Replace("[RefNo]", indicationsData.RefNo ?? "-"); // Số tham chiếu
    dataPrint = ReplaceIndications(dataPrint, paraclinicalIndicationsDataPrint, paraclinicalServicesDataPrint, indicationsData.Indications ?? new List<Indication>());
    dataPrint = dataPrint.Replace("[TotalAmount]",
      indicationsData.TotalAmount.GetValueOrDefault().ToString("N0") ?? "-"); // Tổng số tiền
    dataPrint = dataPrint.Replace("[DeviceId]", currentHospitalService.KioskId); // mã kiosk
    dataPrint = dataPrint.Replace("[PrintedAt]",
      DateTimeHelper.GetCurrentLocalDateTime().ToString("dd/MM/yyyy HH:mm")); // ngày in

    await cachedService.RemoveAsync($"PARACLINICALINDICATIONS_{hospitalId}{receipt.RefNo}3", cancellationToken);
    result.Set(true, ErrorConstant.SUCCESS, dataPrint);
    return result;
  }

  private static string ReplaceIndications(string dataPrint, string paraclinicalIndicationsDataPrint, string paraclinicalServicesDataPrint, List<Indication> indicationsData)
  {
    if (indicationsData.Count == 0)
    {
      return dataPrint;
    }

    var replacementContent = new StringBuilder();
    foreach (var indication in indicationsData)
    {
      var replacementIndication = paraclinicalIndicationsDataPrint;
      replacementIndication = replacementIndication.Replace("[ClinicName]", indication.ClinicName ?? "-");

      var replacementServiceContent = new StringBuilder("");

      if (indication.Services != null && indication.Services.Count > 0)
      {
        // Handle services
        foreach (var service in indication.Services)
        {
          var replacementService = paraclinicalServicesDataPrint;
          replacementService = replacementService.Replace("[ServiceName]", service.ServiceName ?? "-");
          replacementService = replacementService.Replace("[Quantity]", service.Quantity?.ToString("N0") ?? "-");
          replacementService = replacementService.Replace("[Amount]", service.Amount?.ToString("N0") ?? "-");

          replacementServiceContent.Append(replacementService).Append(',');
        }
      }
      // Replace service content instead of service marker: START_SERVICE_TEMPLATE in replacementIndication
      replacementIndication = replacementIndication.Replace(HospitalMetadataConstant.ParaclinicalService, replacementServiceContent.ToString());

      replacementContent.Append(replacementIndication);
    }

    return dataPrint.Replace(HospitalMetadataConstant.ParaclinicalIndications, replacementContent.ToString());
  }
}
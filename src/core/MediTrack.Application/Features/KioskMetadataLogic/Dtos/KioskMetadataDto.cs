using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.KioskMetadataLogic.Dtos
{
    public class KioskMetadataDto
    {
        public string? GroupType { get; set; }
        public string? GroupTypeDesc { get; set; }
        public string? Code { get; set; }
        public string? Title { get; set; }
        public string? Value { get; set; }
        public string? HospitalId { get; set; }
        public string? KioskId { get; set; }
    }
}
﻿using MediTrack.Domain.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.KioskMetadataLogic.Dtos
{
    public class HealthServiceKioskMetadataDto
    {
        public string? ClinicId { get; set; }
        public string? ClinicCode { get; set; }
        public string? ClinicName { get; set; }
        public bool? Active { get; set; }
        public List<HealthServiceKioskMetadata>? HealthServices { get; set; }
    }
    public class HealthServiceKioskMetadata
    {
        public string? HealthServiceName { get; set; }
        public string? HealthServiceId { get; set; }
        public string? HealthServiceCode { get; set; }
        public bool? Active { get; set; }
        public bool? RealTimeActive { get; set; }
        public List<OpenDateKioskMetadata>? OpenDates { get; set; }
    }
    public class OpenDateKioskMetadata
    {
        public bool? Active { get; set; }
        public string? DayOfWeek { get; set; }
        public List<OpenTimeKioskMetadata>? OpenTimes { get; set; }
    }

    public class OpenTimeKioskMetadata
    {
        public bool? Active { get; set; }
        public bool? IsDisable { get; set; }
        public string? TimeStart { get; set; }
        public string? TimeEnd { get; set; }
    }
}

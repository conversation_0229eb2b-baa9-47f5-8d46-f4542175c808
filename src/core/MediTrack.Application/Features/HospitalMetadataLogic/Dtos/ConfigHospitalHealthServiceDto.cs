﻿namespace MediTrack.Application.Features.HospitalMetadataLogic.Dtos
{
    public class ConfigHospitalHealthServiceDto
    {
        public string Id { get; set; } = string.Empty;
        public bool IsInsurance { get; set; }
        //không tự động check bảo hiểm (cho c<PERSON><PERSON> viện tự động check bảo hiểm)
        public bool IsIgnoreAutoCheckInsurance { get; set; }
        public bool IsDisabled { get; set; }
        public int MaxNumberMedical { get; set; } = 0;
        public bool IsDisabledNumberMedical { get; set; } = true;
        public bool IsDisabledAgeRange { get; set; } = true;
        public bool IsDisabledGender { get; set; } = true;
        public int? MinAge { get; set; }
        public int? MaxAge { get; set; }
        public int? Gender { get; set; } // 1: Nam, 2: Nữ
    }
}

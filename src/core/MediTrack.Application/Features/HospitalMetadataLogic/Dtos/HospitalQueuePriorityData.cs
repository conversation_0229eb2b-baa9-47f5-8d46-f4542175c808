﻿namespace MediTrack.Application.Features.HospitalMetadataLogic.Dtos
{
    public class HospitalQueuePriorityData
    {
        public List<QueuePriorityOption> QueuePriorityOptions { get; set; } = [];
    }
    public class QueuePriorityOption
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? GroupText { get; set; }
        public string? RequestQueueText { get; set; }
        public bool IsDefault { get; set; } = false;
        public List<string> QueuePriorityGroups { get; set; } = [];
    }
}

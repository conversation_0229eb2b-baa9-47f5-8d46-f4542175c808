﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Serilog;

namespace MediTrack.Application.Features.HospitalMetadataLogic.Queries
{
    public class GetHospitalMapRequest : IRequest<BaseCommandResultWithData<string>>
    {
    }

    public class GetHospitalMapRequestHandler(
        ICurrentHospitalService currentHospitalService,
        IHospitalMetadataRepository hospitalMetadataRepository,
        IHisServiceHelper hisServiceHelper,
        IDatabaseService dbService
    ) : IRequestHandler<GetHospitalMapRequest, BaseCommandResultWithData<string>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;

        public async Task<BaseCommandResultWithData<string>> Handle(
            GetHospitalMapRequest request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<string>();
            try
            {
                var kiosk = await hisServiceHelper.GetKiosk(currentHospitalService.KioskId, dbService, cancellationToken);
                if (kiosk == null)
                {
                    result.Set(false, HospitalConstant.NotFound);
                    return result;
                }
                if (kiosk.IsAllowShowHospitalMap.HasValue && !kiosk.IsAllowShowHospitalMap.Value)
                {
                    result.Set(false, "Không hiển thị bản đồ bệnh viện ở kiosk này.");
                    return result;
                }
                var hospitalMetadata = await hospitalMetadataRepository
                    .GetHospitalMetadataByKeyAsync(
                        currentHospitalService.CurrentHospital.HospitalId,
                        "hospital_map",
                        cancellationToken: cancellationToken);

                var url = hospitalMetadata?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(url))
                {
                    result.Set(false, "Bản đồ bệnh viện không được cấu hình.");
                    return result;
                }
                result.Set(true, HospitalConstant.Ok, url);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when getting hospital map", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

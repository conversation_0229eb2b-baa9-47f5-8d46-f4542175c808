﻿using System.Text.Json;
using Mapster;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.CareerLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace MediTrack.Application.Features.HospitalMetadataLogic.Queries
{
    public class GetHospitalMetadataRequest : IRequest<BaseCommandResultWithData<object>>
    {
        public string Key { get; set; } = string.Empty;
    }

    public class GetHospitalMetadataRequestHandler(ICurrentHospitalService currentHospitalService,
    IHospitalMetadataRepository hospitalMetadataRepository) : IRequestHandler<GetHospitalMetadataRequest, BaseCommandResultWithData<object>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        public async Task<BaseCommandResultWithData<object>> Handle(
            GetHospitalMetadataRequest request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<object>();
            try
            {
                var hospitalMetadata = await hospitalMetadataRepository
                    .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, request.Key, cancellationToken: cancellationToken);

                if (hospitalMetadata != null && !string.IsNullOrWhiteSpace(hospitalMetadata.Value))
                {
                    object metadata = JsonSerializer.Deserialize<object>(hospitalMetadata.Value ?? string.Empty)?? string.Empty;
                    result.Set(true, CareerConstant.Ok, metadata!);
                    return result;
                }
                result.Set(true, CareerConstant.Ok, hospitalMetadata!);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get hospitalMetadata", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Constants;
using MediTrack.Ultils.Helpers;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.HospitalMetadataLogic.Queries
{
    public class GetExameCategories : IRequest<BaseCommandResultWithData<List<HospitalMetadataExameCategory>>>
    {
    }

    public class GetExameCategoriesHandler(ICurrentHospitalService currentHospitalService,
    IHospitalMetadataRepository hospitalMetadataRepository) : IRequestHandler<GetExameCategories, BaseCommandResultWithData<List<HospitalMetadataExameCategory>>>
    {
        private readonly IHospitalMetadataRepository hospitalMetadataRepository = hospitalMetadataRepository;
        public async Task<BaseCommandResultWithData<List<HospitalMetadataExameCategory>>> Handle(
            GetExameCategories request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HospitalMetadataExameCategory>>();
            try
            {
                var hospitalMetadata = await hospitalMetadataRepository
                    .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "exame_categories", cancellationToken: cancellationToken);

                List<HospitalMetadataExameCategory> metadata = new ();
                if (hospitalMetadata != null && !string.IsNullOrWhiteSpace(hospitalMetadata.Value))
                {
                    metadata = JsonConvert.DeserializeObject<List<HospitalMetadataExameCategory>>(hospitalMetadata.Value ?? string.Empty) ?? new();
                }

                if (metadata.Count == 0)
                {
                    metadata =
                    [
                        new() { Id = string.Empty, Name = "Khám BHYT", IsInsurance = true },
                        new() { Id = string.Empty, Name = "Khám thường", IsInsurance = false }
                    ];
                }

                if (currentHospitalService.CurrentHospital.IsBlockInsuranceOnWeekend)
                {
                    if (DateTimeHelper.GetCurrentLocalDateTime().IsWeekend())
                    {
                        metadata.ForEach(e => e.IsDisabled = e.IsDisabled.GetValueOrDefault() || e.IsInsurance.GetValueOrDefault());
                    }
                }
                
                result.Set(true, HospitalConstant.Ok, metadata);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get hospitalMetadata", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

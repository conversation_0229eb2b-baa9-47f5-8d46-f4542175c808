﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.HospitalMetadataLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Domain.Constants;
using Newtonsoft.Json;
using Serilog;

namespace MediTrack.Application.Features.HospitalMetadataLogic.Queries
{
    public class GetQueuePriorityDataRequest : IRequest<BaseCommandResultWithData<HospitalQueuePriorityData>>
    {
    }

    public class GetQueuePriorityDataRequestHandler(ICurrentHospitalService currentHospitalService,
    IHospitalMetadataRepository hospitalMetadataRepository) : IRequestHandler<GetQueuePriorityDataRequest, BaseCommandResultWithData<HospitalQueuePriorityData>>
    {
        public async Task<BaseCommandResultWithData<HospitalQueuePriorityData>> Handle(
            GetQueuePriorityDataRequest request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<HospitalQueuePriorityData>();
            try
            {
                HospitalQueuePriorityData response = new();

                var queuePriorityOptions = await hospitalMetadataRepository
                    .GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "queue_priority_options", cancellationToken: cancellationToken);

                if (queuePriorityOptions != null && !string.IsNullOrWhiteSpace(queuePriorityOptions.Value))
                {
                    response.QueuePriorityOptions = JsonConvert.DeserializeObject<List<QueuePriorityOption>>(queuePriorityOptions.Value ?? string.Empty) ?? new();
                }

                if (response.QueuePriorityOptions.Count == 0)
                {
                    response.QueuePriorityOptions =
                    [
                        new()
                        {
                            Id = "",
                            Name = "Có",
                            GroupText = "Tôi thuộc nhóm ưu tiên",
                            RequestQueueText = "Lấy số ưu tiên",
                            IsDefault = true,
                            QueuePriorityGroups =
                            [
                                "Dưới 06 tuổi",
                                "Trên 75 tuổi",
                                "Có thai",
                                "Có công cách mạng",
                                "Khuyết tật"
                            ]
                        },
                        new()
                        {
                            Id = "",
                            Name = "Không",
                            GroupText = "Tôi khám thường",
                            RequestQueueText = "Lấy số nhanh",
                            IsDefault = false,
                            QueuePriorityGroups = []
                        }
                    ];
                }

                result.Set(true, HospitalConstant.Ok, response);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "{LogPrefix} Error when get hospitalMetadata", currentHospitalService.LogPrefix);
                result.Set(false, ex.Message);
            }

            return result;
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.Runtime.Internal;
using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Application.Features.SearchLogic.Dtos;
using MediTrack.Application.Integrate;
using MediTrack.Application.Repositories;
using MediTrack.Application.Services;
using MediTrack.Domain.Domain;
using Newtonsoft.Json;

namespace MediTrack.Application.Features.SearchLogic.Queries
{
    public class SearchHealthService : IRequest<BaseCommandResultWithData<List<HealthServiceSearchDto>>>
    {
    }

    public class SearchHealthServiceHandler(
            IHospitalMetadataRepository hospitalMetadataRepository,
            ICurrentHospitalService currentHospitalService)
        : IRequestHandler<SearchHealthService, BaseCommandResultWithData<List<HealthServiceSearchDto>>>
    {
        public async Task<BaseCommandResultWithData<List<HealthServiceSearchDto>>> Handle(SearchHealthService request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<List<HealthServiceSearchDto>>();
            {
                var metaData = await hospitalMetadataRepository.GetHospitalMetadataByKeyAsync(currentHospitalService.CurrentHospital.HospitalId, "health_services_search", cancellationToken: cancellationToken);

                if (metaData is null || string.IsNullOrEmpty(metaData.Value))
                {
                    (bool IsSuccess, string message,_, List<HealthServiceSearchDto> data) = await currentHospitalService.HisService.GetSearchListHealthServices();
                    result.Set(IsSuccess, message, data);
                    return result;
                }

                var healthServices = JsonConvert.DeserializeObject<List<HealthServiceSearchDto>>(metaData.Value);
                if (healthServices is null || healthServices.Count == 0)
                {
                    result.Set(false, "No health services found for the current hospital.", []);
                    return result;
                }
                result.Set(true, "Health services found.", healthServices);
                return result;
            }
        }
    }
}
﻿using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediatR;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.DistrictLogic.Dtos;

namespace MediTrack.Application.Features.DistrictLogic.Queries
{
    public class GetDistrict
       : IRequest<BaseCommandResultWithData<DistrictDto>>
    {
        public string Id { get; set; } = string.Empty;
    }

    public class GetDistrictHandler(IDistrictRepository districtRepository)
        : IRequestHandler<GetDistrict, BaseCommandResultWithData<DistrictDto>>
    {
        public async Task<BaseCommandResultWithData<DistrictDto>> Handle(
            GetDistrict request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<DistrictDto>();
            var allDistricts = await districtRepository.GetDistrictByParentIdAsync(null, cancellationToken);

            var district = allDistricts.FirstOrDefault(d => d.Id == request.Id);

            if (district == null)
            {
                result.Set(false, DistrictConstant.NotFound);
            }
            else
            {
                var mappedDistrict = new DistrictDto
                {
                    Id = district.Id,
                    Name = district.Name,
                    OtherName = district.OtherName,
                    ProvinceId = district.ProvinceId,
                    Level = district.Level
                };
                result.Set(true, DistrictConstant.Ok, mappedDistrict);
            }

            return result;
        }
    }
}

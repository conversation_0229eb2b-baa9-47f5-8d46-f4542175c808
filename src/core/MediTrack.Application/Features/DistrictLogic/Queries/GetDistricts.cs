﻿using MediatR;
using MediTrack.Application.Bases;
using MediTrack.Domain.Constants;
using MediTrack.Application.Repositories;
using MediTrack.Application.Features.DistrictLogic.Dtos;

namespace MediTrack.Application.Features.DistrictLogic.Queries
{
    public class GetDistricts
         : IRequest<BaseCommandResultWithData<IEnumerable<DistrictDto>>>
    {
        public string? Keywords { get; set; }
    }

    public class GetDistrictsHandler(IDistrictRepository districtRepository)
        : IRequestHandler<GetDistricts, BaseCommandResultWithData<IEnumerable<DistrictDto>>>
    {
        public async Task<BaseCommandResultWithData<IEnumerable<DistrictDto>>> Handle(
            GetDistricts request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<DistrictDto>>();
            var allDistricts = await districtRepository.GetDistrictByParentIdAsync(null, cancellationToken);
            var filteredDistricts = allDistricts
                .Where(x => string.IsNullOrEmpty(request.Keywords) ||
                            (x.Name != null && x.Name.Contains(request.Keywords)))
                .Select(x => new DistrictDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    OtherName = x.OtherName,
                    ProvinceId = x.ProvinceId,
                    Level = x.Level
                })
                .ToList();
            result.Set(true, DistrictConstant.Ok, filteredDistricts);

            return result;
        }
    }
}

﻿using MediTrack.Application.Bases;
using MediTrack.Application.Services;
using MediTrack.Domain.Constants;
using MediTrack.Domain.Domain;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Features.DistrictLogic.Queries
{
    public class GetWardsOfDistrict
        : IRequest<BaseCommandResultWithData<IEnumerable<Ward>>>
    {
        public string? Id { get; set; }
    }

    public class GetWardsOfWardHandler(ICurrentUserService currentUserService,
        IDatabaseService databaseService,
        IIdentityService identityService)
        : IRequestHandler<GetWardsOfDistrict, BaseCommandResultWithData<IEnumerable<Ward>>>
    {
        private readonly ICurrentUserService currentUserService = currentUserService;
        private readonly IDatabaseService databaseService = databaseService;
        private readonly IIdentityService identityService = identityService;

        public async Task<BaseCommandResultWithData<IEnumerable<Ward>>> Handle(GetWardsOfDistrict request, CancellationToken cancellationToken)
        {
            var result = new BaseCommandResultWithData<IEnumerable<Ward>>();

            var data = await databaseService.Wards
                .Where(x => x.DistrictId == request.Id)
                .ToListAsync();
            result.Set(true, WardConstant.Ok, data);

            return result;
        }
    }
}

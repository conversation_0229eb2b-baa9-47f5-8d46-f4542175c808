﻿namespace MediTrack.Application.Features.DistrictLogic.Dtos
{
    public class DistrictDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? OtherName { get; set; } = string.Empty;
        public string? ProvinceId { get; set; } = string.Empty;
        public string? Level { get; set; } = string.Empty;
    }
}

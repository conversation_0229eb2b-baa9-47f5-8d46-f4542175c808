﻿namespace MediTrack.Application.Configs
{
    public class HdBankConfig
    {
        public string ConfigName = "HDBANK_";
        public string Url
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.Url))
                ?? throw new NullReferenceException(nameof(this.Url));
        }

        public string UrlUploadFileHost
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlUploadFileHost))
                ?? throw new NullReferenceException(nameof(this.UrlUploadFileHost));
        }

        public string UrlCheckBankAccount
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlCheckBankAccount))
                ?? throw new NullReferenceException(nameof(this.UrlCheckBankAccount));
        }

        public string UrlUploadFilePath
        {
            get => this.UrlUploadFileHost + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlUploadFilePath))
                ?? throw new NullReferenceException(nameof(this.UrlUploadFilePath));
        }

        public string UploadFileApiKey
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UploadFileApiKey))
                ?? throw new NullReferenceException(nameof(this.UploadFileApiKey));
        }

        public string UrlSendOtp
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlSendOtp))
                ?? throw new NullReferenceException(nameof(this.UrlSendOtp));
        }

        public string UrlConfirmOtp
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlConfirmOtp))
                ?? throw new NullReferenceException(nameof(this.UrlConfirmOtp));
        }

        public string UrlCheckStatusOnBoarding
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlCheckStatusOnBoarding))
                ?? throw new NullReferenceException(nameof(this.UrlCheckStatusOnBoarding));
        }
        
        public string PartnerUploadFileCode
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlCheckStatusOnBoarding))
                ?? throw new NullReferenceException(nameof(this.PartnerUploadFileCode));
        }

        public string UrlUpdateCustomerInfo
        {
            get => this.Url + Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UrlUpdateCustomerInfo))
                ?? throw new NullReferenceException(nameof(this.UrlUpdateCustomerInfo));
        }

        public static string PfxFilePath
        {
            get => Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "cert", "certificate.pfx");
        }

        public string PassPfx
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.PassPfx))
               ?? throw new NullReferenceException(nameof(this.PassPfx));
        }

        public static string PemFilePath
        {
            get => Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "pem", "output.pem");
        }

        public string SecretKey
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.SecretKey))
               ?? throw new NullReferenceException(nameof(this.SecretKey));
        }

        public string SecretKeyUploadFile
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.SecretKeyUploadFile))
               ?? throw new NullReferenceException(nameof(this.SecretKeyUploadFile));
        }

        public string PartnerId
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.PartnerId))
               ?? throw new NullReferenceException(nameof(this.PartnerId));
        }

        public string ApiKey
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.ApiKey))
               ?? throw new NullReferenceException(nameof(this.ApiKey));
        }
        public string ChannelId
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.ChannelId))
               ?? throw new NullReferenceException(nameof(this.ChannelId));
        }

        public string AppVersion
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.AppVersion))
               ?? throw new NullReferenceException(nameof(this.AppVersion));
        }

        public string PartnerUserId
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.PartnerUserId))
               ?? throw new NullReferenceException(nameof(this.PartnerUserId));
        }

        public string UploadCategoryCode
        {
            get => Environment.GetEnvironmentVariable(this.ConfigName + nameof(this.UploadCategoryCode))
               ?? throw new NullReferenceException(nameof(this.UploadCategoryCode));
        }
    }
}
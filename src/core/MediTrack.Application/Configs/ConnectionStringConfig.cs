﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Configs
{
    public class ConnectionStringConfig
    {
        public static string ConfigName => "ConnectionStrings";
        public string PersistenceDatabase { get; set; } = string.Empty;
        public string HangfireDatabase { get; set; } = string.Empty;
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MediTrack.Application.Configs
{
    public class MailConfig
    {
        public static string ConfigName => "Mail";
        public string HostMail { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
        public string DefaultCCs { get; set; } = string.Empty;
        public string DefaultBCCs { get; set; } = string.Empty;
        public string Credential { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string MailTemplate { get; set; } = string.Empty;
    }
}

﻿using MediTrack.Domain.Enums;

namespace MediTrack.Application.Bases
{
    public class BaseCommandResultWithData<T> : BaseCommandResult
    {
        public BaseCommandResultWithData() { }
        public BaseCommandResultWithData(bool success, string message, T data, ErrorTypeEnum errorType = ErrorTypeEnum.NoError)
        {
            Success = success;
            Messages = message;
            Data = data;
            ErrorType = success ? ErrorTypeEnum.NoError : errorType;
        }
        
        public void Set(bool success, string message, T data, ErrorTypeEnum errorType = ErrorTypeEnum.NoError)
        {
            Success = success;
            Messages = message;
            Data = data;
            ErrorType = success ? ErrorTypeEnum.NoError : errorType;
        }

        public T? Data { get; set; }
    }
}

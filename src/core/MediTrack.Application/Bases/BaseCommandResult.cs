﻿using MediTrack.Domain.Enums;

namespace MediTrack.Application.Bases
{
    public class BaseCommandResult
    {
        public bool Success { get; set; }
        public string Messages { get; set; } = string.Empty;
        public ErrorTypeEnum ErrorType { get; set; } = ErrorTypeEnum.NoError;
        public IEnumerable<BaseCommandError> Errors { get; set; } = [];
        public void Set(bool success, string message, ErrorTypeEnum errorType = ErrorTypeEnum.NoError)
        {
            Success = success;
            Messages = message;
            ErrorType = success ? ErrorTypeEnum.NoError : errorType;
        }
    }
}

﻿using MediTrack.Domain.Constants;
using MediTrack.Domain.Enums;

namespace MediTrack.Application.Bases
{
    public class BaseResponseModel<T> where T : class
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public ErrorTypeEnum ErrorType { get; set; } = ErrorTypeEnum.NoError;
    }

    public static class ResponseConverter
    {
        public static BaseResponseModel<T> Convert<T>(bool success, string message, T? data, ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError)
        where T : class
        {
            var result = new BaseResponseModel<T>
            {
                Data = data
            };
            if (success)
            {
                result.Code = "00";
                result.Message = message;
                result.ErrorType = ErrorTypeEnum.NoError;
            }
            else
            {
                result.Code = ErrorConstant.ConvertDumy(message);
                result.Message = message;
                result.ErrorType = errorType;
            }

            return result;
        }

        public static BaseResponseModelPaging<TResult> ConvertPaging<TResult, T>(bool success, string message, T? data, ErrorTypeEnum errorType = ErrorTypeEnum.MediPayError)
        where TResult : class
        where T : BasePaging<TResult>
        {
            var result = new BaseResponseModelPaging<TResult>
            {
                Data = data?.Items ?? [],
                Count = data?.TotalItem ?? 0,
                Page = data?.TotalPage ?? 0,
                PageSize = data?.PageSize ?? 0
            };

            if (success)
            {
                result.Code = "00";
                result.Message = message;
                result.ErrorType = ErrorTypeEnum.NoError;
            }
            else
            {
                result.Code = ErrorConstant.ConvertDumy(message);
                result.Message = message;
                result.ErrorType = errorType;
            }

            return result;
        }
    }
}

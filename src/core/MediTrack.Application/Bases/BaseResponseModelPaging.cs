﻿using MediTrack.Domain.Enums;

namespace MediTrack.Application.Bases
{
    public class BaseResponseModelPaging<T>
    {
        public string Code { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public IEnumerable<T>? Data { get; set; }
        public ErrorTypeEnum ErrorType { get; set; } = ErrorTypeEnum.NoError;
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int Count { get; set; }
    }
}
